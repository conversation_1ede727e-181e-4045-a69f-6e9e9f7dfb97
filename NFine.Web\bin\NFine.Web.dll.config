﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  有关如何配置 ASP.NET 应用程序的详细信息，请访问
  http://go.microsoft.com/fwlink/?LinkId=169433
  -->
<configuration>

	<configSections>
		<!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
		<section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
	</configSections>
	<connectionStrings configSource="Configs\database.config" />
	<appSettings configSource="Configs\system.config" />
	<system.web>

		<httpRuntime requestValidationMode="2.0" targetFramework="4.5" />
		<compilation debug="true" targetFramework="4.5" />
		<pages validateRequest="false">
			<namespaces>
				<add namespace="System.Web.Helpers" />
				<add namespace="System.Web.Mvc" />
				<add namespace="System.Web.Mvc.Ajax" />
				<add namespace="System.Web.Mvc.Html" />
				<add namespace="System.Web.Routing" />
				<add namespace="System.Web.WebPages" />
			</namespaces>
		</pages>
	</system.web>
	<system.webServer>
  
		<validation validateIntegratedModeConfiguration="false" />
		<handlers>
			<remove name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" />
			<remove name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" />
			<remove name="ExtensionlessUrlHandler-Integrated-4.0" />
			<add name="ExtensionlessUrlHandler-ISAPI-4.0_32bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness32" responseBufferLimit="0" />
			<add name="ExtensionlessUrlHandler-ISAPI-4.0_64bit" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" modules="IsapiModule" scriptProcessor="%windir%\Microsoft.NET\Framework64\v4.0.30319\aspnet_isapi.dll" preCondition="classicMode,runtimeVersionv4.0,bitness64" responseBufferLimit="0" />
			<add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="GET,HEAD,POST,DEBUG,PUT,DELETE,PATCH,OPTIONS" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" />
		</handlers>
    <httpProtocol>
      <customHeaders>
        <add name="Access-Control-Allow-Origin" value="*" />
        <add name="Access-Control-Allow-Headers" value="*" />
        <add name="Access-Control-Allow-Methods" value="GET, POST,PUT,DELETE,OPTIONS" />
      </customHeaders>
    </httpProtocol>
	</system.webServer>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="EntityFramework" publicKeyToken="b77a5c561934e089" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234" />
			</dependentAssembly>
			<dependentAssembly>
				<assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
			</dependentAssembly>
      
		</assemblyBinding>
	</runtime>

	<system.serviceModel>
		<bindings>
			<basicHttpBinding>
				<binding name="BasicHttpBinding_IWorkFlow" />
				<binding name="BasicHttpBinding_ICommunalService" />
				<binding name="BasicHttpBinding_ITradeService" />
				<binding name="BasicHttpBinding_IPosService" />
				<binding name="BasicHttpBinding_ISaasPosService" maxReceivedMessageSize="2147483647" maxBufferSize="2147483647" maxBufferPoolSize="524288"/>

			</basicHttpBinding>
		</bindings>
		<client>
			<endpoint address="http://localhost:9005/WorkFlow" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IWorkFlow" contract="WorkFlowService.IWorkFlow" name="BasicHttpBinding_IWorkFlow" />
			<endpoint address="http://************:9106/Communal/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ICommunalService" contract="CommunalService.ICommunalService" name="BasicHttpBinding_ICommunalService" />
			<endpoint address="http://localhost:9107/TradeService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ITradeService" contract="TradService.ITradeService" name="BasicHttpBinding_ITradeService" />
			<endpoint address="http://localhost:9108/PosService/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_IPosService" contract="PosService.IPos" name="BasicHttpBinding_IPosService" />
			<endpoint address="http://localhost:9199/SaasPos/" binding="basicHttpBinding" bindingConfiguration="BasicHttpBinding_ISaasPosService" contract="SaasPosService.ISaasPos" name="BasicHttpBinding_ISaasPosService" />
		</client>
	</system.serviceModel>
</configuration>