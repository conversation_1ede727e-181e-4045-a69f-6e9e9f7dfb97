﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods
{
    /// <summary>
    /// 商品查询——视图
    /// </summary>
    public class goods_search_data 
    {
        /// <summary>
        /// 商品编号
        /// </summary>
        public string pid { get; set; }
        /// <summary>
        /// 当前项目对外固定销售商品（当前项目下可能存在多分销商品
        /// </summary>
        public string g_id { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string name { get; set; } 
        /// <summary>
        /// 更新时间（子集发生变化会刷新此字段，防止子数据刷新排序在底部），新增项目默认是创建时间
        /// </summary>
        public DateTime? updatetime { get; set; }
        public int g_count { get; set; }
        /// <summary>
        /// 商品总库存
        /// </summary>
        public int stocknums { get; set; }
        /// <summary>
        /// 商品图片
        /// </summary>
        public string photo { get; set; }
        /// <summary>
        /// 商品销售量
        /// </summary>
        public int salesnums { get; set; }
        /// <summary>
        /// 商品展示价格
        /// </summary>
        public double sprice { get; set; }
        /// <summary>
        /// 商品展示价格
        /// </summary>
        public double yprice { get; set; }
        /// <summary>
        /// 状态
        /// </summary>
        public int state { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int sort { get; set; }
    }
}

