﻿using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods
{
   public class goods_search_params
   {
       #region Model
        private string _name;
        private string _btime;
        private string _etime;
        private int? _price1 = -1;
        private int? _price2 = -1;
        private int? _stock1 = -1;
        private int? _stock2 = -1;
        private int? _sale1 = -1;
        private int? _sale2 = -1;

        /// <summary>
        /// 开始时间
        /// </summary>
        public string BTime
        {
            set { _btime = value; }
            get { return _btime; }
        }
        /// <summary>
        /// 结束时间
        /// </summary>
        public string ETime
        {
            set { _etime = value; }
            get { return _etime; }
        }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string name
        {
            set { _name = value; }
            get { return _name; }
        }
       /// <summary>
       /// 最低价
       /// </summary>
        public int? price1
        {
            set { _price1 = value; }
            get { return _price1; }
        }
       /// <summary>
       /// 最高价
       /// </summary>
        public int? price2
        {
            set { _price2 = value; }
            get { return _price2; }
        }
       /// <summary>
       /// 最小库存
       /// </summary>
        public int? stock1
        {
            set { _stock1 = value; }
            get { return _stock1; }
        }
       /// <summary>
       /// 最大库存
       /// </summary>
        public int? stock2
        {
            set { _stock2 = value; }
            get { return _stock2; }
        }
       /// <summary>
       /// 最低销售量
       /// </summary>
        public int? sale1
        {
            set { _sale1 = value; }
            get { return _sale1; }
        }
       /// <summary>
        /// 最高销售量
       /// </summary>
        public int? sale2
        {
            set { _sale2 = value; }
            get { return _sale2; }
        }
       #endregion
   }
}
