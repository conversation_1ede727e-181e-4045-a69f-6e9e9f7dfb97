﻿/**  版本信息模板在安装目录下，可自行修改。
* NAreaInfo.cs
*
* 功 能： N/A
* 类 名： NAreaInfo
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-09-16 15:14:40   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.MemberPurchase.baseTable
{
	/// <summary>
	/// NAreaInfo:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class NAreaInfo
	{
		public NAreaInfo()
		{}
		#region Model
		private int _areano;
		private string _areaname;
		private string _areaexplain;
		private string _areafield="";
		/// <summary>
		/// 
		/// </summary>
		public int AreaNo
		{
			set{ _areano=value;}
			get{return _areano;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string AreaName
		{
			set{ _areaname=value;}
			get{return _areaname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string AreaExplain
		{
			set{ _areaexplain=value;}
			get{return _areaexplain;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string AreaField
		{
			set{ _areafield=value;}
			get{return _areafield;}
		}
		#endregion Model

	}
}

