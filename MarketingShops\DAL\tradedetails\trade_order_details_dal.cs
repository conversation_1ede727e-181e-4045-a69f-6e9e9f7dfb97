﻿using MarketingShops.Model;
using MarketingShops.Model.trade.baseTable;
using MarketingShops.Model.trade.tradedetails;
using NFine.Code;
using System.Collections.Generic;

namespace MarketingShops.DAL.tradedetails
{
    /// <summary>
    /// 商品销售明细
    /// </summary>
    class trade_order_details_dal
    {
        #region 查询（分页）
        /// <summary>
        /// 系统查询
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">页码信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(string seltj, Pagination Page)
        {
            string sql = @"SELECT {0} FROM dbo.buy_bill_detailed where" + seltj;
            return DbHelp_MarketingShops.paging<buy_bill_detailed>(sql, @"
       [g_name]
       ,[skuname]
       ,[integral]
       ,[orderid]
       ,[buy_num]
       ,[recharge]
       ,[cash]
       ,[weight]
       ,[sku_id]
", Page.sidx, Page.page, Page.rows);
        }
        #endregion


        /// <summary>
        /// 获取订单详情
        /// </summary>
        /// <param name="orderid">订单编号</param>
        /// <returns></returns>
        public object GetTradeInfo(string orderid)
        {
            MSDetails bill = new Model.trade.tradedetails.MSDetails();
            string sql = @"SELECT [photo], [orderid], [g_name], [skuname], [sku_id], [weight], [buy_num], [integral], [recharge], [cash] FROM  buy_bill_detailed where orderid=@orderid";
            bill.goodsList = DbHelp_MarketingShops.ado.GetDataList<buy_bill_detailed>(sql, new { orderid = orderid });
            return bill;
        }


        #region 导出商品销售明细
        /// <summary>
        /// 获取导出数据结构
        /// </summary>
        /// <param name="sys">导出条件</param>
        /// <returns></returns>
        public List<Exportbilldetailed> ExportTradeList(string seltj)
        {
            string sql = @"SELECT   [orderid], [g_name], [skuname], [sku_id], [weight], [buy_num], [integral], [recharge], [cash] FROM  buy_bill_detailed where" + seltj + " order by orderid desc";
            List<Exportbilldetailed> list = DbHelp_MarketingShops.ado.GetDataList<Exportbilldetailed>(sql);
            return list;
        }
        #endregion
    }
}
