﻿using MarketingShops.BLL.trade;
using MarketingShops.Model.trade;
using MarketingShops.Model.trade.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Miniapp.trade
{
   public class trade_mini_dal
    {
        /// <summary>
        /// 小程序同步数据
        /// </summary>
        /// <param name="orderid">订单编号</param>
        /// <returns></returns>
       public static TradeInfo GetMiniTrandInfo(string orderid)
        {
            TradeInfo bill = new Model.trade.TradeInfo();
            string sql = "select * from buy_bill_core where orderid=@orderid";
            bill.bill_core = DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_core>(sql, new { orderid = orderid });
            trade_contacts_bll contacts_bll = new trade_contacts_bll();
            bill.address = contacts_bll.GetContactsInfo(orderid);
            sql = "select [detailedno],[g_id],[g_name],[sku_id] ,[skuname],[photo],[orderid],[buy_num],[showprice],[cash],[integral],[recharge],[weight],[payMethodCou],[stateNo] from buy_bill_detailed where orderid=@orderid";
            bill.goodsList = DbHelp_MarketingShops.ado.GetDataList<GoodListInfo>(sql, new { orderid = orderid });
            if (bill.goodsList.Count > 0)
            {
                for (int i = 0; i < bill.goodsList.Count; i++)
                {
                    sql = "select [total_cash],[total_integral],[total_recharge],[total_weight] from buy_bill_detailed_cost where detailedno=@detailedno";
                    bill.goodsList[i].cost = DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_detailed_cost>(sql, new { detailedno = bill.goodsList[i].detailedno });
                    sql = "select [buy_num],[cash],[integral],[recharge] ,[txt] from buy_bill_detailed_paymentMethod where detailedno=@detailedno";
                    bill.goodsList[i].PaymentMethod = DbHelp_MarketingShops.ado.GetDataList<buy_bill_detailed_paymentMethod>(sql, new { detailedno = bill.goodsList[i].detailedno });
                }
            }
            sql = "select b.[mailName] as company,a.[mail_number],c.[deliverTime] from buy_bill_mail a join mail_freight_project b on a.company=b.freightno join buy_bill_secondary c on a.orderid=c.orderid where a.orderid=@orderid";
            buy_bill_mail mail = DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_mail>(sql, new { orderid = orderid });
            object bill_mail = null;
            if (mail != null)
                bill_mail = new
                {
                    company = mail.company,
                    mail_number = mail.mail_number,
                    inTime = mail.deliverTime
                };
            else
                bill_mail = new
                {
                    company = "",
                    mail_number = "",
                    inTime = ""
                };
            bill.mail = bill_mail;
            return bill;
        }
    }
}
