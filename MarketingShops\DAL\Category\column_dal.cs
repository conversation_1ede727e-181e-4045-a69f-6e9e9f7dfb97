﻿using MarketingShops.Model;
using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Category
{
    /*
     栏目表数据库操作
     *  增删改操作均记录在操作表log_marketing_edit
     */
    public class column_dal
    {
        /// <summary>
        /// 获取栏目信息
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">分页信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(string tcp_name, Pagination Page)
        {
            string variable = "1=1";
            if (!string.IsNullOrEmpty(tcp_name))
                variable += "and [tcc_name]='" + tcp_name.Trim() + " '";
            string sql = "select {0} from dbo.template_category_column where " + variable;

            return DbHelp_MarketingShops.paging<column>(sql, "*", Page.sidx, Page.page, Page.rows);
        }
       /// <summary>
       /// 获取栏目详情
       /// </summary>
       /// <param name="id">栏目自增id</param>
       /// <returns></returns>
        public column GetColumnInfo(int id)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle<column>("select * from template_category_column where tcc_id=" + id + " ");
        }
        /// <summary>
        /// 修改栏目信息
        /// </summary>
        /// <param name="pro">栏目信息</param>
        /// <returns></returns>
        public int UpdateColumn(column pro)
        {
            StringBuilder sb = new StringBuilder();
            var userinfo = OperatorProvider.Provider.GetCurrent();
            sb.Append("update template_category_column set tcc_name='" + pro.tcc_name + "',tcc_desc='" + pro.tcc_desc + "',tcc_state=" + pro.tcc_state + ",tcc_enableshow='" + pro.tcc_enableshow + "',tcc_sort=" + pro.tcc_sort + " where tcc_id=" + pro.tcc_id);
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('update','template_category_column', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + pro.ToJson() + "'," + pro.tcc_id + ",'" + pro.tcc_name + "')");

            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
        /// <summary>
        /// 新增栏目
        /// </summary>
        /// <param name="pro">栏目信息</param>
        /// <returns></returns>
        public int AddColumn(column pro)
        {
            StringBuilder sb = new StringBuilder();
            var userinfo = OperatorProvider.Provider.GetCurrent();
            sb.Append("insert into template_category_column(tcc_name,tcc_state,tcc_enableshow,tcc_cou,tcc_sort,tcc_desc) VALUES('" + pro.tcc_name + "',1 ,'" + pro.tcc_enableshow + "'," + pro.tcc_cou + "," + pro.tcc_sort + ",'" + pro.tcc_desc + "')");
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('insert','template_category_column', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + pro.ToJson() + "',IDENT_CURRENT('[template_category_column]'),'" + pro.tcc_name + "')");

            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
        /// <summary>
        /// 删除栏目
        /// </summary>
        /// <param name="id">栏目自增id</param>
        /// <returns></returns>
        public int DeleteColumn(int id)
        {
            var userinfo = OperatorProvider.Provider.GetCurrent();
            StringBuilder sb = new StringBuilder();
            //检测是否存在下级数据
            List<management> ProjectColumn = DbHelp_MarketingShops.ado.GetDataList<management>("select * from template_category_join_project_column where tcc_id=" + id + " ");
            if (ProjectColumn.Count == 0) return 0;
            column data = GetColumnInfo(id);
            sb.Append("delete from template_category_column where tcc_id=" + id);
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('delete','template_category_column', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "'," + data.tcc_id + ",'" + data.tcc_name + "')");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
    }
}
