﻿using MarketingShops.DAL.refund;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.refund
{
    /// <summary>
    /// 订单退款
    /// </summary>
   public static class refund_bll
    {
        /// <summary>
        /// 退款
        /// 1.检测是否需要返还积分
        /// 2.检测积分是否返回成功
        /// 3.修改账单数据
        /// </summary>
        /// <param name="orderid">订单号</param>
        /// <param name="total_integral">积分</param>
        /// <returns></returns>
       public static int OrderRefund(string orderid, int total_integral)
        {
            return refund_dal.OrderRefund(orderid, total_integral);
        }
    }
}
