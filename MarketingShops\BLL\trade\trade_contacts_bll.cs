﻿using MarketingShops.BLL.Miniapp.trade;
using MarketingShops.DAL.trade;
using MarketingShops.Model.trade.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.trade
{
    /// <summary>
    /// 订单-联系人专用bll
    /// </summary>
   public class trade_contacts_bll
    {
       trade_contacts_dal _dal = new trade_contacts_dal();
        /// <summary>
        /// 修改联系人信息
        /// </summary>
        /// <param name="contacts">联系人数据</param>
        /// <returns></returns>
        public int UpdateContactsInfo(buy_bill_contacts contacts, string orderid)
        {
            int Result = _dal.UpdateContactsInfo(contacts);
            if (Result > 0)
            {
                //获取当前修改的物流信息
               // buy_bill_contacts data = GetContactsInfo(contacts.contacts_no);
                trade_mini_bll.MimiApp(orderid, contacts);
            }
            return Result;
        }

        /// <summary>
        /// 获取收件人信息
        /// </summary>
        /// <param name="orderid">联系人编号</param>
        /// <returns></returns>
        public buy_bill_contacts GetContactsInfo(string contacts_no)
        {
            return _dal.GetContactsInfo(contacts_no);
        }
    }
}
