﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.trade
{
    /// <summary>
    /// 调用api返回的数据结构
    /// </summary>
    /// <typeparam name="T"></typeparam>
   public class dataPost<T>
    {
       public bool success { get; set; }
       public string errorCode { get; set; }
       public List<T> ObjceData { get; set; }
    }
}
