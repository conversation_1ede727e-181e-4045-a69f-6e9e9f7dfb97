﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_ mail.cs
*
* 功 能： N/A
* 类 名： buy_bill_ mail
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-25 15:55:20   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_ mail:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
    public partial class buy_bill_mail
	{
        public buy_bill_mail()
		{}
		#region Model
        private string _mail_no;
        private string _orderid;
		private string _company;
		private string _mail_number;
		private int _stateno=1;
        private DateTime? _delivertime;
		/// <summary>
        /// 物流ID 
		/// </summary>
        public string mail_no
        {
            set { _mail_no = value; }
            get { return _mail_no; }
        }
		/// <summary>
        /// 商品订单号
		/// </summary>
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
		/// <summary>
        /// 物流公司
		/// </summary>
		public string company
		{
			set{ _company=value;}
			get{return _company;}
		}
		/// <summary>
        /// 快递单号
		/// </summary>
		public string mail_number
		{
			set{ _mail_number=value;}
			get{return _mail_number;}
		}
		/// <summary>
        /// 物流状态
		/// </summary>
		public int stateNo
		{
			set{ _stateno=value;}
			get{return _stateno;}
		}
        /// <summary>
        /// 发货时间
        /// </summary>
        public DateTime? deliverTime
        {
            set { _delivertime = value; }
            get { return _delivertime; }
        }
		#endregion Model

	}
}

