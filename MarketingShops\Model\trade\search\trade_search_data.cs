﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.trade.Search
{
    /// <summary>
    /// index页面专用model
    /// </summary>
    public class trade_search_data 
    {
        public trade_search_data()
		{}

        #region Model
        private string _orderid;
        private string _mail_no;
        private int _total_num;
        private int _total_fee = 0;
        private int _cash_fee = 0;
        private int _discount_fee = 0;
        private int _billstate = 0;
        private string _billstatename;
        private bool _isrefund = false;
        private string _phone;
        private string _memberPhoneNumber;
        private string _userid;
        private string _memberkey;
        private string _body;
        private string _g_name;
        private DateTime _intime = DateTime.Now;
        private int _total_integral = 0;
        /// <summary>
        /// 订单号
        /// </summary>
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }

        /// <summary>
        /// 物流号
        /// </summary>
        public string mail_no
        {
            set { _mail_no = value; }
            get { return _mail_no; }
        }

        /// <summary>
        /// 总件数
        /// </summary>
        public int total_num
        {
            set { _total_num = value; }
            get { return _total_num; }
        }
        /// <summary>
        /// 总金额
        /// </summary>
        public int total_fee
        {
            set { _total_fee = value; }
            get { return _total_fee; }
        }
        /// <summary>
        /// 优惠金额
        /// </summary>
        public int cash_fee
        {
            set { _cash_fee = value; }
            get { return _cash_fee; }
        }
        /// <summary>
        /// 折扣
        /// </summary>
        public int discount_fee
        {
            set { _discount_fee = value; }
            get { return _discount_fee; }
        }
        /// <summary>
        /// 订单状态编号
        /// </summary>
        public int billState
        {
            set { _billstate = value; }
            get { return _billstate; }
        }

        /// <summary>
        /// 是否已退款
        /// </summary>
        public bool isRefund
        {
            set { _isrefund = value; }
            get { return _isrefund; }
        }
        
        /// <summary>
        /// 收件人电话
        /// </summary>
        public string phone
        {
            set { _phone = value; }
            get { return _phone; }
        }
        /// <summary>
        /// 会员电话
        /// </summary>
        public string memberPhoneNumber
        {
            set { _memberPhoneNumber = value; }
            get { return _memberPhoneNumber; }
        }
        
        /// <summary>
        /// 操作人
        /// </summary>
        public string Userid
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 会员key
        /// </summary>
        public string memberKey
        {
            set { _memberkey = value; }
            get { return _memberkey; }
        }
        /// <summary>
        /// 订单名称
        /// </summary>
        public string body
        {
            set { _body = value; }
            get { return _body; }
        }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string g_name
        {
            set { _g_name = value; }
            get { return _g_name; }
        }
        /// <summary>
        /// 录入时间
        /// </summary>
        public DateTime inTime
        {
            set { _intime = value; }
            get { return _intime; }
        }
        /// <summary>
        /// 总积分
        /// </summary>
        public int total_integral
        {
            set { _total_integral = value; }
            get { return _total_integral; }
        }
       
        /// <summary>
        /// 订单状态
        /// </summary>
        public string billStateName
        {
            get
            {
                switch (billState)
                {
                    case 1:
                        _billstatename = "待付款";
                        break;
                    case 2:
                        _billstatename = "待收货";
                        break;
                    case 3:
                        _billstatename = "已完成";
                        break;
                    default:
                        _billstatename = "取消";
                        break;
                }
                return _billstatename;
            }
        }

       
        #endregion Model
    }
}
