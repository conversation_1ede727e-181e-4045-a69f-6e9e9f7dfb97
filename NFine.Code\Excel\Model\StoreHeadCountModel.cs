﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Web;

namespace NFine.Code.Excel.Model
{
    public class StoreHeadCountModel
    {
        /// <summary>
        /// 经营类别
        /// </summary>
        [Description("经营类别")]
        public string Business { get; set; }

        /// <summary>
        /// 类别明细
        /// </summary>
        public List<HeadCountDetail> Details { get; set; }
    }

    public class HeadCountDetail
    {
        /// <summary>
        /// 序号
        /// </summary>
        [Description("序号")]
        public int Index { get; set; }

        /// <summary>
        /// 类别1
        /// </summary>
        [Description("类别1")]
        public string Category1 { get; set; }

        /// <summary>
        /// 类别2
        /// </summary>
        [Description("类别2")]
        public string Category2 { get; set; }

        /// <summary>
        /// 人头数量
        /// </summary>
        [Description("来客人数")]
        public int PeoCount { get; set; }
    }
}