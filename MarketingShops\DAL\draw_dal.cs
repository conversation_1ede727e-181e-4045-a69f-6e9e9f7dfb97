﻿using MarketingShops.Model;
using MarketingShops.Model.Draw;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;


namespace MarketingShops.DAL
{
    public class draw_dal
    {
        public PageResultData GetAllDraw(string seltj, Pagination Page)
        {
            string sql = (@"select {0} from draw a join draw_enum_state b on a.d_state = b.state_id  where " + seltj + "");
            return DbHelp_MarketingShops.paging<draw>(sql, "*", Page.sidx, Page.page, Page.rows);
        }

        /// <summary>
        /// 删除主数据
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public int DeleteDraw(int d_id)
        {
            string sql = "delete from draw where d_id =" + d_id + "";
            return DbHelp_MarketingShops.ado.OperationData(sql);

        }

        /// <summary>
        /// 添加活动
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public int AddDraw(draw data)
        {
            string sql = "insert into draw(d_name,d_begtime,d_endtime,d_puttime,d_state) values ('" + data.d_name + "','" + data.d_begtime + "','" + data.d_endtime + "','" + data.d_puttime + "','" + data.d_state + "')";
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }

        /// <summary>
        /// 修改活动
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public int UpdateDraw(draw data)
        {
            string sql = "update draw set d_name='" + data.d_name + "',d_begtime='" + data.d_begtime + "',d_endtime='" + data.d_endtime + "',d_puttime='" + data.d_puttime + "',d_state='" + data.d_state + "' where d_id = " + data.d_id + "";
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }





        /// <summary>
        /// 获取所有状态
        /// </summary>
        /// <returns></returns>
        public List<draw_enum_state> GetAlldraw_enum_state()
        {
            return DbHelp_MarketingShops.ado.GetDataList<draw_enum_state>("select * from draw_enum_state");
        }

        /// <summary>
        /// 获取所有状态
        /// </summary>
        /// <returns></returns>
        public List<draw_enum_model> GetAlldraw_enum_model()
        {
            return DbHelp_MarketingShops.ado.GetDataList<draw_enum_model>("select * from draw_enum_model");
        }




        /// <summary>
        /// 获取下方配置
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public List<draw_config> GetDraw_Config(int d_id)
        {
            return DbHelp_MarketingShops.ado.GetDataList<draw_config>("select * from draw_config a join goods_sku b on a.sku_id = b.sku_id join draw_enum_model c on a.drawmodel = c.model_id where d_id=" + d_id + "");
        }

        /// <summary>
        /// 获取指定配置
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public draw_config GetDrawSinge_Config(int c_id)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle<draw_config>("select * from draw_config a join goods_sku b on a.sku_id = b.sku_id join draw_enum_model c on a.drawmodel = c.model_id where c_id=" + c_id + "");
        }

        /// 删除指定配置
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public int DeleteDraw_Config(int c_id)
        {
            string sql = "delete from draw_config where c_id =" + c_id + "";
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }

        /// 添加配置
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public int AddDraw_Config(draw_config data)
        {
            string sql = "insert into draw_config(c_name,d_id,sku_id,drawnum,drawmodel,appnum) values ('" + data.c_name + "','" + data.d_id + "','" + data.sku_id + "','" + data.drawnum + "','" + data.drawmodel + "','" + data.appnum + "')";
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }


        /// <summary>
        /// 修改配置
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public int UpdateDraw_Config(draw_config data)
        {
            string sql = "update draw_config set c_name='" + data.c_name + "', sku_id='" + data.sku_id + "',drawnum='" + data.drawnum + "',drawmodel='" + data.drawmodel + "',appnum='" + data.appnum + "' where c_id = " + data.c_id + "";
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }

        public ResultModel DrawClearWin_Config(int c_id)
        {
            ResultModel result = new ResultModel();
            try
            {
                string sql = "delete from draw_win where j_id in(select j_id from draw_join where c_id=" + c_id + ") ";
                int delnum = DbHelp_MarketingShops.ado.OperationData(sql);
                result.Msg = "共"+delnum.ToString()+"条清除成功";
                result.MsgState = 1;
            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }
            return result;

        }

        public ResultModel InSqlDataToTable(List<draw_win> data)
        {
            ResultModel result = new ResultModel();
            try
            {
                System.Data.SqlClient.SqlBulkCopy bcp = new System.Data.SqlClient.SqlBulkCopy("Data Source=192.168.2.14;Initial Catalog=MarketingShops;User Id=sa;Password=***");
                DataTable dt = new DataTable(); //员工卡券子表
                dt.Columns.Add("j_id", typeof(Int32));
                dt.Columns.Add("j_stateid", typeof(Int32));
                dt.Columns.Add("wintype", typeof(Int32));
                foreach (var item in data)
                {
                    dt.Rows.Add(item.j_id, 3, 1);
                }
                bcp.DestinationTableName = "MarketingShops.dbo.draw_win";//指定目标表的名称
                bcp.WriteToServer(dt);//导入表
                result.Msg = "共" + dt.Rows.Count.ToString() + "条抽取成功";
                result.MsgState = 1;
                
            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }
            return result;

        }



    }
}
