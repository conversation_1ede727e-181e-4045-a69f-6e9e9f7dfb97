﻿using MarketingShops.DAL.RefundTardeDetailed;
using MarketingShops.Model;
using MarketingShops.Model.RefundTardeDetailed.Export;
using MarketingShops.Model.RefundTardeDetailed.Search;
using NFine.Code;
using NFine.Code.Export;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.RefundTardeDetailed
{
    /// <summary>
    /// 账单退款明细
    /// </summary>
    public class refund_bill_detailed_bll
    {
        refund_bill_detailed_dal _dal = new refund_bill_detailed_dal();

        /// <summary>
        /// 账单退款明细查询
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <param name="Page">分页信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(refund_bill_detailed sys, Pagination Page, string BTime, string ETime,int? fee1,int? fee2, int? number1, int? number2)
        {
            string seltj = GetSearchStr(sys, BTime, ETime, fee1, fee2, number1, number2);
            return _dal.GetGridInfo(seltj, Page);
        }

        #region 查询数据条件
        /// <summary>
        /// 获取查询SQL语句
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <returns></returns>
        string GetSearchStr(refund_bill_detailed sys, string BTime, string ETime, int? fee1, int? fee2,int? number1,int? number2)
        {
            string seltj = " 1 = 1 ";

            if (!string.IsNullOrEmpty(sys.orderid) && sys.orderid != "undefined")//订单号模糊查询
                seltj += "and [orderid] like '%" + sys.orderid + "%'";

            if (sys.refundState > 0)//退款状态查询
                seltj += " and [refundState]=" + sys.refundState + "";

            if (!string.IsNullOrEmpty(sys.opName))//操作人员查询
                seltj += " and [opName]='" + sys.opName + "'";

            if (!string.IsNullOrEmpty(BTime) && !string.IsNullOrEmpty(ETime))//退款时间
            {
                ETime += ":59";//结束时间到当前选中的分
                seltj += " and [opTime] between '" + BTime.Trim() + "' and  '" + ETime.Trim() + "' ";
            }

            if (fee1 >= 0 && fee2 ==null)//退款金额
            {
                seltj += " and [fee]>='" + fee1 + "'";
            }

            if (fee2 >= 0 && fee1 == null)//退款金额
            {
                seltj += " and [fee]<='" + fee2 + "'";
            }

            if (fee1 >= 0 && fee2 >= 0)//退款金额金额
                seltj += " and [fee] between " + fee1 + " and " + fee2 + " ";


             if (number1 >= 0 && number2 ==null)//退款数量
            {
                seltj += " and [number]>='" + number1 + "'";
            }

            if (number2 >= 0 && number1 == null)//退款数量
            {
                seltj += " and [number]<='" + number2 + "'";
            }

            if (number1 >= 0 && number2 >= 0)//退款数量
                seltj += " and [number] between " + number1 + " and " + number2 + " ";


            return seltj;
        }
        #endregion


        /// <summary>
        /// 导出报表
        /// </summary>
        /// <param name="sys">导出条件</param>
        /// <returns></returns>
        public void ExportTradeList(refund_bill_detailed sys, Pagination Page, string BTime, string ETime, int? fee1, int? fee2, int? number1, int? number2)
        {
            string seltj = GetSearchStr(sys, BTime, ETime, fee1, fee2, number1, number2);
            string filename = "商品销售明细报表" + DateTime.Now.ToString("yyyy-MM-dd");
            string fileformat = "csv";
            List<export_refund_bill_detailed> data = _dal.ExportTradeList(seltj);
            ExportData ex = new ExportData();
            ex.ExportToExcel<export_refund_bill_detailed>(data, filename, fileformat);
        }
    }
}
