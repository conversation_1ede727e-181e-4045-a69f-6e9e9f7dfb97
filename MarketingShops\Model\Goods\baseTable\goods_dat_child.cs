﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods.baseTable
{
    /// <summary>
    /// 商品信息子表
    /// </summary>
   public class goods_dat_child 
    {
      /// <summary>
      /// 描述
      /// </summary>
       public string desc { get; set; }
       /// <summary>
       /// SKU JSON集合字符
       /// </summary>
       public string sku_json { get; set; }
        /// <summary>
       /// 详情id
       /// </summary>
       public string gd_id { get; set; }
       /// <summary>
       /// 分类id
       /// </summary>
       public string f_id { get; set; }
       /// <summary>
       /// 商户id
       /// </summary>
       public string m_id { get; set; }
       /// <summary>
       /// 栏目id
       /// </summary>
       public int f_tcpid { get; set; }
       /// <summary>
       /// 系统分类id
       /// </summary>
       public int scid { get; set; }
    }
}
