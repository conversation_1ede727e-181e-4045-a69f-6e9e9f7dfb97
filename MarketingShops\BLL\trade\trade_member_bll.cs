﻿using MarketingShops.BLL.MIMS;
using MarketingShops.DAL.trade;
using MarketingShops.Model.trade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.trade
{
    /// <summary>
    /// 订单-会员专用bll
    /// </summary>
   public static class trade_member_bll
    {
       /// <summary>
       /// 获取会员信息
       /// </summary>
       /// <param name="MemberKey">会员key</param>
       /// <returns>会员信息</returns>
       public static MemberInfo GetMemberInfo(string MemberKey) 
       {
           return member_bll.GetMemberInfoByKey(MemberKey);
       }      
    }
}
