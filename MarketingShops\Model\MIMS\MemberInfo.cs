﻿/**  版本信息模板在安装目录下，可自行修改。
* MemberInfo.cs
*
* 功 能： N/A
* 类 名： MemberInfo
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-09-03 15:25:38   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade
{
	/// <summary>
	/// MemberInfo:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class MemberInfo
	{
		public MemberInfo()
		{}
		#region Model
		private DateTime _memberregdate= DateTime.Now;
		private string _membername="";
		private string _membersex="";
		private string _memberbirthday="";
		private string _memberidnumber="";
		private string _memberphonenumber="";
		private string _memberaddress="";
		private int _membercardtypeno=0;
		private int _pointtotal=0;
		private int _integraltotal=0;
		private int _rechargetotal=0;
		private int _returntotal=0;
        private int _RechargeTotalFrozen = 0;
		private string _val3="";
		private string _val4="";
        private int _MemberCardStatus;
		/// <summary>
		/// 登记时间
		/// </summary>
		public DateTime MemberRegDate
		{
			set{ _memberregdate=value;}
			get{return _memberregdate;}
		}
		/// <summary>
		/// 名字
		/// </summary>
		public string MemberName
		{
			set{ _membername=value;}
			get{return _membername;}
		}
		/// <summary>
		/// 性别
		/// </summary>
		public string MemberSex
		{
			set{ _membersex=value;}
			get{return _membersex;}
		}
		/// <summary>
		/// 生日
		/// </summary>
		public string MemberBirthday
		{
			set{ _memberbirthday=value;}
			get{return _memberbirthday;}
		}
		/// <summary>
		/// 卡号
		/// </summary>
		public string MemberIDNumber
		{
			set{ _memberidnumber=value;}
			get{return _memberidnumber;}
		}
		/// <summary>
		/// 电话
		/// </summary>
		public string MemberPhoneNumber
		{
			set{ _memberphonenumber=value;}
			get{return _memberphonenumber;}
		}
		/// <summary>
		/// 地址
		/// </summary>
		public string MemberAddress
		{
			set{ _memberaddress=value;}
			get{return _memberaddress;}
		}
		/// <summary>
		/// 卡类型
		/// </summary>
		int MemberCardTypeNo
		{
			set{ _membercardtypeno=value;}
			get{return _membercardtypeno;}
		}

         public string MemberCardTypeName
        {
            get
            {
                return GetMemberLeveName(MemberCardTypeNo);
            }
        }
		/// <summary>
		/// 积点
		/// </summary>
		public int PointTotal
		{
			set{ _pointtotal=value;}
			get{return _pointtotal;}
		}
		/// <summary>
		/// 积分
		/// </summary>
		public int IntegralTotal
		{
			set{ _integraltotal=value;}
			get{return _integraltotal;}
		}
		/// <summary>
		/// 充值
		/// </summary>
		public int RechargeTotal
		{
			set{ _rechargetotal=value;}
			get{return _rechargetotal;}
		}
		/// <summary>
		/// 返还
		/// </summary>
		public int ReturnTotal
		{
			set{ _returntotal=value;}
			get{return _returntotal;}
		}
        /// <summary>
        /// 充值冻结金额
        /// </summary>
        public int RechargeTotalFrozen
		{
            set { _RechargeTotalFrozen = value; }
            get { return _RechargeTotalFrozen; }
		}
        
		/// <summary>
		/// openid
		/// </summary>
		public string Val3
		{
			set{ _val3=value;}
			get{return _val3;}
		}
		/// <summary>
		/// 微信卡号
		/// </summary>
		public string Val4
		{
			set{ _val4=value;}
			get{return _val4;}
		}
        /// <summary>
        /// 会员卡状态
        /// </summary>
        int MemberCardStatus
        {
            set { _MemberCardStatus = value; }
            get { return _MemberCardStatus; }
        }
        public string MemberCardStatusStr
        {
            get
            {
                return GetMemberStatusName(MemberCardStatus);
            }
        }

        /// <summary>
        /// 获取会员状态名称
        /// </summary>
        /// <param name="MemberCardStatus"></param>
        /// <returns></returns>
        string GetMemberStatusName(int MemberCardStatus)
        {
            if (MemberCardStatus == 0)
                return "未激活";
            else if (MemberCardStatus == 1)
                return "正常";
            else if (MemberCardStatus == 2)
                return "升级";
            else if (MemberCardStatus == 3)
                return "挂失";
            else if (MemberCardStatus == 4)
                return "注销";
            else
                return "不详";
        }
        /// <summary>
        /// 获取会员等级名称
        /// </summary>
        string GetMemberLeveName(int MemberCardTypeNo)
        {

            switch (MemberCardTypeNo)
            {
                case 0:
                    return "易享卡";
                case 1:
                    return "VIP卡";
                case 2:
                    return "VIP金卡";
                case 3:
                    return "VIP白金卡";
                case 4:
                    return "kboss金卡";
                case 5:
                    return "kboss白金卡";
                case 6:
                    return "kboss黑金卡";
                case 7:
                    return "kboss钻石卡";
                case 8:
                    return "餐厅卡";
                case 9:
                    return "专属卡";
                case 10:
                    return "kbossVIP卡";
                default:
                    return "未知类型";
            }
        }
		#endregion Model

	}
}

