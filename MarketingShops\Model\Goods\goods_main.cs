﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods
{
    /// <summary>
    /// 商品项目表
    /// </summary>
    public class goods_main
    {
        /// <summary>
        /// 项目id
        /// </summary>
        public string pid { get; set; }
        /// <summary>
        /// 主售商品id
        /// </summary>
        public string g_id { get; set; }
        /// <summary>
        /// 项目名称
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 商品数量（商品表数量）
        /// </summary>
        public int g_count { get; set; }
        /// <summary>
        /// 有效商品（正常销售商品）
        /// </summary>
        public int valid_count { get; set; }
        /// <summary>
        /// 销售量
        /// </summary>
        public int sales_nums { get; set; }
        /// <summary>
        /// 项目状态
        /// </summary>
        public int state { get; set; }
        /// <summary>
        /// 排序字段
        /// </summary>
        public int sort { get; set; }
        /// <summary>
        /// 更新时间（子集发生变化会刷新此字段，防止子数据刷新排序在底部），新增项目默认是创建时间
        /// </summary>
        public DateTime updatetime { get; set; }
    }
}
