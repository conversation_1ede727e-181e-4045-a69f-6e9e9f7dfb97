/*******************************************************************************
 * Copyright © 2016 NFine.Framework 版权所有
 * Author: NFine
 * Description: NFine快速开发平台
 * Website：http://www.nfine.cn
*********************************************************************************/
using NFine.Domain.Entity.SystemSecurity;
using NFine.Application.SystemSecurity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using NFine.Domain.Entity.SystemManage;
using NFine.Application.SystemManage;
using NFine.Code;
using NFine.Application;

namespace NFine.Web.Controllers
{
    public class LoginController : Controller
    {
        [HttpGet]
        public virtual ActionResult Index()
        {
            ////获取站点配置信息
            NFine.Application.SystemManage.SiteApp app = new NFine.Application.SystemManage.SiteApp();
            var data = app.GetList();
            OperatorConfig.Site = data;

            var test = string.Format("{0:E2}", 1);


            OperatorModel current = null;

            try
            {
                current = NFine.Code.OperatorProvider.Provider.GetCurrent();
            }
            catch
            {

            }

            if (current != null)
                return RedirectToAction("Index", "Home");
            else
                return View();
        }


        /// <summary>
        /// 客户端登录
        /// </summary>
        /// <returns></returns>
        public virtual ActionResult IndexClient()
        {
            ////获取站点配置信息
            NFine.Application.SystemManage.SiteApp app = new NFine.Application.SystemManage.SiteApp();
            var data = app.GetList();
            OperatorConfig.Site = data;
            var test = string.Format("{0:E2}", 1);
            OperatorModel current = null;
            try
            {
                current = NFine.Code.OperatorProvider.Provider.GetCurrent();
            }
            catch{}
            if (current != null)
                return RedirectToAction("IndexClient", "Home");
            else
                return View();
        }

        [HttpGet]
        public ActionResult GetAuthCode()
        {
            return File(new VerifyCode().GetVerifyCode(), @"image/Gif");
        }
        [HttpGet]
        public ActionResult OutLogin()
        {
            try
            {
                new LogApp().WriteDbLog(new LogEntity
                {
                    F_ModuleName = "系统登录",
                    F_Type = DbLogType.Exit.ToString(),
                    F_Account = OperatorProvider.Provider.GetCurrent().UserCode,
                    F_NickName = OperatorProvider.Provider.GetCurrent().UserName,
                    F_Result = true,
                    F_Description = "安全退出系统",
                });

            }
            catch
            {
            }
            try
            {

                Session.Abandon();
                Session.Clear();
                OperatorProvider.Provider.RemoveCurrent();
            }
            catch (Exception)
            {
            }

            return RedirectToAction("Index", "Login");
        }
        [HttpGet]
        public ActionResult OutLoginClient()
        {
            try
            {
                new LogApp().WriteDbLog(new LogEntity
                {
                    F_ModuleName = "系统登录",
                    F_Type = DbLogType.Exit.ToString(),
                    F_Account = OperatorProvider.Provider.GetCurrent().UserCode,
                    F_NickName = OperatorProvider.Provider.GetCurrent().UserName,
                    F_Result = true,
                    F_Description = "安全退出系统",
                });

            }
            catch
            {
            }
            try
            {

                Session.Abandon();
                Session.Clear();
                OperatorProvider.Provider.RemoveCurrent();
            }
            catch (Exception)
            {
            }

            return RedirectToAction("IndexClient", "Login");
        }
        [HttpPost]
        [HandlerAjaxOnly]
        public ActionResult CheckLogin(string username, string password, string code)
        {
            LogEntity logEntity = new LogEntity();
            logEntity.F_ModuleName = "系统登录";
            logEntity.F_Type = DbLogType.Login.ToString();
            try
            {
                if (OperatorConfig.Site["IsVerifycode"] != "0")
                {
                    if (Session["nfine_session_verifycode"].IsEmpty() || Md5.md5(code.ToLower(), 16) != Session["nfine_session_verifycode"].ToString())
                    {
                        throw new Exception("验证码错误，请重新输入");
                    }
                }

                UserEntity userEntity = new UserApp().CheckLogin(username, password);
                if (userEntity != null)
                {
                    OperatorModel operatorModel = new OperatorModel();
                    operatorModel.UserId = userEntity.F_Id;
                    operatorModel.UserCode = userEntity.F_Account;
                    operatorModel.UserName = userEntity.F_RealName;
                    operatorModel.CompanyId = userEntity.F_OrganizeId;
                    operatorModel.DepartmentId = userEntity.F_DepartmentId;
                    operatorModel.RoleId = userEntity.F_RoleId;
                    operatorModel.LoginIPAddress = Net.Ip;
                    operatorModel.LoginIPAddressName = Net.GetLocation(operatorModel.LoginIPAddress);
                    operatorModel.LoginTime = DateTime.Now;
                    operatorModel.LoginToken = DESEncrypt.Encrypt(Guid.NewGuid().ToString());
                    if (userEntity.F_Account == "admin")
                    {
                        operatorModel.IsSystem = true;
                    }
                    else
                    {
                        operatorModel.IsSystem = false;
                    }
                    OperatorProvider.Provider.AddCurrent(operatorModel);
                    logEntity.F_Account = userEntity.F_Account;
                    logEntity.F_NickName = userEntity.F_RealName;
                    logEntity.F_Result = true;
                    logEntity.F_Description = "登录成功";
                    new LogApp().WriteDbLog(logEntity);
                }
                return Content(new AjaxResult { state = ResultType.success.ToString(), message = "登录成功。" }.ToJson());
            }
            catch (Exception ex)
            {
                logEntity.F_Account = username;
                logEntity.F_NickName = username;
                logEntity.F_Result = false;
                logEntity.F_Description = "登录失败，" + ex.Message;
                new LogApp().WriteDbLog(logEntity);
                return Content(new AjaxResult { state = ResultType.error.ToString(), message = ex.Message }.ToJson());
            }
        }
    }
}
