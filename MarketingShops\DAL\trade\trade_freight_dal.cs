﻿using MarketingShops.Model.Goods.enumTable;
using MarketingShops.Model.trade.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.trade
{
    /// <summary>
    /// 订单-物流专用dal
    /// </summary>
   public class trade_freight_dal
    {
        /// <summary>
        /// 获取物流信息
        /// </summary>
        /// <param name="orderid">订单号</param>
        /// <returns></returns>
        public buy_bill_mail GetFreightInfo(string mail_no)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_mail>(@"SELECT mail_no,company,mail_number,deliverTime
  FROM [MarketingShops].[dbo].[buy_bill_mail] a join dbo.buy_bill_secondary b on a.orderid=b.orderid where mail_no=@mail_no", new { mail_no = mail_no });
        }
        /// <summary>
        /// 获取物流公司数据
        /// </summary>
        /// <returns></returns>
        public List<good_enum_freight> GetCompanyList()
        {
            return DbHelp_MarketingShops.ado.GetDataList<good_enum_freight>("SELECT * FROM dbo.mail_freight_project"); ;
        }
        /// <summary>
        /// 发货(添加物流信息)
        /// </summary>
        /// <param name="mail">物流信息</param>
        /// <returns></returns>
        public int AddMailInfo(buy_bill_mail mail)
        {
            StringBuilder sb = new StringBuilder("SET XACT_ABORT OFF  BEGIN TRAN ");
            sb.Append(TradeMailStr(mail));
            sb.Append(" COMMIT TRAN");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString(), mail);
        }

        public List<buy_bill_mail> GetMailList() 
        {
            return DbHelp_MarketingShops.ado.GetDataList<buy_bill_mail>("SELECT * FROM dbo.buy_bill_mail"); ;

        }
        /// <summary>
        /// 批量发货
        /// </summary>
        /// <param name="deliverList">发货集合</param>
        /// <returns></returns>
        public int BatchDeliver(List<buy_bill_mail> deliverList)
        {
            StringBuilder sb = new StringBuilder("SET XACT_ABORT OFF  BEGIN TRAN ");
            for (int i = 0; i < deliverList.Count; i++)
            {
                sb.Append(TradeMailStr(deliverList[i]));
            }
            sb.Append(" COMMIT TRAN");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
        /// <summary>
        /// 修改物流信息
        /// </summary>
        /// <param name="mail">物流信息</param>
        /// <returns></returns>
        public int UpdateMailInfo(buy_bill_mail mail)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("if exists(select * from [MarketingShops].[dbo].[buy_bill_mail] where [mail_no]=@mail_no) begin ");
            sb.Append(" update [MarketingShops].[dbo].[buy_bill_mail] set [mail_number]=@mail_number where [mail_no]=@mail_no");
            sb.Append(" update [MarketingShops].[dbo].[buy_bill_secondary] set [upTime]=getdate() where [orderid]=@orderid");
            sb.Append(" end");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString(), mail);
        }
        string TradeMailStr(buy_bill_mail mail)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("if exists(select * from [MarketingShops].[dbo].[buy_bill_core] where [orderid]='" + mail.orderid + "' ) begin ");
            sb.Append("if not exists(select * from [MarketingShops].[dbo].[buy_bill_mail] where [orderid]='" + mail.orderid + "' ) begin ");
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_mail]
           ([mail_no]
           ,[orderid]
           ,[company]
           ,[mail_number]
           ,[stateNo])
     VALUES
           ('" + mail.mail_no + "','" + mail.orderid + "' ,'" + mail.company + "' ,'" + mail.mail_number + "' ," + mail.stateNo + ") ");
            //修改订单状态 
            sb.Append("update [MarketingShops].[dbo].[buy_bill_core] set billState=2 ,[mail_no]='" + mail.mail_no + "' where [orderid]='" + mail.orderid + "' ");
            //修改发货时间
            sb.Append("update [MarketingShops].[dbo].[buy_bill_secondary] set [deliverTime]=getdate(),[upTime]=getdate() where [orderid]='" + mail.orderid + "' ");
            sb.Append("end ");
            sb.Append("end ");
            return sb.ToString();
        }
    }
}
