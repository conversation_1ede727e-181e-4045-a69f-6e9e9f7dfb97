﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods.baseTable
{
    /// <summary>
    /// Spu-栏目属性集合
    /// </summary>
   public class goods_dat_spu_entity_list
    {
       /// <summary>
        /// Spu 集合编号
       /// </summary>
       public int spu_l_id { get; set; }
       /// <summary>
       /// 属性名称
       /// </summary>
       public string name { get; set; }
       /// <summary>
       /// 属性值
       /// </summary>
       public string value { get; set; }
       /// <summary>
       /// 模板类别项ID
       /// </summary>
       public int tcp_id { get; set; }
    }
}
