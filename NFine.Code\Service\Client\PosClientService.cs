﻿using ComponentApplicationServiceInterface.Context.Response;
using NFine.Code.Service.Model;
using SERVICE.PROXY.PosService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Code.Service.Client
{
    public class PosClientService : ClientBase
    {
        public PosClient PosService;
        public PosClientService(int storeId)
        {
            var storeList = GetStoreConfig();
            var storeConfig = storeList.FirstOrDefault(w => w.RealStoreId == storeId);
            if (storeConfig == null)
                throw new Exception("店铺配置不存在，请检查！");
            if (string.IsNullOrEmpty(storeConfig.Url))
                throw new Exception("店铺未配置请求地址，请检查！");

            BasicHttpBinding binding = new BasicHttpBinding();
            EndpointAddress endpoint = new EndpointAddress(storeConfig.Url);
            binding.MaxBufferPoolSize = 2147483647;
            binding.MaxBufferSize = 2147483647;
            binding.MaxReceivedMessageSize = 2147483647;
            PosService = new PosClient(binding, endpoint);
        }
    }
}
