﻿using MarketingShops.DAL;
using MarketingShops.Model;
using MarketingShops.Model.Img_Manage;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL
{
    public class resource_space_file_bll
    {
        resource_space_file_dal _dal = new resource_space_file_dal();
        
        
        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="s_id"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public List<resource_space_file> GetLevFileData(int s_id)
        {
            List<resource_space_file> list = GetFolderData(s_id);//查出所有的集合

            var Subgrade = new Dictionary<Guid, resource_space_file>(list.Count);//此处的Guid要根据层级的类型进行更改
            
            foreach (var item in list)
            {
                Subgrade.Add(item.ikey, item);
            }
            foreach (var data in Subgrade.Values)
            {
                if (data.mkey.HasValue) //HasValue 判断可空类型是否有值
                {
                    if (Subgrade.ContainsKey(data.mkey.Value))//ContainsKey是否判断属性是否存在	
                    {
                        if (Subgrade[data.mkey.Value].subfileList == null)//如果为空就调用在子集里面加入
                        {
                            Subgrade[data.mkey.Value].subfileList = new List<resource_space_file>();
                        }
                        Subgrade[data.mkey.Value].subfileList.Add(data);
                    }
                }
            }
            return Subgrade.Values.Where(t => t.mkey.HasValue == false).ToList();//运用lambda表达式找出所有的第一级
            //HasValue == false即没有值
        }

        /// <summary>
        /// 获取文件数据
        /// </summary>
        /// <param name="s_id"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public List<resource_space_file> GetFileData(int s_id, Guid? mkey)
        {
            StringBuilder seltj = new StringBuilder("s_id= " + s_id + " and mkey =" + mkey + "");

            return _dal.GetImgFileData(seltj.ToString());
        }

        /// <summary>
        /// 获取文件夹数据
        /// </summary>
        /// <param name="s_id"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public List<resource_space_file> GetFolderData(int s_id)
        {
            StringBuilder seltj = new StringBuilder("s_id= " + s_id + " and isfile =0");

            return _dal.GetImgFileData(seltj.ToString());

        }



        /// <summary>
        /// 获取当前目录下的文件数据
        /// </summary>
        /// <param name="s_id"></param>
        /// <param name="nmkey"></param>
        /// <returns></returns>
        public List<resource_space_file> GetNextData(int s_id, Guid? nmkey)
        {
            string mkey = nmkey.ToString();
            if (string.IsNullOrEmpty(mkey))
            {
                mkey = "mkey is NULL";
            }
            else
            {
                mkey = "mkey='" + mkey + "'";
            }
            StringBuilder seltj = new StringBuilder("s_id= " + s_id + " and   " + mkey + " and isfile =1");
            return _dal.GetImgFileData(seltj.ToString());
        }

        /// <summary>
        /// 获取搜索数据
        /// </summary>
        /// <param name="s_id"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public List<resource_space_file> GetImgFileData(resource_space_file data)
        {
            return _dal.GetImgFileData(ReturnSeltj(data).ToString());
        }


        /// <summary>
        /// 只需要文件数据
        /// </summary>
        /// <param name="s_id"></param>
        /// <param name="filename"></param>
        /// <returns></returns>
        public List<resource_space_file> GetSearchFileData(resource_space_file data)
        {
            StringBuilder sb = new StringBuilder();
            sb = ReturnSeltj(data);
            sb.Append(" and isfile = 1");
            return _dal.GetImgFileData(sb.ToString(),"top 50 *");
        }

        /// <summary>
        /// 获取条件
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public StringBuilder ReturnSeltj(resource_space_file data)
        {

            StringBuilder seltj = new StringBuilder("1=1");

            if (data.s_id != 0)
            {
                seltj.Append(" and s_id = '");
                seltj.Append(data.s_id);
                seltj.Append("'");
            }
            if (!string.IsNullOrEmpty(data.filename))
            {
                seltj.Append(" and filename like '%");
                seltj.Append(data.filename);
                seltj.Append("%'");
            }
            if (data.ikey != default(Guid))
            {
                seltj.Append(" and a.ikey = '");
                seltj.Append(data.ikey);
                seltj.Append("'");
            }
            if (data.mkey != default(Guid))
            {
                seltj.Append(" and a.mkey = '");
                seltj.Append(data.mkey);
                seltj.Append("'");
            }
            if (data.ft_id != 0)
            {
                seltj.Append(" and a.ft_id = '");
                seltj.Append(data.ft_id);
                seltj.Append("'");
            }
            return seltj;
        
        }


        /// <summary>
        /// 插入文件数据
        /// </summary>
        /// <returns></returns>
        public ResultModel AddFile(resource_space_file data)
        {
            ResultModel result = new ResultModel();
            try
            {
                string thisurl ="";
                if (data.isouturl == false)
                { //不是外部链接
                    data.fileurl = returnfileur(data);
                    thisurl = baseurl + data.fileurl;
                    if (IsExistFile(thisurl))
                    {
                        result.MsgState = 2;
                        result.Msg = "已存在相同文件";
                    }
                    else {
                        result.MsgState = _dal.AddData(data);
                    }
                }
                else {//是外部链接则只用插入数据就可以了
                    result.MsgState = _dal.AddData(data);
                }

                
            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }
           
            return result;
        }

          /// <summary>
        /// 插入文件数据
        /// </summary>
        /// <returns></returns>
        public ResultModel AddFile(List<resource_space_file> data, bool iscover)
        {
            string inputid = NFine.Code.OperatorProvider.Provider.GetCurrent().UserCode;
            int ft_id = 0;
            int s_id = 0;
            string mkeystr = "";
            
            ResultModel result = new ResultModel();
            try
            {
                //string thisurl ="";
                List<resource_space_file> sqldata = new List<resource_space_file>(); ///改层级下数据库所有的文件
                if (data != null && data.Count > 0)
                {
                    s_id = data[0].s_id;
                    Guid? mkey = data[0].mkey;
                    ft_id = data[0].ft_id;
                    if (string.IsNullOrEmpty(mkey.ToString()))
                    {
                        mkeystr = "NULL";
                        mkey = default(Guid);
                    }
                    else
                    {
                        mkeystr = "'" + mkey.ToString() + "'";
                    }
                    sqldata = GetImgFileData(new resource_space_file { s_id = s_id, mkey = mkey, isfile = true });
                    List<resource_space_file> isexistdata = new List<resource_space_file>();///传递中已存在文件
                    StringBuilder sb = new StringBuilder();
                    foreach (var item in data)
                    {
                        if (sqldata.FindAll(x => x.filename == item.filename).Count > 0)
                        { ///有重名的文件,只需将旧数据复制到操作记录中，并修改旧数据时间和操作人即可
                            isexistdata.Add(item);
                            string selmkeystr = " mkey = " + mkeystr + "";
                            if(mkeystr=="NULL")
                                selmkeystr = " mkey is NULL";
                            sb.Append("insert into log_resource_space_file select *,'被覆盖','" + inputid + "',getdate() from resource_space_file where s_id =" + s_id + " and " + selmkeystr + " and filename = '" + item.filename + "' ");
                            sb.Append("update resource_space_file set inputid='" + inputid + "',inputtime =getdate(),filesize = " + item.filesize + " where s_id =" + s_id + " and " + selmkeystr + " and filename = '" + item.filename + "' ");
                        }
                        else
                        {
                            sb.Append("insert into resource_space_file(ikey,mkey,s_id,inputid,filename,fileurl,filesize,inputtime,ft_id,isouturl) values (NEWID()," + mkeystr + "," + item.s_id + ",'" + inputid + "','" + item.filename + "','" + item.fileurl + "','" + item.filesize + "',getdate(),'" + ft_id + "','" + item.isouturl + "')");
                        }
                    }

                    if (iscover == false && isexistdata.Count > 0)///不覆盖且存在重名文件
                    {
                        result.Msg = "存在重名文件为:" + string.Join(",", isexistdata.Select(a => a.filename));
                    }
                    else
                    {
                        result.MsgState = _dal.ExecuteSql(sb.ToString());
                    }
                }
                else {
                    result.Msg = "未获取到传递数据";
                }
                
                
            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }
           
            return result;
        }



        /// <summary>
        /// 插入文件夹
        /// </summary>
        /// <returns></returns>
        public ResultModel AddFoler(resource_space_file data)
        {
            ResultModel result = new ResultModel();
            try
            {
                data.fileurl = returnfileur(data);
                string thisurl = baseurl + data.fileurl;
                data.fileurl=basefileurl+data.filename;
                if (IsExistDirectory(thisurl))
                {
                    result.Msg = "已存在相同目录文件夹";
                }
                else
                {
                    CreateDirectory(thisurl);
                    result.MsgState = _dal.AddData(data);
                }
            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }
           
            return result;
        }


        private string returnfileur(resource_space_file data) {

            string url ="";
            if (data.mkey == null)
            {
                resource_spacesub_bll rsb = new resource_spacesub_bll();
                resource_space_sub mdata = rsb.GetSingSubData(data.s_id);
                url = data.filename;
            }
            else
            {
                resource_space_file mdata = GetImgFileData(new resource_space_file { ikey = new Guid(data.mkey.ToString()),mkey=default(Guid) }).FirstOrDefault();
                url = mdata.fileurl + "/" + data.filename;
            }
            return url;
        
        }
        /// <summary>
        /// 删除文件
        /// </summary>
        /// <returns></returns>
        public ResultModel DelFile(Guid ikey,string dealid)
        {
            return _dal.DelFile(ikey, dealid);
        }
        public static string basefileurl = "/Content/Resource_Space/AllResource/";
        public static string baseurl = AppDomain.CurrentDomain.BaseDirectory + basefileurl;
        
        #region 检测指定目录是否存在
        /// <summary>
        /// 检测指定目录是否存在
        /// </summary>
        /// <param name="directoryPath">目录的绝对路径</param>
        /// <returns></returns>
        public static bool IsExistDirectory(string directoryPath)
        {
            return Directory.Exists(directoryPath);
        }
        #endregion

      
        #region 创建一个目录
        /// <summary>
        /// 创建一个目录
        /// </summary>
        /// <param name="directoryPath">目录的绝对路径</param>
        public static void CreateDirectory(string directoryPath)
        {
            //如果目录不存在则创建该目录
            if (!IsExistDirectory(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }
        }
        #endregion


        #region 检测指定文件是否存在
        /// <summary>
        /// 检测指定目录是否存在
        /// </summary>
        /// <param name="directoryPath">目录的绝对路径</param>
        /// <returns></returns>
        public static bool IsExistFile(string FilePath)
        {
            return File.Exists(FilePath);
        }
        #endregion


       

   
    }
}
