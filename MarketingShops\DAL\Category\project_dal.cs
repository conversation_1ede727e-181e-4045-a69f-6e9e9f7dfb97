﻿using MarketingShops.Model;
using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Category
{
    /*
    项目表数据库操作
    * 增删改操作均记录在操作表log_marketing_edit
    */
    public  class project_dal
    {
      /// <summary>
      /// 获取项目信息
      /// </summary>
      /// <param name="seltj">查询条件</param>
      /// <param name="Page">分页信息</param>
      /// <returns></returns>
        public PageResultData GetGridInfo(string tcp_name, Pagination Page)
      {
          string variable = "1=1";
          if (!string.IsNullOrEmpty(tcp_name))
              variable += "and [tcp_name]='" + tcp_name.Trim() + " '";

          string sql = "select {0} from dbo.template_category_project where " + variable;
          return DbHelp_MarketingShops.paging<project>(sql, "*", Page.sidx, Page.page, Page.rows);
      }
      /// <summary>
      /// 获取项目详情
      /// </summary>
      /// <param name="id">项目自增id</param>
      /// <returns></returns>
      public project GetProjectInfo(int id)
      {
          return DbHelp_MarketingShops.ado.GetDataSingle<project>("select * from template_category_project where tcp_id=" + id + " ");
      }
      /// <summary>
      /// 修改项目信息
      /// </summary>
      /// <param name="pro">项目信息</param>
      /// <returns></returns>
      public int UpdateProject(project pro) 
      {
          StringBuilder sb = new StringBuilder();
          var userinfo = OperatorProvider.Provider.GetCurrent();
          sb.Append("update template_category_project set tcp_name='" + pro.tcp_name + "',tcp_sort=" + pro.tcp_sort + ",tcp_enableshow='" + pro.tcp_enableshow + "' where tcp_id=" + pro.tcp_id);
          sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('update','template_category_project', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + pro.ToJson() + "'," + pro.tcp_id + ",'" + pro.tcp_name + "')");

          return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
      }
      /// <summary>
      /// 新增项目
      /// </summary>
      /// <param name="pro">项目信息</param>
      /// <returns></returns>
      public int AddProject(project pro) 
      {
          StringBuilder sb = new StringBuilder();
          var userinfo = OperatorProvider.Provider.GetCurrent();
          sb.Append("insert into template_category_project(tcp_name,tcp_sort,tcp_enableshow) VALUES('" + pro.tcp_name + "',"+pro.tcp_sort+",'"+pro.tcp_enableshow+"' )");
          sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('insert','template_category_project', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + pro.ToJson() + "',IDENT_CURRENT('template_category_project'),'" + pro.tcp_name + "')");
          
          return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
      }
      /// <summary>
      /// 删除项目
      /// </summary>
      /// <param name="id">项目自增id</param>
      /// <returns></returns>
      public int DeleteProject(int id) 
      {
          var userinfo = OperatorProvider.Provider.GetCurrent();
          StringBuilder sb = new StringBuilder();
          project data = GetProjectInfo(id);
          sb.Append("delete from template_category_project where tcp_id=" + id);
          sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('delete','template_category_project', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "'," + data.tcp_id + ",'" + data.tcp_name + "')");
          return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
      }
    }
}
