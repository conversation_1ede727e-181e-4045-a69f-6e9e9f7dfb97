﻿using MarketingShops.Model.Goods.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Draw
{
   public class draw_config:goods_dat_sku_entity
    {
       /// <summary>
       /// 自增id
       /// </summary>
       public int c_id { get; set; }
       /// <summary>
       /// 配置名称
       /// </summary>
       public string c_name { get; set; }
       /// <summary>
       /// 活动id
       /// </summary>
       public int d_id { get; set; }
       /// <summary>
       /// 规格id
       /// </summary>
       public string sku_id { get; set; }
       /// <summary>
       /// 抽取数量
       /// </summary>
       public int drawnum { get; set; }
       /// <summary>
       /// 抽取模式
       /// </summary>
       public int drawmodel { get; set; }

     
       /// <summary>
       /// 优先指定数量
       /// </summary>
       public int appnum { get; set; }
       /// <summary>
       /// 配置时间
       /// </summary>
       public DateTime configtime { get; set; }

       /// <summary>
       /// 模式id
       /// </summary>
       public int model_id { get; set; }
       /// <summary>
       /// 抽取模式名称
       /// </summary>
       public string model_name { get; set; }



    }
}
