﻿using MarketingShops.BLL.Miniapp.trade;
using MarketingShops.DAL.trade;
using MarketingShops.Model.trade.baseTable;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Data;
using System.Data.OleDb;
using MarketingShops.Model.Goods.enumTable;

namespace MarketingShops.BLL.trade
{
   public class trade_freight_bll
    {
       trade_freight_dal _dal = new trade_freight_dal();
       trade_mini_bll mini_bll = new trade_mini_bll();
        /// <summary>
        /// 获取物流信息
        /// </summary>
        /// <param name="orderid">订单号</param>
        /// <returns></returns>
        public buy_bill_mail GetFreightInfo(string mail_no)
        {
            return _dal.GetFreightInfo(mail_no);
        }

        /// <summary>
        /// 获取物流公司数据
        /// </summary>
        /// <returns></returns>
        public List<good_enum_freight> GetCompanyList()
        {
            return _dal.GetCompanyList();
        }
        /// <summary>
        /// 修改物流信息
        /// </summary>
        /// <param name="mail">物流信息</param>
        /// <returns></returns>
        public int UpdateMailInfo(buy_bill_mail mail)
        {
            int Result = _dal.UpdateMailInfo(mail);
            if (Result > 0)
            {
                trade_mini_bll.MimiApp(mail.orderid, null);
            }
            return Result;
        }
        /// <summary>
        /// 发货(添加物流信息)
        /// </summary>
        /// <param name="mail">物流信息</param>
        /// <returns></returns>
        public int AddMailInfo(buy_bill_mail mail)
        {
            long time = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
            mail.mail_no = "M" + time.ToString().Substring(2, time.ToString().Length - 2);
            int Result = _dal.AddMailInfo(mail);
            if (Result > 0)
            {
                trade_mini_bll.MimiApp(mail.orderid, null);
            }
            return Result;
        }
        /// <summary>
        /// 批量发货
        /// </summary>
        /// <param name="path">文件路径</param>
        /// <returns></returns>
        public int BatchDeliver(string path)
        {
            try
            {
                if (!FileDownHelper.FileExists(path)) throw new Exception("文件" + path + "不存在");
                //解析Excel文件
                List<buy_bill_mail> deliverList = ExcelToDS(path);
                //获取已经提交的物理数据
                List<buy_bill_mail> mailList = _dal.GetMailList();
                //去除重复数据，已提交的数据无需再次提交
                for (int i = 0; i < deliverList.Count; i++)
                {
                    for (int j = 0; j < mailList.Count; j++)
                    {
                        if (deliverList[i].mail_number == mailList[j].mail_number)
                            deliverList.Remove(deliverList[i]);
                    }
                }
                if (deliverList.Count == 0) throw new Exception("excel文件解析失败");
                long time = 0;
                int Result = 0;
                //获取物流信息
                List<good_enum_freight> CompanyList = GetCompanyList();
                //当前匹配的物流信息
                good_enum_freight data = null;
                for (int i = 0; i < deliverList.Count; i++)
                {
                    deliverList[i].orderid = deliverList[i].orderid.Trim();
                    data = CompanyList.Find(j => j.mailName == deliverList[i].company);
                    if (data != null)
                        deliverList[i].company = data.freightno;
                    time = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
                    deliverList[i].mail_no = "M" + time.ToString().Substring(2, time.ToString().Length - 3) + i;
                }
                //开始更新数据库数据
                Result = _dal.BatchDeliver(deliverList);
                if (Result > 0)
                {
                    for (int i = 0; i < deliverList.Count; i++)
                    {
                        //同步小程序数据
                        trade_mini_bll.MimiApp(deliverList[i].orderid, null);
                    }
                }
                return Result;
            }
            catch (Exception ex) {
                throw new Exception(ex.Message);
            }
        }


        /// <summary>
        /// 解析Excel文件数据
        /// </summary>
        /// <param name="Path">文件路径</param>
        /// <returns></returns>
        public List<buy_bill_mail> ExcelToDS(string Path)
        {
            string strConn = "Provider=Microsoft.Ace.OleDb.12.0;" + "data source=" + Path + ";Extended Properties='Excel 12.0; HDR=OFF; IMEX=1'";
            OleDbConnection conn = null;
            OleDbDataAdapter myCommand = null;
            DataSet ds = null;
            try
            {
                conn = new OleDbConnection(strConn);
                conn.Open();
                string strExcel = "";
                //获取表名
                DataTable schemaTable = conn.GetOleDbSchemaTable(System.Data.OleDb.OleDbSchemaGuid.Tables, null);
                string tableName = schemaTable.Rows[0][2].ToString().Trim();
                strExcel = "select * from [" + tableName + "]";
                myCommand = new OleDbDataAdapter(strExcel, strConn);
                ds = new DataSet();
                myCommand.Fill(ds, "TradeInfo");
                List<buy_bill_mail> mail = new List<buy_bill_mail>();
                foreach (DataRow dr in ds.Tables[0].Rows)
                {
                    mail.Add(new buy_bill_mail { orderid = Convert.ToString(dr[0]), company = Convert.ToString(dr[1]), mail_number = Convert.ToString(dr[2]) });
                }
                return mail;
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
            finally
            {
                conn.Close();
                myCommand.Dispose();
                ds.Clear();
            }
        }
    }
}
