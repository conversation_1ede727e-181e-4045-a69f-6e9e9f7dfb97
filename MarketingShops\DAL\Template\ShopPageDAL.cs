﻿using MarketingShops.Model;
using MarketingShops.Model.Template.PageShopTemplate;
using MarketingShops.Model.Template.ShopPageTemplate;
using MarketingShops.Model.Template.ShopPageTemplate.search;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Template
{
   public class ShopPageDAL
   {
       #region 数据查询
       /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">页码信息</param>
        /// <returns></returns>
       public PageResultData GetGridJson(sell_search_params template, Pagination Page)
        {
            string variable = "TemplateType='page'";
            if (!string.IsNullOrEmpty(template.BTime) && !string.IsNullOrEmpty(template.ETime))
            {
                template.ETime += ":59";
                variable += " and BTime >='" + template.BTime.Trim() + "' and ETime<='" + template.ETime.Trim() + "'";
            }
            if (!string.IsNullOrEmpty(template.title))
                variable += " and title like '%" + template.title.Trim() + "%' ";

            string sql = "select {0} from dbo.Sell_Combination_project a join dbo.Sell_Combination_enum_state b on a.State=b.stateid join dbo.Sell_Combination_enum_template c on a.TemplateNo=c.TemplateNo where " + variable;
            return DbHelp_MarketingShops.paging<sell_search_data>(sql, "a.[KEY],a.title,a.banner,a.btime,a.etime,a.IsShowMember,b.statename,c.TemplateName", Page.sidx, Page.page, Page.rows);
        }
       /// <summary>
       /// 获取活动详情
       /// </summary>
       /// <param name="key">活动模板key</param>
       /// <returns></returns>
       public sell_edit_model GetActiveInfo(string key)
       {
           sell_edit_model pagetemplate = new sell_edit_model();
           pagetemplate = DbHelp_MarketingShops.ado.GetDataSingle<sell_edit_model>(@"SELECT [KEY]
      ,[TemplateNo]
      ,[Banner]
      ,[IsShowMember]
      ,[State]
      ,[BTime]
      ,[ETime]
      ,[title]
      ,[Describe]
  FROM [MarketingShops].[dbo].[Sell_Combination_project] where [KEY]=@KEY", new { KEY = key });
           pagetemplate.GoodProject = DbHelp_MarketingShops.ado.GetDataSingle<Sell_Combination_goods_project>(@"SELECT [SCGPID],[TemplateNo],[Describe],[ItemCou] FROM [MarketingShops].[dbo].[Sell_Combination_goods_project] where [KEY]=@KEY", new { KEY = key });
           if (pagetemplate.GoodProject != null && pagetemplate.GoodProject.ItemCou > 0)
           {
               pagetemplate.GoodItems = DbHelp_MarketingShops.ado.GetDataList<Sell_Combination_goods_item>(@"SELECT [SCGIID],[g_id] ,[State] FROM [MarketingShops].[dbo].[Sell_Combination_goods_item] where [SCGPID]=@SCGPID and [State]!=4", new { SCGPID=pagetemplate.GoodProject.SCGPID });
               List<GoodList> GoodList = GetGood();
               for (int i = 0; i < pagetemplate.GoodItems.Count; i++)
               {
                   for (int j = 0; j < GoodList.Count; j++)
                   {
                       if (GoodList[j].g_id == pagetemplate.GoodItems[i].g_id)
                           pagetemplate.GoodItems[i].name = GoodList[j].name;
                   }
               }
           }
           pagetemplate.Luck = GetLuckList(key);
           return pagetemplate;
       }

       /// <summary>
       /// 获取组合购信息
       /// </summary>
       /// <param name="KEY">组合购key</param>
       /// <returns></returns>
       public Sell_Combination_project GetSellCombinationInfo(string KEY) 
       {
           return DbHelp_MarketingShops.ado.GetDataSingle<Sell_Combination_project>(@"SELECT [KEY]
      ,[TemplateNo]
      ,[IsShowMember]
      ,[State]
      ,[BTime]
      ,[ETime]
      ,[title]
      ,[Describe],[Banner]
  FROM [MarketingShops].[dbo].[Sell_Combination_project] where [KEY]=@KEY", new { KEY = KEY });
       }
       /// <summary>
       /// 获取商品编号
       /// </summary>
       /// <param name="KEY">组合购key</param>
       /// <returns></returns>
       public List<string> GetGID(string KEY)
       {
           return DbHelp_MarketingShops.ado.GetDataList<string>(@"select g_id from dbo.Sell_Combination_goods_item where SCGPID in(select SCGPID from Sell_Combination_goods_project where [KEY]='" + KEY + "') and [State]!=4");
       }
       /// <summary>
       ///  获取抽签信息
       /// </summary>
       /// <param name="KEY">组合购key</param>
       /// <returns></returns>
       public Sell_Combination_luck GetLuckList(string KEY)
       {
           return DbHelp_MarketingShops.ado.GetDataSingle<Sell_Combination_luck>(@"SELECT [Pid],[Describe],[TemplateNo],[WinDescribe],[BTime],[ETime] FROM [MarketingShops].[dbo].[Sell_Combination_luck] where [KEY]=@KEY", new { KEY = KEY });
       }
        /// <summary>
        /// 获取模板
        /// </summary>
        /// <returns></returns>
        public List<Template_Enum> GetTemplate()
        {
            return DbHelp_MarketingShops.ado.GetDataList<Template_Enum>("select * from Sell_Combination_enum_template");
        }
       /// <summary>
       /// 获取商品信息
       /// </summary>
       /// <returns></returns>
        public List<GoodList> GetGood()
        {
            return DbHelp_MarketingShops.ado.GetDataList<GoodList>("select g_id,name from goods_dat_entity where state=0");
        }
       /// <summary>
       /// 获取活动状态
       /// </summary>
       /// <returns></returns>
        public List<State_Enum> GetState() 
        {
            return DbHelp_MarketingShops.ado.GetDataList<State_Enum>("select * from Sell_Combination_enum_state");
        }
       #endregion

        #region  数据操作
        /// <summary>
       /// 添加组合购
       /// </summary>
        /// <param name="Combination">组合购信息</param>
       /// <returns></returns>
        public int AddActive(sell_edit_model Combination, bool start_up) 
        {
            var data = new
            {
                //项目表
                KEY = Combination.KEY.Trim(),
                TemplateNo = Combination.TemplateNo.Trim(),
                Title = Combination.Title.Trim(),
                Banner = Combination.Banner.Trim(),
                IsShowMember = Combination.IsShowMember,
                State = Combination.State,
                BTime = Combination.BTime,
                ETime = Combination.ETime,
                Describe = Combination.Describe.Trim(),
                //商品售卖项目表
                SCGPID = Combination.GoodProject.SCGPID,
                ShopDescribe = Combination.GoodProject.Describe.Trim(),
                ItemCou = Combination.GoodProject.ItemCou,
                ShopTemplateNo = Combination.GoodProject.TemplateNo.Trim()
            };
            StringBuilder sb = new StringBuilder(" SET XACT_ABORT OFF  BEGIN TRAN ");
            //项目表
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[Sell_Combination_project]
           ([KEY]
           ,[TemplateNo]
           ,[Title]
           ,[Banner]
           ,[IsShowMember]
           ,[State]
           ,[BTime]
           ,[ETime]
           ,[Describe])
     VALUES
           (@KEY
           ,@TemplateNo
           ,@Title
           ,@Banner
           ,@IsShowMember
           ,@State
           ,@BTime
           ,@ETime
           ,@Describe)");
            //商品售卖项目表
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[Sell_Combination_goods_project]
           ([SCGPID]
           ,[KEY]
           ,[Describe]
           ,[ItemCou]
           ,[TemplateNo])
     VALUES
           (@SCGPID
           ,@KEY
           ,@ShopDescribe
           ,@ItemCou
           ,@ShopTemplateNo)");

            //商品售卖明细表
            for (int i = 0; i < Combination.GoodItems.Count; i++) 
            {
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[Sell_Combination_goods_item]([SCGPID] ,[g_id] ,[State]) VALUES(@SCGPID ,'" + Combination.GoodItems[i].g_id + "',1)");
            }
            //抽奖信息
            if (start_up && Combination.Luck!=null)//启用
            {
                sb.Append(@"INSERT INTO [Sell_Combination_luck] ( [KEY] ,[Pid] ,[Describe] ,[TemplateNo] ,[WinDescribe] ,[BTime] ,[ETime] ) VALUES (@KEY,"+Combination.Luck.Pid+",'"+Combination.Luck.Describe+"',"+Combination.Luck.TemplateNo+",'"+Combination.Luck.WinDescribe+"','"+Combination.Luck.BTime+"','"+Combination.Luck.ETime+"')");
            }
            sb.Append(" COMMIT TRAN");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString(), data);
        }
       /// <summary>
        /// 修改组合购
       /// </summary>
        /// <param name="Combination">组合购信息</param>
       /// <returns></returns>
        public int EditActive(sell_edit_model Combination, bool start_up) 
        {
            var data = new
            {
                //项目表
                KEY = Combination.KEY.Trim(),
                TemplateNo = Combination.TemplateNo.Trim(),
                Title = Combination.Title.Trim(),
                Banner = Combination.Banner.Trim(),
                IsShowMember = Combination.IsShowMember,
                State = Combination.State,
                BTime = Combination.BTime,
                ETime = Combination.ETime,
                Describe = Combination.Describe.Trim(),
                //商品售卖项目表
                SCGPID = Combination.GoodProject.SCGPID,
                ShopDescribe = Combination.GoodProject.Describe.Trim(),
                ItemCou = Combination.GoodProject.ItemCou,
                ShopTemplateNo = Combination.GoodProject.TemplateNo.Trim()
            };
            StringBuilder sb = new StringBuilder(" SET XACT_ABORT OFF  BEGIN TRAN ");
            //项目表
            sb.Append(@"update [MarketingShops].[dbo].[Sell_Combination_project] set [TemplateNo]=@TemplateNo,[State]=@State,[BTime]=@BTime,[ETime]=@ETime,[title]=@Title,[Describe]=@Describe,[Banner]=@Banner,[IsShowMember]=@IsShowMember where [KEY]=@KEY");
           //商品售卖表
            sb.Append(@" update [MarketingShops].[dbo].[Sell_Combination_goods_project] set [TemplateNo]=@ShopTemplateNo,[ItemCou]=@ItemCou,[Describe]=@ShopDescribe where [SCGPID]=@SCGPID");
            //商品售卖明细表
            for (int i = 0; i < Combination.GoodItems.Count; i++)
            {
                if (Combination.GoodItems[i].SCGIID > 0)
                    sb.Append(@" update [MarketingShops].[dbo].[Sell_Combination_goods_item] set [State]=" + Combination.GoodItems[i].State + " where [SCGIID]=" + Combination.GoodItems[i].SCGIID + "");
                else
                {
                    //检测当前添加的商品是否存在，存在则修改商品状态，不存在则添加
                    sb.Append(@" if exists(select * from Sell_Combination_goods_item where SCGPID='" + Combination.GoodProject.SCGPID + "' and g_id='"+Combination.GoodItems[i].g_id+"')");
                    sb.Append(@" update [MarketingShops].[dbo].[Sell_Combination_goods_item] set [State]=" + Combination.GoodItems[i].State + " where  SCGPID='" + Combination.GoodProject.SCGPID + "' and g_id='" + Combination.GoodItems[i].g_id + "' ");
                    sb.Append(@" else ");
                    sb.Append(@" INSERT INTO [MarketingShops].[dbo].[Sell_Combination_goods_item]([SCGPID] ,[g_id] ,[State]) VALUES(@SCGPID ,'" + Combination.GoodItems[i].g_id + "',1)");
                }
            }
            //抽奖信息
            if (start_up && Combination.Luck != null)//启用
            {
                sb.Append(" if exists(select *from [Sell_Combination_luck] where [KEY] = @KEY)");
                sb.Append(@" update [Sell_Combination_luck] set [Pid] = " + Combination.Luck.Pid + ",[Describe] = '" + Combination.Luck.Describe + "',[TemplateNo] = " + Combination.Luck.TemplateNo + ",[WinDescribe] ='" + Combination.Luck.WinDescribe + "',[BTime] = '" + Combination.Luck.BTime + "',[ETime] = '" + Combination.Luck.ETime + "' where [KEY] = @KEY ");
                sb.Append(" else");
                sb.Append(@" INSERT INTO [Sell_Combination_luck] ( [KEY] ,[Pid] ,[Describe] ,[TemplateNo] ,[WinDescribe] ,[BTime] ,[ETime] ) VALUES (@KEY," + Combination.Luck.Pid + ",'" + Combination.Luck.Describe + "'," + Combination.Luck.TemplateNo + ",'" + Combination.Luck.WinDescribe + "','" + Combination.Luck.BTime + "','" + Combination.Luck.ETime + "')");
            }
            else//不启用
            {
                sb.Append(@" delete from [Sell_Combination_luck] where [KEY] = @KEY");
            }
            sb.Append(" COMMIT TRAN");
           return DbHelp_MarketingShops.ado.OperationData(sb.ToString(),data);
        }
       
       /// <summary>
        /// 删除组合购
       /// </summary>
        /// <param name="key">组合购key</param>
       /// <returns></returns>
        public int DeleteActive(string key) 
        {
            string SCGPID = DbHelp_MarketingShops.ado.GetDataSingle<string>(@"SELECT [SCGPID] FROM [MarketingShops].[dbo].[Sell_Combination_goods_project] where [KEY]=@KEY ", new { KEY =key});
            StringBuilder sb = new StringBuilder("delete from Sell_Combination_project where [KEY]=@KEY ");
            sb.Append("delete from Sell_Combination_goods_item where [SCGPID]=@SCGPID ");
            sb.Append("delete from Sell_Combination_goods_project where [KEY]=@KEY");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString(), new { KEY = key, SCGPID = SCGPID });
        }
        #endregion
   }

}
