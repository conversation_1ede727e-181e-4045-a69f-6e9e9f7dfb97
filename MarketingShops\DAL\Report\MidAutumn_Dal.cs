﻿using MarketingShops.Model;
using MarketingShops.Model.Report;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Report
{
   public class MidAutumn_Dal
    {
       public PageResultData GetGridJson(string seltj, Pagination Page)
        {
            //string sql = "select {0} from MoonCakeReport where " + seltj;
            //return DbHelp_MarketingShops.paging<MidAutumnModel>(sql, "*", Page.sidx, Page.page, Page.rows);
            string sql = @"select {0} from(
select g_name ,SUM(buy_num) as num from buy_bill_detailed group by g_name,orderid having " + seltj + ")as a group by g_name";
            return DbHelp_MarketingShops.paging<MidAutumnModel>(sql, "g_name, SUM(num) as num", Page.sidx, Page.page, Page.rows);
        }

       public List<MidAutumnModel> ExportMidAutumnData(string seltj)
       {
           //根据数量排序
           return DbHelp_MarketingShops.ado.GetDataList<MidAutumnModel>("select * from MoonCakeReport where " + seltj + " order by num desc");
       }

       
    }
}
