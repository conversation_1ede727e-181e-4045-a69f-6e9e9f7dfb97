﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.RefundTardeDetailed.Search
{
    /// <summary>
    /// 账单退款明细model
    /// </summary>
    public class refund_bill_detailed
    {
        #region models
        private string _orderid;
        private string _rState;
        private int _number;
        private int _fee;
        private int _integral;
        private int _recharge;
        private string _opName;
        private DateTime _opTime;
        private int _refundState;

        /// <summary>
        /// 订单号
        /// </summary>
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }


        /// <summary>
        /// 退款状态编号
        /// </summary>
        public int refundState
        {
            set { _refundState = value; }
            get { return _refundState; }
        }


        /// <summary>
        /// 退款状态
        /// </summary>
        public string rState
        {
            get
            {
                switch (refundState)
                {
                    case 1:
                        _rState = "全额退款";
                        break;
                    case 2:
                        _rState = "部分退款";
                        break;
                    case 3:
                        _rState = "换货";
                        break;
                }
                return _rState;
            }
        }

        /// <summary>
        /// 退款数量
        /// </summary>
        public int number
        {
            set { _number = value; }
            get { return _number; }
        }

        /// <summary>
        /// 退款金额
        /// </summary>
        public int fee
        {
            set { _fee = value; }
            get { return _fee; }
        }

        /// <summary>
        /// 退款积分
        /// </summary>
        public int integral
        {
            set { _integral = value; }
            get { return _integral; }
        }


        /// <summary>
        /// 退款总充值
        /// </summary>
        public int recharge
        {
            set { _recharge = value; }
            get { return _recharge; }
        }

        /// <summary>
        /// 操作人
        /// </summary>
        public string opName
        {
            set { _opName = value; }
            get { return _opName; }
        }


        /// <summary>
        /// 退款时间
        /// </summary>
        public DateTime opTime
        {
            set { _opTime = value; }
            get { return _opTime; }
        }

        #endregion
    }
}
