﻿using MarketingShops.DAL;
using MarketingShops.Model;
using MarketingShops.Model.User;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL
{
    public class user_bll
    {
        user_dal _dal = new user_dal();
        /// <summary>
        /// 获取用户数据
        /// </summary>
        /// <param name="p_id"></param>
        /// <returns></returns>
        public List<userinfo> GetUserData(userinfo data)
        {
            return _dal.GetUserData(GetDataTj(data));
        }
        /// <summary>
        /// 获取用户数据_分页
        /// </summary>
        /// <param name="seltj"></param>
        /// <param name="Page"></param>
        /// <returns></returns>
        public PageResultData GetUserData(userinfo data, Pagination Page)
        {
            return _dal.GetUserData(GetDataTj(data), Page);
        }

        /// <summary>
        /// 查询条件
        /// </summary>
        /// <returns></returns>
        public string GetDataTj(userinfo data)
        {
            StringBuilder sb = new StringBuilder("1=1");
            if (!string.IsNullOrEmpty(data.openid))
            {
                sb.Append(" and openid = '");
                sb.Append(data.openid);
                sb.Append("'");
            }
            if (!string.IsNullOrEmpty(data.phone))
            {
                sb.Append(" and phone like '%");
                sb.Append(data.phone);
                sb.Append("%'");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 插入用户数据
        /// </summary>
        /// <returns></returns>
        public ResultModel AddUser(userinfo data)
        {
            ResultModel result = new ResultModel();
            try
            {
                result = CheckData(data);
                if (result.MsgState == 1) //检测通过
                {
                    result.MsgState = _dal.AddUserData(data);
                    if (result.MsgState >= 1) //发生变化的数据
                    {
                        result.Msg = "添加成功";
                    }
                    else {
                        result.Msg = "添加失败";
                    }
                }
                

            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }

            return result;
        }


        /// <summary>
        /// 修改用户数据
        /// </summary>
        /// <returns></returns>
        public ResultModel UpUser(userinfo data)
        {
            ResultModel result = new ResultModel();
            try
            {
                result = CheckData(data);
                if (result.MsgState == 1) //检测通过
                {
                    result.MsgState = _dal.UpUserData(data);
                    if (result.MsgState >= 1) //发生变化的数据
                    {
                        result.Msg = "修改成功";
                    }
                    else
                    {
                        result.Msg = "修改失败";
                    }
                }


            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }

            return result;
        }
        /// <summary>
        /// 检查数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public ResultModel CheckData(userinfo data)
        {
            ResultModel result = new ResultModel();
            if (string.IsNullOrEmpty(data.openid)) {
                result.Msg = "openid is NULL";
            }
            else if (string.IsNullOrEmpty(data.unionid))
            {
                result.Msg = "unionid is NULL";
            }
            else {
                result.MsgState = 1;//检测成功
            }
            return result;
        
        }
 
    }
}
