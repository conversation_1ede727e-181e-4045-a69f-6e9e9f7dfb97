﻿using MarketingShops.DAL;
using MarketingShops.Model;
using MarketingShops.Model.Category;
using MarketingShops.Model.Goods;
using MarketingShops.Model.Goods.enumTable;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace MarketingShops.BLL
{
    public class goods_bll
    {
        private static goods_dal _dal = new goods_dal();
        #region  数据查询
        /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="pagination">页码信息</param>
        /// <param name="searchModel">查询条件</param>
        /// <returns></returns>
        public PageResultData GetGoodsInfo(Pagination pagination, goods_search_params searchModel)
        {
            return _dal.GetGoodsInfo(searchModel, pagination);
        }
       /// <summary>
        /// 查询分类信息（树状结构）
       /// </summary>
       /// <param name="name">商品名称</param>
       /// <param name="price1">价格</param>
        /// <param name="price2">价格</param>
       /// <param name="time">时间</param>
       /// <returns></returns>
        public good_table GetJsonInfo(string name, int price1, int price2, string time)
        {
            return _dal.GetJsonInfo(name, price1, price2, time);
        }
        #endregion

        /// <summary>
        /// 获取商品信息
        /// </summary>
        /// <param name="id">商品id</param>
        /// <returns></returns>
        public GetGoodModel GetGoodInfo(string id) 
        {
            return _dal.GetGoodInfo(id);
        }

        /// <summary>
        /// 获取对应分类的栏目信息
        /// </summary>
        /// <param name="f_id">分类编号</param>
        /// <returns>对应栏目数据集合</returns>
        public List<good_fill> GetGoodsNext(string f_id) 
        {
            templatedata_model data = _dal.GetGoodsNext(f_id);
            List<good_fill> TemplateData = new List<good_fill>();
            List<good_item> GoodItemList = null;
            good_fill TemplateItem = null;
            for (int i = 0; i < data.ConfigData.Count; i++)
            {
                GoodItemList = new List<good_item>();
                TemplateItem = new good_fill();
                for (int j = 0; j < data.ItemInfo.Count; j++)
                {
                    if (data.ConfigData[i].tcc_id == data.ItemInfo[j].tcc_id)
                    {
                        GoodItemList.Add(data.ItemInfo[j]);
                    }
                }
                #region  数据回绑
                TemplateItem.itemlist = GoodItemList;
                TemplateData.Add(TemplateItem);
                TemplateData[i].tcc_desc = data.ConfigData[i].tcc_desc;
                TemplateData[i].tcpj_id = data.ConfigData[i].tcpj_id;
                TemplateData[i].tcp_id = data.ConfigData[i].tcp_id;
                TemplateData[i].tcc_id = data.ConfigData[i].tcc_id;
                TemplateData[i].tci_id = data.ConfigData[i].tci_id;
                TemplateData[i].tcpj_enableshow = data.ConfigData[i].tcpj_enableshow;
                TemplateData[i].tcpj_sku = data.ConfigData[i].tcpj_sku;
                TemplateData[i].tcp_name = data.ConfigData[i].tcp_name;
                TemplateData[i].tcc_name = data.ConfigData[i].tcc_name;
                TemplateData[i].tci_name = data.ConfigData[i].tci_name;
                #endregion

                #region 获取对应字典数据
                for (int j = 0; j < TemplateData[i].itemlist.Count; j++)
                {
                    TemplateData[i].itemlist[j].dictionarylist = new List<item_dictionary>();
                    for (int l = 0; l < data.DictionaryInfo.Count; l++)
                    {
                        if (TemplateData[i].itemlist[j].tci_id == data.DictionaryInfo[l].tci_id)
                        {
                            TemplateData[i].itemlist[j].dictionarylist.Add(data.DictionaryInfo[l]);
                        }
                    }
                }
                #endregion
            }
            return TemplateData;
        }

        /// <summary>
        /// 商品发布下拉框数据
        /// </summary>
        /// <returns>商户类型、系统分类、有效期模板</returns>
        public object GetEnumData()
        {
            //商户信息
            List<good_enum_merchanty> MerchantList = _dal.GetEnumData<good_enum_merchanty>("merchant_entity");
            //系统分类
            List<good_enum_system> SystemList = _dal.GetEnumData<good_enum_system>("sys_class_enum");
            //邮寄公司
            List<good_enum_freight> FreightList = _dal.GetEnumData<good_enum_freight>("mail_freight_project");
            
            return new
            {
                MerchantList,
                SystemList,
                FreightList
            };
        }
        /// <summary>
        /// 获取栏目数据
        /// </summary>
        /// <returns></returns>
        public List<good_enum_column> GetColoumData()
        {
            return _dal.GetColoumData(); ;
        }
       /// <summary>
        /// 新增/编辑商品
       /// </summary>
       /// <param name="model">商品信息</param>
       /// <returns></returns>
        public int AddOrEditGoodInfo(EditGoodModel model, string keyValue)
        {
            try
            {
                int Result = 0;
                if (string.IsNullOrEmpty(keyValue))
                {
                    try
                    {
                        #region  动态赋值相关编号
                        //商品数据
                        model.goods.g_id = "G" + GenerateStringID(2);
                        model.child.gd_id = "D" + GenerateStringID();
                       
                        Random rd = new Random();
                        //sku数据
                        for (int i = 0; i < model.sku.Count; i++)
                        {
                            model.sku[i].sku_id = "SK" + GenerateStringID(2) + rd.Next(100);
                            //去掉“,”
                            model.sku[i].name = model.sku[i].name.Replace(",", "");
                            model.sku[i].paynum = model.sku[i].paymentMethod == null ? 0 : model.sku[i].paymentMethod.Count;
                        }
                        model.child.sku_json = model.sku.ToJson();
                        model.spu.spu_id = "SP" + GenerateStringID(); 
                        #endregion
                        Result= _dal.AddGoodInfo(model);                        
                    }
                    catch (Exception ex) { throw new Exception(ex.Message); }
                }
                else
                {
                    Random rd = new Random();
                    string gid = model.goods.g_id;
                    int count = 0;
                    for (int i = 0; i < model.sku.Count; i++)
                    {
                        count = model.sku.FindAll(j => j.name.Replace(",", "") == model.sku[i].name.Replace(",", "")).Count();
                        if (count > 1)
                            throw new Exception("sku规格存在重复数据");
                        if (string.IsNullOrEmpty(model.sku[i].sku_id))
                        {
                            model.sku[i].sku_id = "SK" + GenerateStringID(2) + rd.Next(100);
                        }
                        model.sku[i].paynum = model.sku[i].paymentMethod == null ? 0 : model.sku[i].paymentMethod.Count;
                    //去掉“,”
                        model.sku[i].name = model.sku[i].name.Replace(",", "");
                    }
                    model.child.sku_json = model.sku.ToJson();
                    if (model.spulist != null)
                    {
                        model.spu.spu_list_json = model.spulist.ToJson();
                        model.spu.spu_count = model.spulist.Count;
                    }
                    Result = _dal.EditGoodInfo(model, keyValue.Trim());
                }
                if (Result > 0)
                {
                    //同步小程序api接口
                    try
                    {
                        var param = GetGoodInfo(model.goods.g_id.Trim());
                        string str = param.ToJson();
                        NFine.Code.Miniapp.cloud.CloudDBManage clond = new NFine.Code.Miniapp.cloud.CloudDBManage();
                        clond.db = "goods";
                        if (string.IsNullOrEmpty(keyValue))
                            clond.add(str);
                        else
                            clond.update(str, "{\"goods.g_id\": \"" + model.goods.g_id.Trim() + "\"}");
                    }
                    catch (Exception ex)
                    {
                        throw new Exception("数据操作成功，小程序同步失败" + ex.Message);
                    }
                }
                return Result;
                   
            }
            catch(Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
        /// <summary>
        /// 删除规格
        /// </summary>
        /// <param name="sku_id">规格编号</param>
        /// <param name="g_id">商品编号</param>
        /// <param name="jsonlist">sku_json</param>
        /// <returns></returns>
        public int DeleteSKUInfo(string sku_id, string g_id, string jsonlist) 
        {
            return _dal.DeleteSKUInfo(sku_id, g_id, jsonlist.Trim());
        }

        /// <summary>
        /// 生成唯一编号
        /// </summary>
        /// <param name="type">生成类型</param>
        /// <returns></returns>
        private string GenerateStringID(int type = 1)
        {
            string str = "";
            if (type == 1)
            {
                //根据guid生成唯一编号
                str = Guid.NewGuid().ToString("N");
                str = str.Substring(0, str.Length - 24).ToUpper();
            }
            else if (type == 2)
            {
                ///根据时间戳生成唯一编号
                long time = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
                str = time.ToString().Substring(4, time.ToString().Length - 4);
            }
            return str;

        }
    }
}
