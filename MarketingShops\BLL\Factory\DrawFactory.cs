﻿using MarketingShops.BLL.DrawModel;
using MarketingShops.Model.Draw;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.Factory
{
    /// <summary>
    /// 抽取工厂
    /// </summary>
    public class DrawFactory
    {
        DrawModelBase dmb;

        /// <summary>
        /// 获取数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public DrawModelBase GetData(draw_config data)
        {
            draw_enum_model_enum drem = GetDrawModel(data);
            switch (drem)
            {
                case draw_enum_model_enum.随机抽取:
                    dmb = new DrawModelRandom();
                    break;
                case draw_enum_model_enum.优先抽取:
                    dmb = new DrawModelFirst();
                    break;

            }
            dmb.data = data;
            dmb.modelenum = drem;
            return dmb;
        
        }


        /// <summary>
        /// 获取抽取模式
        /// </summary>
        /// <returns></returns>
        public draw_enum_model_enum GetDrawModel(draw_config data) {
            draw_enum_model_enum drem = draw_enum_model_enum.随机抽取;
            if(data.drawmodel ==2)
                drem = draw_enum_model_enum.优先抽取;
            else if(data.drawmodel ==3)
                drem = draw_enum_model_enum.混合抽取;
            else if (data.drawmodel == 4)
                drem = draw_enum_model_enum.指定抽取;
            return drem;
        }


    }
}
