﻿using MarketingShops.DAL.RefundTarde;
using MarketingShops.Model;
using MarketingShops.Model.Therefunddetail.Search;
using NFine.Code;

namespace MarketingShops.BLL.RefundTarde
{
    /// <summary>
    /// 退款明细bll
    /// </summary>
    public class therefunddetail_bll
    {
        therefunddetail_dal _dal = new therefunddetail_dal();


        /// <summary>
        /// 系统查询
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <param name="Page">分页信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(the_refund_detail sys, Pagination Page, string BTime, string ETime, int? discount_fee1, int? discount_fee2)
        {
            string seltj = GetSearchStr(sys, BTime, ETime, discount_fee1, discount_fee2);
            return _dal.GetGridInfo(seltj, Page);
        }


        #region 查询数据条件
        /// <summary>
        /// 获取查询SQL语句
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <returns></returns>
        string GetSearchStr(the_refund_detail sys, string BTime, string ETime, int? discount_fee1, int? discount_fee2)
        {
            string seltj = "isRefund= 1 ";
            if (!string.IsNullOrEmpty(sys.orderid))//订单号模糊查询
                seltj += "and a.[orderid] like '%" + sys.orderid + "%'";

            if (sys.total_num > 0)//购买数量
                seltj += " and [total_num]='" + sys.total_num + "'";

            if (!string.IsNullOrEmpty(sys.phone))//顾客电话
                seltj += " and [phone]='" + sys.phone + "'";

            if (!string.IsNullOrEmpty(BTime) && !string.IsNullOrEmpty(ETime))//录入时间
            {
                ETime += ":59";//结束时间到当前选中的分
                seltj += " and a.[inTime] between '" + BTime.Trim() + "' and  '" + ETime.Trim() + "' ";
            }

            if (discount_fee1 >= 0 && discount_fee2 == null)//优惠金额
            {
                seltj += " and [discount_fee]>='" + discount_fee1 + "'";
            }

            if (discount_fee2 >= 0 && discount_fee1 == null)//优惠金额
            {
                seltj += " and [discount_fee]>='" + discount_fee1 + "'";
            }

            if (discount_fee1 >= 0 && discount_fee2 >= 0)//优惠金额
                seltj += " and [discount_fee] between " + discount_fee1 + " and " + discount_fee2 + " ";
            return seltj;
        }
        #endregion
    }
}
