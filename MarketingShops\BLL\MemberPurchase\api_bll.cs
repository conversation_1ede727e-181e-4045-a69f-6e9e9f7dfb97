﻿using MarketingShops.BLL.api;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.MemberPurchase
{
   public class api_bll
    {
       static GrouponApi api = new GrouponApi();
       /// <summary>
       /// 查询会员购api接口（分页查询）
       /// </summary>
       /// <param name="condition">查询条件</param>
       /// <param name="PageIndex">页码</param>
       /// <param name="PageSize">页数</param>
       /// <returns></returns>
       public static string GetDataList(string condition, int PageIndex,int PageSize)
       {
            api.baseurl="http://183.63.130.69:88/ExecUse/Index?Ex=Ex_NGrouponInfo" ;
            return api.GetGridJson(condition, PageIndex, PageSize); 
       }
       /// <summary>
       /// 获取会员购项目
       /// </summary>
       /// <returns></returns>
       public static string GetProjectList() {
           api.baseurl = "http://183.63.130.69:88/ExecUse/Index?Ex=Ex_NGroupon";
           return api.GetDataJosn("&t=0"); 
       }
       /// <summary>
       /// 获取会员购详情
       /// </summary>
       /// <param name="GrouonKey">会员购key</param>
       /// <returns></returns>
       public static string GetGrouponInfo(Guid GrouonKey)
       {
           api.baseurl = "http://183.63.130.69:88/ExecUse/Index?Ex=Ex_NGrouponInfo";
           return api.GetDataJosn("&t=0&GrouponKey=" + GrouonKey);
       }
       /// <summary>
       /// 获取会员购html模板
       /// </summary>
       /// <param name="GrouonKey">会员购key</param>
       /// <returns></returns>
       public static string GetGrouponHtmlInfo(Guid GrouonKey)
       {
           api.baseurl = "http://183.63.130.69:88/ExecUse/Index?Ex=Ex_NGrouponTemplate";
           return api.GetDataJosn("&t=0&GrouponKey=" + GrouonKey);
       }
       
    }
}
