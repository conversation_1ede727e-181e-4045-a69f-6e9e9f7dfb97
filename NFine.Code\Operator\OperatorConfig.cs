﻿using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace System
{
    /// <summary>
    /// 操作缓存信息
    /// </summary>
    public static class OperatorConfig
    {
        public static Dictionary<string, string> Site
        {
            set
            {
                _Site = value;
                WebHelper.WriteCookie("Sys_Site", DESEncrypt.Encrypt(value.ToJson()));

            }
            get
            {
                if (_Site == null)
                    return DESEncrypt.Decrypt(WebHelper.GetCookie("Sys_Site").ToString()).ToObject<Dictionary<string, string>>();
                else
                    return _Site;
            }
        }
        public static Dictionary<string, string> _Site { get; set; }


    }
}
