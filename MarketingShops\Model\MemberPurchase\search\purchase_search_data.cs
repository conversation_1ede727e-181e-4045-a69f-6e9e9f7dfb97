﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.MemberPurchase
{
    public class purchase_search_data
    {
       /// <summary>
       /// 组合购key
       /// </summary>
       public Guid? GrouponKey { get; set; }
       /// <summary>
       /// 项目key
       /// </summary>
       public Guid? ProjectKey { get; set; }
        /// <summary>
        /// 2020年四店店庆
        /// </summary>
        public string ProjectName { get; set; }
        /// <summary>
        /// 广州地区
        /// </summary>
        public string AreaName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int Total { get; set; }
        /// <summary>
        /// 【天河店】20元现金立减券
        /// </summary>
        public string GrouponName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double GoodsPrice { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double GrouponPrice { get; set; }
        /// <summary>
        /// 全天
        /// </summary>
        public string Period { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FdNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsUse { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime InputTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string Valid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ValidEnd { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string SaleEndTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CodeNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CodeSaleMin { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CodeSaleMax { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime NValid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime NValidEnd { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int UseAreaNo { get; set; }

        public string NValidStr {
            get {
                return NValid.ToShortDateString() + " 至 " + NValidEnd.ToShortDateString();
            }
        }
        public string GoodsPriceStr {
            get {
                return GoodsPrice + "、" + GrouponPrice;
            }
        }
        public string CodeNumberStr {
            get {
                return CodeNumber + "、" + CodeSaleMax + "、" + CodeSaleMin;
            }
        }
    }

 

}
