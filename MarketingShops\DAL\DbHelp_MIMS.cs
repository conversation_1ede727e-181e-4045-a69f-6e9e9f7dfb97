﻿using HDtool.ADO;
using MarketingShops.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL
{

    public static class DbHelp_MIMS
    {
         public static IAdo ado;
        public static SqlHelp sql;

        static DbHelp_MIMS()
        {



            InitConnectionModel("***********");

        }

        public static void InitConnectionModel(string IP, bool isconfig = false)
        {
            try
            {
                ado = new AdoCurrency(new SqlConnection("server=***********;User Id=sa;password=***********;Database=MIMS"));

            }
            catch (Exception e)
            {

                Console.WriteLine(e.Message);
            }

        }
        /// <summary>
        /// 分页
        /// </summary>
        /// <param name="sql">sql 语句</param>
        /// <param name="datatotal">总页码</param>
        /// <param name="pagesize">每页显示</param>
        /// <param name="pageindex">当前页码</param>
        /// <returns></returns>
        public static PageResultData paging<T>(string SQL, string param, string sort, int pageindex, int pagesize)
        {

            StringBuilder sb = new StringBuilder("select top " + pagesize + " * from (  ");
            sb.Append(string.Format(SQL, string.Format(" {0} {1}{2}", "", param, ",ROW_NUMBER() over(order by " + sort + ")as rownum")));
            sb.Append(")as tab where rownum>");
            sb.Append(((pageindex - 1) * pagesize));
            var Alldata = DbHelp_MarketingShops.ado.GetDataList<T>(sb.ToString());

            int datatotal = DbHelp_MarketingShops.ado.GetDataSingle<int>(string.Format(SQL, "count(1) "));

            int total = (int)Math.Ceiling(Convert.ToDouble(datatotal) / pagesize);//总页码数
            PageResultData data = new PageResultData
            {
                rows = Alldata,
                total = total,
                page = pageindex,
                records = datatotal
            };
            return data;
        }





    }
}
