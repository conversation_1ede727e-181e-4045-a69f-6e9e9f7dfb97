﻿using MarketingShops.DAL.Classification;
using MarketingShops.Model;
using MarketingShops.Model.Classification;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.Classification
{
   public class Classification_Bll
    {

       Classification_Dal _dal = new Classification_Dal();
       /// <summary>
       /// 查询分类信息
       /// </summary>
       /// <param name="keyword">名称</param>
       /// <returns></returns>
       public List<goods_classification> GetJsonInfo()
       {
           string variable = "1=1";//只查询有效的数据(F_EnabledMark=1)
           //if (!string.IsNullOrEmpty(keyword))
           //    variable += "and F_GrounponNo=(select F_GrounponNo from goods_classification where [F_FullName]='" + keyword.Trim() + "' )";
           return _dal.GetJsonInfo(variable);
       }

       /// <summary>
       /// 获取分类信息
       /// </summary>
       /// <param name="catId">自增编号</param>
       /// <returns></returns>
       public goods_classification GetClassificationInfo(string id) 
       {
           return _dal.GetClassificationInfo(id);
       }
       /// <summary>
       /// 获取有效数据
       /// </summary>
       /// <returns></returns>
       public List<goods_classification> GetEffectiveData()
       {
           return GetJsonInfo();
       }
       /// <summary>
       /// 新增分类
       /// </summary>
       /// <param name="str">分类信息</param>
       /// <returns></returns>
       public int SubmitClassification(goods_classification classification, string keyValue, string grouponNo)
       {
           try 
           {
               if (!string.IsNullOrEmpty(keyValue))
               {
                   classification.F_Id = new Guid(keyValue);
                   return _dal.UpdateClassification(classification);
               }
               else
               {
                   return _dal.InsertClassification(classification);
               }
           }
           catch (Exception ex) { throw new Exception("数据转化出错"+ex.Message); }
          
       }
       /// <summary>
       /// 删除分类信息
       /// </summary>
       /// <param name="id">分类id</param>
       /// <returns></returns>
       public int DeleteClassification(string id) 
       {
           return _dal.DeleteClassification(id);
       }
      
    }
}
