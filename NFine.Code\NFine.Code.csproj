﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{DB19CE03-C307-43FE-A209-08AA4AE10E21}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NFine.Code</RootNamespace>
    <AssemblyName>NFine.Code</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ComponentApplicationServiceInterface">
      <HintPath>\\dss\工作流\项目引用\ComponentApplicationServiceInterface.dll</HintPath>
    </Reference>
    <Reference Include="ICSharpCode.SharpZipLib, Version=0.86.0.518, Culture=neutral, PublicKeyToken=1b03e6acf1164f73, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.*******\lib\net40\ICSharpCode.SharpZipLib.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="log4net, Version=********, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.2.0.5\lib\net45-full\log4net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>Packages\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NPOI, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.*******\lib\net40\NPOI.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OOXML, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.*******\lib\net40\NPOI.OOXML.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXml4Net, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.*******\lib\net40\NPOI.OpenXml4Net.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="NPOI.OpenXmlFormats, Version=*******, Culture=neutral, PublicKeyToken=0df73ec7942b34e1, processorArchitecture=MSIL">
      <HintPath>..\packages\NPOI.*******\lib\net40\NPOI.OpenXmlFormats.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SERVICE.PROXY">
      <HintPath>\\dss\工作流\项目引用\SERVICE.PROXY.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Cache\Cache.cs" />
    <Compile Include="Cache\CacheFactory.cs" />
    <Compile Include="Cache\ICache.cs" />
    <Compile Include="Common.cs" />
    <Compile Include="Configs\Configs.cs" />
    <Compile Include="Configs\DBConnection.cs" />
    <Compile Include="Excel\ExcelHelper.cs" />
    <Compile Include="Excel\Model\StoreHeadCountModel.cs" />
    <Compile Include="Excel\Model\StoreReportModel.cs" />
    <Compile Include="Excel\NPOIExcel.cs" />
    <Compile Include="Export\ExportData.cs" />
    <Compile Include="Extend\Ext.Convert.cs" />
    <Compile Include="Extend\Ext.DateTime.cs" />
    <Compile Include="Extend\Ext.Format.cs" />
    <Compile Include="Extend\ExtLinq.cs" />
    <Compile Include="Extend\ExtList.cs" />
    <Compile Include="Extend\ExtLinq.SortBy.cs" />
    <Compile Include="Extend\ExtTable.cs" />
    <Compile Include="Extend\ExtList.Comparint.cs" />
    <Compile Include="File\FileDownHelper.cs" />
    <Compile Include="File\FileHelper.cs" />
    <Compile Include="File\FileUploadHelper.cs" />
    <Compile Include="GZip.cs" />
    <Compile Include="Json\Json.cs" />
    <Compile Include="Licence.cs" />
    <Compile Include="Log\Log.cs" />
    <Compile Include="Log\LogFactory.cs" />
    <Compile Include="Mail\MailHelper.cs" />
    <Compile Include="Miniapp\cloud\CloudDBManage.cs" />
    <Compile Include="Miniapp\miniCode\CreatMiniCode.cs" />
    <Compile Include="Net\HttpMethods.cs" />
    <Compile Include="Net\Net.cs" />
    <Compile Include="Operator\OperatorConfig.cs" />
    <Compile Include="Operator\OperatorLoginInfo.cs" />
    <Compile Include="Operator\OperatorModel.cs" />
    <Compile Include="Operator\OperatorProvider.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Security\DESEncrypt.cs" />
    <Compile Include="Security\Md5.cs" />
    <Compile Include="Serialize.cs" />
    <Compile Include="Service\Client\ClientBase.cs" />
    <Compile Include="Service\Client\PosClientService.cs" />
    <Compile Include="Service\Client\SaasClientService.cs" />
    <Compile Include="Service\Model\StoreUrlConfig.cs" />
    <Compile Include="Service\RPC.cs" />
    <Compile Include="UserPurview\PurviewType.cs" />
    <Compile Include="UserPurview\UserPurviewModel.cs" />
    <Compile Include="Validate\Validate.cs" />
    <Compile Include="VerifyCode.cs" />
    <Compile Include="Web\AjaxResult.cs" />
    <Compile Include="Web\TreeQuery.cs" />
    <Compile Include="Web\TreeGrid\TreeGrid.cs" />
    <Compile Include="Web\TreeGrid\TreeGridModel.cs" />
    <Compile Include="Web\Pagination.cs" />
    <Compile Include="Web\TreeView\TreeView.cs" />
    <Compile Include="Web\TreeView\TreeViewModel.cs" />
    <Compile Include="Web\Tree\TreeSelect.cs" />
    <Compile Include="Web\Tree\TreeSelectModel.cs" />
    <Compile Include="Web\WebHelper.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="ExtendCode\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="Packages\ComponentApplicationServiceInterface.dll" />
    <Content Include="Packages\SERVICE.PROXY.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>