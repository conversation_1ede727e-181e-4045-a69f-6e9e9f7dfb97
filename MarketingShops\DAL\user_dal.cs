﻿using MarketingShops.Model;
using MarketingShops.Model.User;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL
{
   public class user_dal
    {
        /// <summary>
        /// 获取用户数据
        /// </summary>
        /// <param name="p_id"></param>
        /// <returns></returns>
        public List<userinfo> GetUserData(string seltj)
        {

            string sql = ("select * from userinfo where " + seltj + " order by uptime    desc");
            return DbHelp_MarketingShops.ado.GetDataList<userinfo>(sql);
        }
        /// <summary>
        /// 获取用户数据_分页
        /// </summary>
        /// <param name="seltj"></param>
        /// <param name="Page"></param>
        /// <returns></returns>
        public PageResultData GetUserData(string seltj, Pagination Page)
        {
            string sql = (@"select {0} from userinfo where " + seltj + "");
            return DbHelp_MarketingShops.paging<userinfo>(sql, "*", Page.sidx, Page.page, Page.rows);
        }
 

        /// <summary>
        /// 添加用户数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public int AddUserData(userinfo data)
        {
            string sql = "insert into userinfo(openid,unionid,phone,nickname,avatarurl,ud_id,ud_count,memberkey,uptime) values ('" + data.openid + "','" + data.unionid + "','" + data.phone + "','" + data.nickname + "','" + data.avatarurl + "',NULL,0,NULL,getdate())";
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }


        /// <summary>
        /// 添加用户数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public int UpUserData(userinfo data)
        {
            string sql = "update userinfo set unionid='"+data.unionid+"',phone='"+data.phone+"',nickname='"+data.nickname+"',avatarurl='"+data.avatarurl+"'  where openid = '" + data.openid + "'";
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }

        /// <summary>
        /// 执行sql
        /// </summary>
        /// <param name="sql"></param>
        /// <returns></returns>
        public int ExecuteSql(string sql)
        {
            return DbHelp_MarketingShops.ado.OperationData(sql);
        }
       

    }
}
