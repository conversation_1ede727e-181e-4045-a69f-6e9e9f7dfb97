﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Classification
{
    public class goods_classification
    {
        /// <summary>
        /// 自增id
        /// </summary>
        public Guid? F_Id { get; set; }
        /// <summary>
        /// 父级id
        /// </summary>
        public Guid? F_ParentId { get; set; }
        /// <summary>
        /// 组名编号
        /// </summary>
        public Guid? F_GrounponNo { get; set; }
        /// <summary>
        /// 排序
        /// </summary>
        public int F_SortCode { get; set; }
        /// <summary>
        /// 当前子集拥有的商品数
        /// </summary>
        public int F_GoodTot { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string F_FullName { get; set; }
        /// <summary>
        /// 图片
        /// </summary>
        public string F_Img { get; set; }
        /// <summary>
        /// 是否展示
        /// </summary>
        public bool? F_IsShow { get; set; }
        /// <summary>
        /// 描述
        /// </summary>
        public string F_Description { get; set; }
        /// <summary>
        ///是否有效
        /// </summary>
        public bool? F_EnabledMark { get; set; }
        /// <summary>
        /// 层级数
        /// </summary>
        public int? F_Layers { get; set; }
        /// <summary>
        /// 项目id
        /// </summary>
        public int? F_TcpId { get; set; }

        ///// <summary>
        ///// 创建人
        ///// </summary>
        //public string F_CreatorUserId { get; set; }
        ///// <summary>
        ///// 创建时间
        ///// </summary>
        //public DateTime F_CreatorTime { get; set; }       
    }
}
