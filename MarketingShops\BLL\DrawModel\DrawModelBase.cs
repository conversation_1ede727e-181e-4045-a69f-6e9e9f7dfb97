﻿using MarketingShops.Model.Draw;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.DrawModel
{
    /// <summary>
    /// 抽取方法的基类
    /// </summary>
    public abstract class DrawModelBase
    {

        public draw_config data =null;
        //public List<draw_join> DataList = new List<draw_join>();
        /// <summary>
        /// 获取抽奖结果
        /// </summary>
        public abstract List<draw_win> GetWinData();

        /// <summary>
        /// 获取需抽取的基础数据
        /// </summary>
        /// <returns></returns>
        public List<draw_win> GetDrawBaseData()
        {
            List<draw_win> resultdata = new List<draw_win>();
            if (this.data != null)
            {
                draw_join_bll bll = new draw_join_bll();
                draw_win settjdata = new draw_win();
                settjdata.c_id = data.c_id;
                settjdata.d_id = data.d_id;
                resultdata = bll.GetDrawBaseData(settjdata);
            }
            return resultdata;
        }

        public draw_enum_model_enum modelenum;

        
    }
}
