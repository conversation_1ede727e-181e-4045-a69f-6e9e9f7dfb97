﻿using MarketingShops.DAL.MemberPurchase;
using MarketingShops.Model;
using MarketingShops.Model.MemberPurchase;
using MarketingShops.Model.MemberPurchase.baseTable;
using MarketingShops.Model.MemberPurchase.search;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.MemberPurchase
{
   public class base_bll
    {
       MemberPurchase_dal _dal = new MemberPurchase_dal();
        /// <summary>
        /// 系统查询
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <param name="Page">分页信息</param>
        /// <returns></returns>
       public PageResultData GetGridInfo(purchase_search_params sys, Pagination Page)
        {
            List<purchase_search_data> DataList = _dal.GetGridInfo(sys, Page);
            int datatotal = 0;
            if (DataList != null && DataList.Count > 0)
                datatotal = DataList[0].Total;
            int total = (int)Math.Ceiling(Convert.ToDouble(datatotal) / Page.rows);//总页码数
            PageResultData data = new PageResultData
            {
                rows = DataList,
                total = total,
                page = Page.page,
                records = datatotal
            };
            return data;
        }
        /// <summary>
        /// 获取会员购详情
        /// </summary>
        /// <param name="GrouponKey">会员购key</param>
        /// <returns></returns>
        public purchase_details GetGrouponInfo(Guid GrouponKey)
        {
            return _dal.GetGrouponInfo(GrouponKey);
        }
        /// <summary>
        /// 区域信息
        /// </summary>
        /// <returns></returns>
        public List<NAreaInfo> GetNAreaInfo()
        {
            return _dal.GetComboBoxDataList<NAreaInfo>("GrouponBase.dbo.NAreaInfo");
        }
       /// <summary>
       /// 门店信息
       /// </summary>
       /// <returns></returns>
        public List<NShopInfo> GetNShopInfo()
        {
            var data=_dal.GetComboBoxDataList<NShopInfo>("MIMS.dbo.ShopInfo");
            List<NShopInfo> fina = new List<NShopInfo>();
            if (data!=null && data.Count > 0)
            {
                fina = data.FindAll(i => i.IsUse);
            }
            return fina;
        }
        /// <summary>
        /// 商品标签
        /// </summary>
        /// <returns></returns>
        public List<NCardLable> GetNCardLable()
        {
            return _dal.GetComboBoxDataList<NCardLable>("GrouponBase.dbo.NCardLable");
        }
        /// <summary>
        /// 有效模式
        /// </summary>
        /// <returns></returns>
        public List<NValidModel> GetNValidModel()
        {
            return _dal.GetComboBoxDataList<NValidModel>("GrouponBase.dbo.NValidModel");
        }
        /// <summary>
        /// 售卖模式
        /// </summary>
        /// <returns></returns>
        public List<NSaleModel> GetNSaleModel()
        {
            return _dal.GetComboBoxDataList<NSaleModel>("GrouponBase.dbo.NSaleModel");
        }
        /// <summary>
        /// 商品类型
        /// </summary>
        /// <returns></returns>
        public List<NCardModel> GetNCardModel()
        {
            return _dal.GetComboBoxDataList<NCardModel>("GrouponBase.dbo.NCardModel");
        }
        /// <summary>
        /// 券码规则
        /// </summary>
        /// <returns></returns>
        public List<NBuildModel> GetNBuildModel()
        {
            return _dal.GetComboBoxDataList<NBuildModel>("GrouponBase.dbo.NBuildModel");
        }
        /// <summary>
        /// html模板
        /// </summary>
        /// <returns></returns>
        public List<NHtmlTemplate> GetNHtmlTemplate()
        {
            return _dal.GetComboBoxDataList<NHtmlTemplate>("GrouponBase.dbo.NHtmlTemplate");
        }
       
       /// <summary>
        /// 获取会员购项目
       /// </summary>
       /// <returns></returns>
        public List<projectModel> GetProjectList() {
            return _dal.GetProjectList();
        }
    }
}
