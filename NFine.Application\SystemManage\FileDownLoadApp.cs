﻿using MarketingShops.DAL;
using MarketingShops.Model;
using NFine.Code;
using NFine.Domain.Entity.SystemManage;
using NFine.Domain.IRepository.SystemManage;
using NFine.Repository.SystemManage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Application.SystemManage
{
    public class FileDownLoadApp
    {
         private IFileDownLoadRepository service = new FileDownLoadRepository();
        public int InsertForm(FileDownLoadEntity fileDownLoadEntity)
        {
            try
            {
                service.Insert(fileDownLoadEntity);
                return 1;
            }
            catch (Exception)
            {
                return 0;
            }
        }
        public PageResultData GetFileDownLoadList(Pagination pagination, string DealType, int State)
        {
            var UserCode = OperatorProvider.Provider.GetCurrent().UserCode;
            IQueryable<FileDownLoadEntity> query = null;
            try
            {
                query = service.IQueryable(x => x.OperatorCode == UserCode);   //查询本账号
            }
            catch (Exception)
            {
                return null;
            }
            if (!string.IsNullOrWhiteSpace(DealType))                //处理类型的模糊查询
            {
                query = query.Where(x => x.DealType.Contains(DealType));
            }
            if (State > 0) 
            {
                query = query.Where(x => x.State == State);                  //处理状态
            }
            pagination.records = query.Count();
            var data= query.OrderByDescending(x => x.CreateDate).Skip(pagination.rows * (pagination.page - 1)).Take(pagination.rows).ToList();
            PageResultData pageResultdata = new PageResultData()
            {
                rows=data,
                total= pagination.records / pagination.rows + 1,
                page= pagination.page,
                records=pagination.records,          
            };
            return pageResultdata;
        }
        public int UpdateForm(FileDownLoadEntity fileDownLoadEntity) {
            try
            {
                service.Update(fileDownLoadEntity);
                return 1;
            }
            catch (Exception)
            {
                return 0;
            }
        }
        //查详情
        public FileDownLoadEntity GetFileDownLoadInfo(int ID) 
        {
            FileDownLoadEntity data = null;
            try
            {
                data = service.FindEntity(x => x.ID == ID);
                return data;
            }
            catch (Exception)
            {
                return data;
            }
        }
}
}
