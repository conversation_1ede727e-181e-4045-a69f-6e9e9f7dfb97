﻿using MarketingShops.Model.trade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

using NFine.Code;
using MarketingShops.BLL.trade;
using MarketingShops.DAL.Miniapp.trade;

namespace MarketingShops.BLL.Miniapp.trade
{
    /// <summary>
    /// 订单-小程序同步专用bll
    /// </summary>
  public class trade_mini_bll
    {
      /// <summary>
      /// 小程序同步数据
      /// </summary>
      /// <param name="orderid">订单编号</param>
      /// <returns></returns>
      public static TradeInfo GetMiniTrandInfo(string orderid)
      {
          return trade_mini_dal.GetMiniTrandInfo(orderid);
      }
        /// <summary>
        /// 小程序同步
        /// </summary>
        /// <param name="orderid">编号</param>
      public static void MimiApp(string orderid, object mimidata)
        {
            try
            {
                if (string.IsNullOrEmpty(orderid)) throw new Exception("订单号为空");
                var data = mimidata;
                if (data == null)
                    data = GetMiniTrandInfo(orderid);
                if (data != null)
                {
                    NFine.Code.Miniapp.cloud.CloudDBManage clond = new NFine.Code.Miniapp.cloud.CloudDBManage();
                    clond.db = "buy_bill";
                    clond.update(data.ToJson(), "{\"bill_core.orderid\": \"" + orderid + "\"}");
                }
                else
                    throw new Exception("数据查询失败");
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }
    }
}
