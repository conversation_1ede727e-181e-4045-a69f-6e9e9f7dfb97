﻿using MarketingShops.DAL.Report;
using MarketingShops.Model;
using MarketingShops.Model.Report;
using NFine.Code;
using NFine.Code.Export;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.Report
{
   public class MidAutumn_Bll
    {
        MidAutumn_Dal dal = new MidAutumn_Dal();
        public PageResultData GetGridJson(Pagination pagination, string name,string time)
        {
            string seltj = "1=1";
            if(!string.IsNullOrEmpty(name))
                seltj += " and name like'%" + name.Trim() + "%'";
            if (!string.IsNullOrEmpty(time))
            {
                seltj += " and orderid in(select orderid from dbo.buy_bill_core where inTime<= '" + time.Trim() + "')";
            }
            return dal.GetGridJson(seltj, pagination);
        }

       /// <summary>
       /// 数据导出
       /// </summary>
       /// <param name="name">商品名称</param>
       /// <returns></returns>
        public void ExportMidAutumnData(string name)
        {
            string seltj = "1=1";
            if (!string.IsNullOrEmpty(name))
                seltj += " and name like'%" + name + "%'";
            string filename = "销售统计报表" + DateTime.Now.ToString("yyyy-MM-dd");
            string fileformat = "csv";
            List<MidAutumnModel> data = dal.ExportMidAutumnData(seltj);
            ExportData ex = new ExportData();
            ex.ExportToExcel<MidAutumnModel>(data, filename, fileformat);
        }
    }
}
