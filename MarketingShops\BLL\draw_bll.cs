﻿using MarketingShops.BLL.Factory;
using MarketingShops.DAL;
using MarketingShops.Model;
using MarketingShops.Model.Draw;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL
{
    public class draw_bll
    {
        draw_dal _dal = new draw_dal();
        public PageResultData GetAllDraw(Pagination pagination,string d_name="",int d_id=0)
        {
            string seltj = " 1 = 1";

            if (!string.IsNullOrEmpty(d_name))
            {
                seltj += " and a.d_name like '%" + d_name + "%' ";

            }
            if (d_id!=0)
            {
                seltj += " and a.d_id = '" + d_id + "' ";

            }
            return _dal.GetAllDraw(seltj, pagination);
        }

        public int DeleteDraw(int d_id) {

            return _dal.DeleteDraw(d_id);
        }

        /// <summary>
        /// 增加或修改
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public int SubmitForm(draw data)
        {
            if (data.d_state == 0)
                data.d_state = data.state_id;
            if (data.d_id == 0)
            { ///添加
                return _dal.AddDraw(data);
            }
            else
            { //修改
                return _dal.UpdateDraw(data);
            }
        }
          /// <summary>
        /// 配置增加或修改
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public int SubmitForm_Config(draw_config data)
        {
            if (data.drawmodel == 0)
                data.drawmodel = data.model_id;
            if (data.c_id == 0)
            { ///添加
                return _dal.AddDraw_Config(data);
            }
            else
            { //修改
                return _dal.UpdateDraw_Config(data);
            }
        }

        

        /// <summary>
        /// 获取所有活动状态
        /// </summary>
        public List<draw_enum_state> GetAlldraw_enum_state(){
            return _dal.GetAlldraw_enum_state();
        }

         /// <summary>
        /// 获取所有抽奖模式
        /// </summary>
        public List<draw_enum_model> GetAlldraw_enum_model()
        {
            return _dal.GetAlldraw_enum_model();
        }


        
        /// <summary>
        /// 获取活动下的配置
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public List<draw_config> GetDraw_Config(int d_id){
            return _dal.GetDraw_Config(d_id);
        }

         /// <summary>
        /// 获取活动下的配置
        /// </summary>
        /// <param name="d_id"></param>
        /// <returns></returns>
        public draw_config GetDrawSinge_Config(int c_id)
        {
            return _dal.GetDrawSinge_Config(c_id);
        }
        

        /// <summary>
        /// 删除指定配置规格
        /// </summary>
        /// <param name="c_id"></param>
        /// <returns></returns>
        public int DeleteDraw_Config(int c_id) {
            return _dal.DeleteDraw_Config(c_id);
        
        }
        /// <summary>
        /// 抽取配置的数据
        /// </summary>
        /// <param name="c_id"></param>
        /// <returns></returns>
        public ResultModel DrawHandle_Config(int c_id)
        {
            ResultModel result = new ResultModel();
            try
            {
                draw_config dcdata = GetDrawSinge_Config(c_id);
                DrawFactory df = new DrawFactory();
                List<draw_win> data = df.GetData(dcdata).GetWinData(); //
                if (data.Count > 0)
                    result= this.InSqlDataToTable(data);
                else {
                    result.Msg = "已达到或超过抽取数量";
                }
            }
            catch (Exception ex)
            {
                result.Msg=ex.Message;
                
            }

            return result;
            
        }

        /// <summary>
        /// 抽取配置的数据
        /// </summary>
        /// <param name="c_id"></param>
        /// <returns></returns>
        public ResultModel DrawClearWin_Config(int c_id)
        {
            ResultModel result = new ResultModel();
            try
            {
                return _dal.DrawClearWin_Config(c_id);
            }
            catch (Exception ex)
            {
                result.Msg=ex.Message;
                return result;
            }
          

            
        }

        



        public ResultModel InSqlDataToTable(List<draw_win> data)
        {
            return _dal.InSqlDataToTable(data);
            
        }





    }
}
