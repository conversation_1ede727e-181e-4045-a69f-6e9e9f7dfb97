﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_detailed_paymentMethod.cs
*
* 功 能： N/A
* 类 名： buy_bill_detailed_paymentMethod
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-25 15:55:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_detailed_paymentMethod:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class buy_bill_detailed_paymentMethod
	{
		public buy_bill_detailed_paymentMethod()
		{}
		#region Model
		//private int _id;
        //private Guid _detailedno;
		private int _buy_num;
		private int _gdskupid;
		private int _cash=0;
		private int _integral=0;
		private int _recharge;
        private string _txt;
		/// <summary>
        /// 自增ID
		/// </summary>
        //public int id
        //{
        //    set{ _id=value;}
        //    get{return _id;}
        //}
		/// <summary>
        /// 明细id
		/// </summary>
        //public Guid detailedno
        //{
        //    set{ _detailedno=value;}
        //    get{return _detailedno;}
        //}
		/// <summary>
        /// 购买数量
		/// </summary>
		public int buy_num
		{
			set{ _buy_num=value;}
			get{return _buy_num;}
		}
		/// <summary>
        /// SKU价格模块ID
		/// </summary>
        public int GDSKUPID
        {
            set { _gdskupid = value; }
            get { return _gdskupid; }
        }
		/// <summary>
        /// 现金金额（单价）
		/// </summary>
		public int cash
		{
			set{ _cash=value;}
			get{return _cash;}
		}
		/// <summary>
        /// 积分（单价）
		/// </summary>
		public int integral
		{
			set{ _integral=value;}
			get{return _integral;}
		}
		/// <summary>
        /// 充值（单价）
		/// </summary>
		public int recharge
		{
			set{ _recharge=value;}
			get{return _recharge;}
		}
        /// <summary>
        /// 展示文本
        /// </summary>
        public string txt {
            set { _txt = value; }
            get { return _txt; }
        }
		#endregion Model

	}
}

