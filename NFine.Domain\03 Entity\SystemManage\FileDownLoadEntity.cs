﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Domain.Entity.SystemManage
{
    public class FileDownLoadEntity : IEntity<FileDownLoadEntity>
    {
        public int ID { get; set; }
        public string FileName { get; set; }
        public int State { get; set; }
        //发起时间
        public DateTime? CreateDate { get; set; }
        //处理时间
        public DateTime? DealDate { get; set; }
        //处理账号
        public string OperatorCode { get; set; }
        //处理姓名
        public string OperatorName { get; set; }
        public bool IsDel { get; set; }
        //存放位置
        public string Address { get; set; }
        //文件大小
        public string FileSize { get; set; }
        //文件处理类型
        public string DealType { get; set; }
        //请求josn
        public string RequestJosn { get; set; }
        //下载次数
        public int? Number { get; set; }
    }
}
