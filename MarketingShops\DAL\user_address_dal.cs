﻿using MarketingShops.Model;
using MarketingShops.Model.User;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL
{
    public class user_address_dal
    {
        /// <summary>
        /// 获取用户数据
        /// </summary>
        /// <param name="p_id"></param>
        /// <returns></returns>
        public List<user_address> GetAddressData(string seltj)
        {

            string sql = ("select * from user_address where " + seltj + " order by isdefault desc,inputtime desc");
            return DbHelp_MarketingShops.ado.GetDataList<user_address>(sql);
        }

        /// <summary>
        /// 添加地址数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public ResultModel AddAddress(user_address data)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle_Pro<ResultModel>("user_address_ex", new { t = "add", data.openid,data.name,data.phone,data.region,data.detailed,data.isdefault });
        }

        /// <summary>
        /// 修改地址数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public ResultModel UpAddress(user_address data)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle_Pro<ResultModel>("user_address_ex", new { t = "update", data.ud_id,data.openid, data.name, data.phone, data.region, data.detailed, data.isdefault });
        }

        /// <summary>
        /// 删除地址数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public ResultModel DelAddress(int ud_id)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle_Pro<ResultModel>("user_address_ex", new { t = "del", ud_id });
        }



    }
}
