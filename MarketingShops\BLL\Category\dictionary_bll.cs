﻿using MarketingShops.DAL.Category;
using MarketingShops.Model.Category;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.Category
{
   public class dictionary_bll
    {
       private static dictionary_dal _dal = new dictionary_dal();
        /// <summary>
        /// 获取项内容详情
        /// </summary>
        /// <param name="keyValue">项内容id</param>
        /// <returns></returns>
        public List<item_dictionary> GetItemDictionaryInfo(int keyValue)
        {
            return _dal.GetItemDictionaryInfo(keyValue);
        }
       /// <summary>
       /// 删除字典
       /// </summary>
       /// <param name="ikey">字典key</param>
       /// <returns></returns>
        public int DeleteDictionary(string ikey)
        {
            return _dal.DeletrDictionaty(ikey);
        }
        /// <summary>
        /// 添加/修改 资源字典
        /// </summary>
        /// <param name="str">资源字典集合json</param>
        /// <param name="tci_id">栏目id</param>
        /// <returns></returns>
        public int SubmitForm(List<item_dictionary> dic, int tci_id)
        {
            if (tci_id > 0)
                return _dal.UpdataDictionary(dic, tci_id);
            else
                return _dal.AddDictionary(dic);
        }
    }
}
