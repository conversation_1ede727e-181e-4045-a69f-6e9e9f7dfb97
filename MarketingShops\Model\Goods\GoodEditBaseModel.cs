﻿using MarketingShops.Model.Goods.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods
{
    /// <summary>
    /// 商品编辑model
    /// </summary>
  public class GoodEditBaseModel
    {
        /// <summary>
        /// 商品字表
        /// </summary>
        public goods_dat_child child { get; set; }
        /// <summary>
        /// 商品详情表
        /// </summary>
        public goods_dat_details details { get; set; }
        /// <summary>
        /// 详情素材表
        /// </summary>
        public List<goods_dat_details_material> details_material { get; set; }
        /// <summary>
        /// 商品规格
        /// </summary>
        public List<goods_dat_sku_entity> sku { get; set; }
        /// <summary>
        /// 商品属性
        /// </summary>
        public goods_dat_spu_entity spu { get; set; }
        /// <summary>
        /// 商品属性详情
        /// </summary>
        public List<goods_dat_spu_entity_list> spulist { get; set; }
        /// <summary>
        /// 商品有效管理
        /// </summary>
        public goods_manage_valid managevalid { get; set; }
        /// <summary>
        /// 物流
        /// </summary>
        public goods_logistics logistics { get; set; }
    }
}
