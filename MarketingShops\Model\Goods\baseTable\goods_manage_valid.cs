﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods.baseTable
{
    /// <summary>
    /// 商品-有效管理
    /// </summary>
   public class goods_manage_valid
    {
       public int gvm_id { get; set; }
       /// <summary>
       /// 上架开始时间
       /// </summary>
       public DateTime? on_sale_time { get; set; }
       /// <summary>
       /// 上架结束时间
       /// </summary>
       public DateTime? in_stock_time { get; set; }
       /// <summary>
       /// 售卖开始时间
       /// </summary>
       public DateTime? start_sale_time { get; set; }
       /// <summary>
       /// 售卖结束时间
       /// </summary>
       public DateTime? end_sale_time { get; set; }
       /// <summary>
       /// 有效期开始时间
       /// </summary>
       public DateTime? start_validity_time { get; set; }
       /// <summary>
       /// 有效期结束时间
       /// </summary>
       public DateTime? end_validity_time { get; set; }
       /// <summary>
       /// 有效模式
       /// </summary>
       public int vilidity_model_no { get; set; }

    }
}
