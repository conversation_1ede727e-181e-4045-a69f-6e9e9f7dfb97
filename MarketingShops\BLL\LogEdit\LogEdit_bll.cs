﻿using MarketingShops.DAL.LogEdit;
using MarketingShops.Model;
using MarketingShops.Model.Classification;
using MarketingShops.Model.LogEdit;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.LogEdit
{
   public class LogEdit_bll
    {
       public LogEdit_dal _dal = new LogEdit_dal();
       public PageResultData GetClassificationEdit(Pagination page, string table_name, string BTime, string ETime, ClassificationLog template)
       {
           return _dal.GetClassificationEdit(template, page, BTime, ETime);;
       }
    }
}
