﻿using MarketingShops.Model.Template.PageShopTemplate;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Template.ShopPageTemplate
{
    /// <summary>
    /// 组合购修改、新增数据结构
    /// </summary>
    public class sell_edit_model : Sell_Combination_project
    {
        /// <summary>
        /// 商品项目信息
        /// </summary>
        public Sell_Combination_goods_project GoodProject { get; set; }
        /// <summary>
        /// 商品列表
        /// </summary>
        public List<Sell_Combination_goods_item> GoodItems { get; set; }
        /// <summary>
        /// 抽签信息
        /// </summary>
        public Sell_Combination_luck Luck { get; set; }

    }
    
}
