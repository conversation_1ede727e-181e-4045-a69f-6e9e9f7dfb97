﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Code.Miniapp.miniCode
{
   public class CreatMiniCode
    {
        public string token = "th_miniapp_seasonscard_token";
        public string page = "pages/CombinationPurchase/TimeLimitPage/index";
        public string baseurl = "http://r.tang-hui.com.cn/api/wechat/getwxacodeunlimit.ashx";
        /// <summary>
        /// 生成小程序码
        /// </summary>
        /// <param name="scene"></param>
        /// <returns></returns>
        string CreatCodeURL(string scene)
        {
            string url = "";
            if (!string.IsNullOrEmpty(scene))
                url = baseurl + "?token=" + token + "&page=" + page + "&scene=" + scene;
            return url;
        }

        /// <summary>
        /// 获取组合购小程序码
        /// </summary>
        /// <param name="scene"></param>
        /// <returns></returns>
        public string CreatMiniCode_Combination(string scene)
        {
            return CreatCodeURL(scene);
        }
    }
}
