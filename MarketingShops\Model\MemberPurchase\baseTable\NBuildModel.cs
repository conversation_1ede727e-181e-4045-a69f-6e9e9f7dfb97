﻿/**  版本信息模板在安装目录下，可自行修改。
* NBuildModel.cs
*
* 功 能： N/A
* 类 名： NBuildModel
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-09-16 15:14:42   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.MemberPurchase.baseTable
{
	/// <summary>
	/// NBuildModel:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class NBuildModel
	{
		public NBuildModel()
		{}
		#region Model
		private int _buildno;
		private string _buildname;
		private string _buildexplain;
		/// <summary>
		/// 
		/// </summary>
		public int BuildNo
		{
			set{ _buildno=value;}
			get{return _buildno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string BuildName
		{
			set{ _buildname=value;}
			get{return _buildname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string BuildExplain
		{
			set{ _buildexplain=value;}
			get{return _buildexplain;}
		}
		#endregion Model

	}
}

