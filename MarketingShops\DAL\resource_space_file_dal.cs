﻿using MarketingShops.Model;
using MarketingShops.Model.Img_Manage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL
{
   public class resource_space_file_dal
    {
        /// <summary>
        /// 获取文件数据
        /// </summary>
        /// <param name="p_id"></param>
        /// <returns></returns>
       public List<resource_space_file> GetImgFileData(string seltj,string formtdata="*")
        {

            string sql = ("select " + formtdata + " from resource_space_file a join resource_space_filetype b on a.ft_id= b.ft_id  where " + seltj + " order by a.inputtime desc");
            return DbHelp_MarketingShops.ado.GetDataList<resource_space_file>(sql);
        }

       /// <summary>
       /// 添加文件
       /// </summary>
       /// <param name="data"></param>
       /// <returns></returns>
       public int AddData(resource_space_file data)
       {
           string mkey =data.mkey.ToString();
           if (string.IsNullOrEmpty(mkey))
           {
               mkey = "NULL";
           }
           else {
               mkey = "'" + mkey + "'";
           }
           string sql = "insert into resource_space_file(ikey,mkey,s_id,inputid,filename,fileurl,filesize,inputtime,ft_id,isouturl) values (NEWID()," + mkey + "," + data.s_id + ",'" + data.inputid + "','" + data.filename + "','" + data.fileurl + "','" + data.filesize + "',getdate(),'" + data.ft_id + "','" + data.isouturl + "')";
           return DbHelp_MarketingShops.ado.OperationData(sql);
       }

       /// <summary>
       /// 删除文件
       /// </summary>
       /// <param name="ikey">文件key</param>
       /// <param name="dealid">操作人工号</param>
       /// <returns></returns>
       public ResultModel DelFile(Guid ikey, string dealid)
       {
           return DbHelp_MarketingShops.ado.GetDataSingle_Pro<ResultModel>("resource_space_ex_deal", new { cztype = "del", ikey, dealid });
       }

       /// <summary>
       /// 执行sql
       /// </summary>
       /// <param name="sql"></param>
       /// <returns></returns>
       public int ExecuteSql(string sql) {

           return DbHelp_MarketingShops.ado.OperationData(sql);
       }
       
    }
}
