﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web.Mvc;

namespace Page.CommunalService.Areas.Staff.Controllers
{
    public class BenefitWeddingController : WebInterface.ControllerBase
    {
       

        public ActionResult SubmitForm(string userids, string type)
        {

            SERVICE.PROXY.CommunalService.EmployeeTicketWeddingContext context = new SERVICE.PROXY.CommunalService.EmployeeTicketWeddingContext();
            if (type == "1")
            {
                context.Type = SERVICE.PROXY.CommunalService.WeddingTicketType.员工婚庆欢唱券1000元;
            }
            else
            {
                context.Type = SERVICE.PROXY.CommunalService.WeddingTicketType.员工婚庆欢唱券2000元;

            }
            context.UserList = userids.Split(',');
            var res = SERVICE.PROXY.RPC.Communal.ActionProxy(context, (cl, data) =>
            {
                return cl.SendEmployeeTicketWedding(data);

            }, null, null);

            return Success("操作成功。");
        }
    }
}

