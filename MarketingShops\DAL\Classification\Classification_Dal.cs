﻿using MarketingShops.Model;
using MarketingShops.Model.Classification;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Classification
{
   public class Classification_Dal
    {
       /// <summary>
       /// 查询分类信息
       /// </summary>
       /// <param name="seltj">查询条件</param>
       /// <returns></returns>
       public List<goods_classification> GetJsonInfo(string seltj)
       {
           string sql = "select * from goods_classification where " + seltj;
           return DbHelp_MarketingShops.ado.GetDataList<goods_classification>(sql);
       }
       /// <summary>
       /// 获取分类信息
       /// </summary>
       /// <param name="F_Id">自增编号</param>
       /// <returns></returns>
       public goods_classification GetClassificationInfo(string id)
       {
           return DbHelp_MarketingShops.ado.GetDataSingle<goods_classification>("select * from goods_classification where F_Id='" + id + "'");
       }

       /// <summary>
       /// 新增分类
       /// </summary>
       /// <param name="data">分类信息</param>
       /// <returns></returns>
       public int InsertClassification(goods_classification data)
       {
           var userinfo=OperatorProvider.Provider.GetCurrent();
           StringBuilder sb = new StringBuilder("declare @grouponNo uniqueidentifier,@F_Id uniqueidentifier=NEWID() ");
           if (data.F_ParentId != null)//子级
           {
               sb.Append("declare @F_Layers int ");
               sb.Append("select @grouponNo=F_GrounponNo,@F_Layers=F_Layers+1 from goods_classification where F_Id='" + data.F_ParentId + "' ");
               sb.Append(@"insert into goods_classification ([F_Id],[F_ParentId] ,[F_GrounponNo],[F_FullName],[F_IsShow] ,[F_SortCode] ,[F_Img] ,[F_GoodTot],[F_Description],[F_State],[F_Layers],[F_TcpId]) 
           values(@F_Id,'" + data.F_ParentId + "',@grouponNo,'" + data.F_FullName + "','" + data.F_IsShow + "'," + data.F_SortCode + ",'" + data.F_Img + "'," + data.F_GoodTot + ",'" + data.F_Description + "',1,@F_Layers," + data.F_TcpId + ") ");
           }
           else//父级
           {
               sb.Append("set @grouponNo =NEWID()");
               sb.Append(@"insert into goods_classification ([F_Id],[F_ParentId] ,[F_GrounponNo],[F_FullName],[F_IsShow] ,[F_SortCode] ,[F_Img] ,[F_GoodTot],[F_Description],[F_State],[F_Layers],[F_TcpId]) 
           values(@F_Id,null,@grouponNo,'" + data.F_FullName + "','" + data.F_IsShow + "'," + data.F_SortCode + ",'" + data.F_Img + "'," + data.F_GoodTot + ",'" + data.F_Description + "',1,0," + data.F_TcpId + ") ");
           }
              
           sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('insert','goods_classification', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "',@F_Id,'" + data.F_FullName + "')");
          
           return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
       }
       /// <summary>
       /// 修改分类信息
       /// </summary>
       /// <param name="data">分类信息</param>
       /// <param name="keyValue">id</param>
       /// <returns></returns>
       public int UpdateClassification(goods_classification data)
       {
           var userinfo = OperatorProvider.Provider.GetCurrent();
           StringBuilder sb = new StringBuilder();
           if (data.F_ParentId != null)//子级
           {
               sb.Append("declare @F_Layers int ");//获取层级数
               sb.Append("select @F_Layers=F_Layers+1 from goods_classification where F_Id='" + data.F_ParentId + "' ");
               sb.Append("update goods_classification set [F_ParentId]='" + data.F_ParentId + "',[F_FullName]='" + data.F_FullName + "',[F_IsShow]='" + data.F_IsShow + "' ,[F_SortCode]=" + data.F_SortCode + " ,[F_Img]='" + data.F_Img + "',[F_Description]='" + data.F_Description + "',[F_Layers]=@F_Layers,[F_TcpId]=" + data.F_TcpId + " where [F_Id]='" + data.F_Id + "'");
           }
           else//父级
               sb.Append("update goods_classification set [F_ParentId]=null,[F_FullName]='" + data.F_FullName + "',[F_IsShow]='" + data.F_IsShow + "' ,[F_SortCode]=" + data.F_SortCode + " ,[F_Img]='" + data.F_Img + "',[F_Description]='" + data.F_Description + "',[F_Layers]=0,[F_TcpId]=" + data.F_TcpId + " where [F_Id]='" + data.F_Id + "'");
           sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('update','goods_classification', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "','" + data.F_Id + "','" + data.F_FullName + "')");

           return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
       }
       /// <summary>
       /// 删除分类信息
       /// </summary>
       /// <param name="id">分类id</param>
       /// <returns></returns>
       public int DeleteClassification(string id)
       {
           var userinfo = OperatorProvider.Provider.GetCurrent();
           StringBuilder sb = new StringBuilder();
           goods_classification data = GetClassificationInfo(id);
           //不存在子集时方可删除分类信息（直接硬删除）
           sb.Append("if not exists(select * from goods_classification where F_ParentId='" + id + "' and F_EnabledMark=1)");
           sb.Append("begin ");
           sb.Append("delete from goods_classification where F_Id='" + id + "'");
           sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('delete','goods_classification', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "','" + data.F_Id+ "','"+data.F_FullName+"')");
           sb.Append(" end");
           return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
       }
    }
}
