﻿using MarketingShops.Model.trade;
using MarketingShops.Model.trade.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.trade
{
    /// <summary>
    /// 订单-导出专用dal
    /// </summary>
   public class trade_export_dal
    {
        /// <summary>
        /// 获取导出数据结构
        /// </summary>
        /// <param name="sys">导出条件</param>
        /// <returns></returns>
        public List<ExportTradeInfo> ExportTradeList(string seltj)
        {
            string sql = @"SELECT [total_goodsfee],[total_fee],[cash_fee],[discount_fee],[freight_fee],[total_integral],[total_recharge],[total_weight],[memberPhoneNumber],a.orderid,a.cash_fee,a.Userid,a.total_integral,a.total_num,a.InTime,b.name,b.phone,b.region,b.detailed,c.payTime,c.transactionid FROM dbo.buy_bill_core a join buy_bill_contacts b on a.orderid=b.orderid join buy_bill_secondary c on a.orderid=c.orderid where " + seltj + " order by c.inTime desc";
            List<ExportTradeInfo> list = DbHelp_MarketingShops.ado.GetDataList<ExportTradeInfo>(sql);
            if (list.Count > 0)
            {
                List<buy_bill_detailed> bill_details = null;
                buy_bill_mail mail = null;
                List<string> GoodName = null;
                for (int i = 0; i < list.Count; i++)
                {
                    sql = "select [g_name],[buy_num] from buy_bill_detailed where orderid='" + list[i].orderid + "' ";
                    bill_details = DbHelp_MarketingShops.ado.GetDataList<buy_bill_detailed>(sql);
                    if (bill_details.Count > 0)
                    {
                        GoodName = new List<string>();
                        for (int j = 0; j < bill_details.Count; j++)
                        {
                            bill_details[j].g_name += " x" + bill_details[j].buy_num;
                            GoodName.Add(bill_details[j].g_name);
                        }
                        //更换分隔符，将“，”改为“|”，原先的“，”导致导出报表数据出错，报表根据“，”分割字段
                        list[i].g_name = string.Join("|", GoodName.ToArray());
                    }
                    sql = "select b.mailName as company,a.mail_number from buy_bill_mail a join mail_freight_project b on a.company=b.freightno where orderid='" + list[i].orderid + "' ";
                    mail = DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_mail>(sql);
                    if (mail != null)
                    {
                        list[i].company = mail.company;
                        list[i].mail_number = mail.mail_number;
                    }
                    else
                    {
                        list[i].company = "";
                        list[i].mail_number = "";
                    }
                }
            }
            return list;
        }
    }
}
