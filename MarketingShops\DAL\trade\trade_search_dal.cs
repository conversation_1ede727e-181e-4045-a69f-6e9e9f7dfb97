﻿using MarketingShops.Model.trade.Search;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.trade
{
   public class trade_search_dal
    {
        /// <summary>
        /// 获取查询SQL语句
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <returns></returns>
       public static string GetSearchStr(trade_search_params sys)
        {
            string seltj = "isRefund='" + sys.isRefund + "'";
            if (sys.billState > 0)
                seltj += " and [billState]=" + sys.billState + "";
            if (sys.total_num >= 0)
                seltj += " and [total_num]=" + sys.total_num + "";
            if (!string.IsNullOrEmpty(sys.BTime) && !string.IsNullOrEmpty(sys.ETime))
            {
                sys.ETime += ":59";//结束时间到当前选中的分
                seltj += " and a.[inTime] between '" + sys.BTime.Trim() + "' and  '" + sys.ETime.Trim() + "' ";
            }
            if (!string.IsNullOrEmpty(sys.orderid))//订单号模糊查询
                seltj += " and a.[orderid] like '%" + sys.orderid.Trim() + "%'";
            if (!string.IsNullOrEmpty(sys.phone))
                seltj += " and [phone]='" + sys.phone + "'";
            if (!string.IsNullOrEmpty(sys.memberPhoneNumber))
                seltj += " and [memberPhoneNumber]='" + sys.memberPhoneNumber.Trim() + "'";
            if (sys.discount_fee1 >= 0 && sys.discount_fee2 >= 0)
                seltj += " and [discount_fee] between " + sys.discount_fee1 + " and " + sys.discount_fee2 + " ";
            return seltj;
        }

    }
}
