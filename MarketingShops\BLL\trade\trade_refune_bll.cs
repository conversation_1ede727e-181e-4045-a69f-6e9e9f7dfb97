﻿using MarketingShops.BLL.refund;
using MarketingShops.DAL.trade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.trade
{
   public class trade_refune_bll
    {
        /// <summary>
        /// 退款
        /// </summary>
        /// <param name="orderid">订单号</param>
        /// <returns></returns>
        public int Refune(string orderid, int total_integral)
        {
            return refund_bll.OrderRefund(orderid, total_integral);
        }
    }
}
