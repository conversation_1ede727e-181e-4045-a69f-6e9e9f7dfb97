﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_secondary.cs
*
* 功 能： N/A
* 类 名： buy_bill_secondary
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-25 15:55:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_secondary:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class buy_bill_secondary
	{
		public buy_bill_secondary()
		{}
		#region Model
		//private string _orderid;
		private string _scene;
		private DateTime? _paytime;
		private DateTime? _delivertime;
		private DateTime? _dealtime;
		private DateTime? _uptime;
		private int _transaction_cou=1;
		private DateTime _intime= DateTime.Now;
        private string _transactionid;
		/// <summary>
        /// 订单号
		/// </summary>
        //public string orderid
        //{
        //    set{ _orderid=value;}
        //    get{return _orderid;}
        //}
		/// <summary>
        /// 账单场景
		/// </summary>
		public string scene
		{
			set{ _scene=value;}
			get{return _scene;}
		}
		/// <summary>
        /// 付款时间
		/// </summary>
		public DateTime? payTime
		{
			set{ _paytime=value;}
			get{return _paytime;}
		}
		/// <summary>
        /// 发货时间
		/// </summary>
		public DateTime? deliverTime
		{
			set{ _delivertime=value;}
			get{return _delivertime;}
		}
		/// <summary>
        /// 完成时间
		/// </summary>
		public DateTime? dealTime
		{
			set{ _dealtime=value;}
			get{return _dealtime;}
		}
		/// <summary>
        /// 更改时间
		/// </summary>
		public DateTime? upTime
		{
			set{ _uptime=value;}
			get{return _uptime;}
		}
		/// <summary>
        /// 交易次数
		/// </summary>
		public int transaction_cou
		{
			set{ _transaction_cou=value;}
			get{return _transaction_cou;}
		}
		/// <summary>
        /// 记录时间
		/// </summary>
		public DateTime inTime
		{
			set{ _intime=value;}
			get{return _intime;}
		}
        /// <summary>
        /// 
        /// </summary>
        public string transactionId
        {
            set { _transactionid = value; }
            get { return _transactionid; }
        }
		#endregion Model

	}
}

