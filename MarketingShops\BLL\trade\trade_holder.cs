﻿using MarketingShops.DAL.trade;
using MarketingShops.Model.trade;
using MarketingShops.Model.trade.baseTable;
using MarketingShops.Model.trade.Search;

using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MarketingShops.Model;
using NFine.Code.Export;
using System.Data.OleDb;
using System.Data;
using MarketingShops.Model.Goods.enumTable;

namespace MarketingShops.BLL.trade
{
    /// <summary>
    /// 订单管理
    /// </summary>
    public class trade_holder
    {
        /// <summary>
        /// 订单
        /// </summary>
       trade_order_bll order_bll = new trade_order_bll();
        /// <summary>
        /// 退款
        /// </summary>
       trade_refune_bll refune_bll = new trade_refune_bll();
        /// <summary>
        /// 物流信息
        /// </summary>
       trade_freight_bll freight_bll = new trade_freight_bll();
        /// <summary>
        /// 联系人
        /// </summary>
       trade_contacts_bll contacts_bll = new trade_contacts_bll();
        /// <summary>
        /// 导出
        /// </summary>
       trade_export_bll export_bll = new trade_export_bll();
       #region  数据查询
        /// <summary>
       /// 系统查询
       /// </summary>
       /// <param name="sys">查询条件</param>
       /// <param name="Page">分页信息</param>
       /// <returns></returns>
       public PageResultData GetGridInfo(trade_search_params sys, Pagination Page)  
       {
           return order_bll.GetGridInfo(sys, Page);
       }
      
       /// <summary>
       /// 获取订单详情
       /// </summary>
       /// <param name="orderid">订单编号</param>
       /// <returns></returns>
       public object GetTradeInfo(string orderid)
       {
           return order_bll.GetTradeInfo(orderid);
       }

       /// <summary>
       /// 获取收件人信息
       /// </summary>
       /// <param name="orderid">联系人编号</param>
       /// <returns></returns>
       public buy_bill_contacts GetContactsInfo(string contacts_no)
       {
           return contacts_bll.GetContactsInfo(contacts_no);
       }
       /// <summary>
       /// 获取物流信息
       /// </summary>
       /// <param name="orderid">订单号</param>
       /// <returns></returns>
       public buy_bill_mail GetFreightInfo(string mail_no)
       {
           return freight_bll.GetFreightInfo(mail_no);
       }
      
        /// <summary>
        /// 获取物流公司数据
        /// </summary>
        /// <returns></returns>
       public List<good_enum_freight> GetCompanyList()
       {
           return freight_bll.GetCompanyList();
       }
       #endregion

       #region  数据操作
     /// <summary>
     /// 下单（添加订单记录）
     /// </summary>
     /// <param name="model">订单信息</param>
     /// <param name="pay">支付信息</param>
     /// <returns></returns>
       public int AddTradeInfo(TradeInfo model, buy_bill_payment pay)
       {
           return order_bll.AddTradeInfo(model, pay);
       }
       /// <summary>
       /// 修改联系人信息
       /// </summary>
       /// <param name="contacts">联系人数据</param>
       /// <returns></returns>
       public int UpdateContactsInfo(buy_bill_contacts contacts, string orderid) 
       {
           return contacts_bll.UpdateContactsInfo(contacts, orderid);
       }
       /// <summary>
       /// 订单完成
       /// </summary>
       /// <param name="orderid">订单号</param>
       /// <returns></returns>
       public int FinishOrder (string orderid)
       {
           return order_bll.FinishOrder(orderid);
       }
      
       /// <summary>
       /// 发货(添加物流信息)
       /// </summary>
       /// <param name="mail">物流信息</param>
       /// <returns></returns>
       public int AddMailInfo(buy_bill_mail mail)
       {
           return freight_bll.AddMailInfo(mail);
       }
       /// <summary>
       /// 修改物流信息
       /// </summary>
       /// <param name="mail">物流信息</param>
       /// <returns></returns>
       public int UpdateMailInfo(buy_bill_mail mail) 
       {
           return freight_bll.UpdateMailInfo(mail);
       }
       /// <summary>
       /// 导出报表
       /// </summary>
       /// <param name="sys">导出条件</param>
       /// <returns></returns>
       public void ExportTradeList(trade_search_params sys)   
       {
           export_bll.ExportTradeList(sys);
       }
       /// <summary>
       /// 批量发货
       /// </summary>
       /// <param name="path">文件路径</param>
       /// <returns></returns>
       public int BatchDeliver(string path)
       {
           return freight_bll.BatchDeliver(path);
       }
        /// <summary>
        /// 退款
        /// </summary>
       /// <param name="orderid">订单号</param>
        /// <returns></returns>
       public int Refune(string orderid, int total_integral)
       {
           return refune_bll.Refune(orderid, total_integral);
       }
       #endregion

      

    }
}
