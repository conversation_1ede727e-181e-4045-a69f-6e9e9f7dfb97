﻿using MarketingShops.Model.Img_Manage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL
{
    /// <summary>
    /// 图片空间站子项目管理
    /// </summary>
   public class resource_space_sub_dal
    {
       /// <summary>
       /// 获取标题栏
       /// </summary>
       /// <param name="p_id"></param>
       /// <returns></returns>
       public List<resource_space_sub> GetAllSubData(int p_id)
       {
           string sql = ("select * from resource_space_sub  where p_id=" + p_id + " order by s_id ");
           return DbHelp_MarketingShops.ado.GetDataList<resource_space_sub>(sql);
       }

       public resource_space_sub GetSingSubData(int s_id)
       {
           string sql = ("select * from resource_space_sub  where s_id=" + s_id + "");
           return DbHelp_MarketingShops.ado.GetDataSingle<resource_space_sub>(sql);
       }
    }
}
