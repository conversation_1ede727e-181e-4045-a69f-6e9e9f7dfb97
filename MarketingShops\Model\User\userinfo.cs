﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.User
{
   public  class userinfo
    {

       public string openid { get; set; }

       public string unionid { get; set; }
       /// <summary>
       /// 手机号码
       /// </summary>
       public string phone { get; set; }
       /// <summary>
       /// 用户昵称
       /// </summary>
       public string nickname {get;set;}
       /// <summary>
       /// 用户头像地址
       /// </summary>
       public string avatarurl { get; set; }
       /// <summary>
       /// 收件地址该字段表示用户当前正在使用的默认地址
       /// </summary>
       public int?  ud_id { get; set; }
       /// <summary>
       /// 收件地址条数
       /// </summary>
       public int ud_count { get; set; }
       /// <summary>
       /// 会员key
       /// </summary>
       public Guid? memberkey { get; set; }
       /// <summary>
       /// 最新修改时间
       /// </summary>
       public DateTime uptime { get; set; }
    }
}
