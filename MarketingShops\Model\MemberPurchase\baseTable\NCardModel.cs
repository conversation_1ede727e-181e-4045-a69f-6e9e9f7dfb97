﻿/**  版本信息模板在安装目录下，可自行修改。
* NCardModel.cs
*
* 功 能： N/A
* 类 名： NCardModel
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-09-16 15:14:41   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.MemberPurchase.baseTable
{
	/// <summary>
	/// NCardModel:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class NCardModel
	{
		public NCardModel()
		{}
		#region Model
		private int _cardmodelno;
		private string _cardname;
		private string _cardexplain="";
		private bool _iscardcheck= false;
		/// <summary>
		/// 
		/// </summary>
		public int CardModelNo
		{
			set{ _cardmodelno=value;}
			get{return _cardmodelno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CardName
		{
			set{ _cardname=value;}
			get{return _cardname;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CardExplain
		{
			set{ _cardexplain=value;}
			get{return _cardexplain;}
		}
		/// <summary>
		/// 
		/// </summary>
		public bool IsCardCheck
		{
			set{ _iscardcheck=value;}
			get{return _iscardcheck;}
		}
		#endregion Model

	}
}

