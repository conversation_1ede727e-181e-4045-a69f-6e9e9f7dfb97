﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_payment.cs
*
* 功 能： N/A
* 类 名： buy_bill_payment
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-26 19:29:00   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_payment:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class buy_bill_payment
	{
		public buy_bill_payment()
		{}
        #region Model
        private string _transactionid;
        private string _orderid;
        private string _appid;
        private string _attach;
        private string _banktype;
        private string _feetype;
        private string _outtradeno;
        private int _cashfee;
        private int _totalfee;
        private string _timeend;
        private string _openid;
        private string _body;
        private string _tradetype;
        private DateTime _time_in;
        /// <summary>
        /// 
        /// </summary>
        public string transactionId
        {
            set { _transactionid = value; }
            get { return _transactionid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string appid
        {
            set { _appid = value; }
            get { return _appid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string attach
        {
            set { _attach = value; }
            get { return _attach; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string bankType
        {
            set { _banktype = value; }
            get { return _banktype; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string feeType
        {
            set { _feetype = value; }
            get { return _feetype; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string outTradeNo
        {
            set { _outtradeno = value; }
            get { return _outtradeno; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int cashFee
        {
            set { _cashfee = value; }
            get { return _cashfee; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int totalFee
        {
            set { _totalfee = value; }
            get { return _totalfee; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string timeEnd
        {
            set { _timeend = value; }
            get { return _timeend; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string openid
        {
            set { _openid = value; }
            get { return _openid; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string body
        {
            set { _body = value; }
            get { return _body; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string tradeType
        {
            set { _tradetype = value; }
            get { return _tradetype; }
        }
        /// <summary>
        /// 
        /// </summary>
        public DateTime time_in
        {
            set { _time_in = value; }
            get { return _time_in; }
        }
        #endregion Model


	}
}

