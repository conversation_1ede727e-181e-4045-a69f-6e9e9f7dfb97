﻿using MarketingShops.DAL.Category;
using MarketingShops.Model;
using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.Category
{
  public class column_bll
    {
      private static column_dal _dal = new column_dal();
        /// <summary>
      /// 获取栏目信息
        /// </summary>
        /// <param name="pagination">分页信息</param>
        /// <param name="tcp_name">栏目名称</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(Pagination pagination, string tcp_name)
        {
          
            return _dal.GetGridInfo(tcp_name, pagination);
        }
      /// <summary>
      /// 获取栏目详情
      /// </summary>
      /// <param name="id">栏目自增id</param>
      /// <returns></returns>
        public column GetColumnInfo(int id)
        {
            return _dal.GetColumnInfo(id);
        }
        /// <summary>
        /// 修改栏目信息
        /// </summary>
        /// <param name="pro">栏目信息</param>
        /// <returns></returns>
        public int UpdateColumn(column pro)
        {
            return _dal.UpdateColumn(pro);
        }
        /// <summary>
        /// 新增栏目
        /// </summary>
        /// <param name="pro">栏目信息</param>
        /// <returns></returns>
        public int AddColumn(column pro)
        {
            return _dal.AddColumn(pro);
        }
        /// <summary>
        /// 删除栏目
        /// </summary>
        /// <param name="id">栏目自增id</param>
        /// <returns></returns>
        public int DeleteColumn(int id)
        {
            return _dal.DeleteColumn(id);
        }
    }
}
