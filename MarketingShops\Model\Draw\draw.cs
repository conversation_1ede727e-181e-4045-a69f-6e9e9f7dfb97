﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Draw
{
    /// <summary>
    /// 抽签项目活动
    /// </summary>
    public class draw:draw_enum_state
    {
        /// <summary>
        /// 主键自增
        /// </summary>
        public int d_id { get; set; }
        /// <summary>
        /// 名称
        /// </summary>
        public string d_name { get; set; }
        /// <summary>
        /// 开始时间
        /// </summary>
        public DateTime d_begtime { get; set; }
        /// <summary>
        /// 结束时间
        /// </summary>
        public DateTime d_endtime { get; set; }
        /// <summary>
        /// 抽取时间
        /// </summary>
        public DateTime d_puttime { get; set; }
        /// <summary>
        /// 当前状态
        /// </summary>
        public int d_state { get; set; }

        //插入时间
        public DateTime d_intime { get; set; }

        /// <summary>
        /// 总配置数量
        /// </summary>
        public int d_confignum { get; set; }
        /// <summary>
        /// 总抽取数量
        /// </summary>
        public int d_totnum { get; set; }


        
    }
}
