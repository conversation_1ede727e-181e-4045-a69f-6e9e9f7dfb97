﻿/**  版本信息模板在安装目录下，可自行修改。
* Sell_Combination_luck.cs
*
* 功 能： N/A
* 类 名： Sell_Combination_luck
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-09-01 14:07:42   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.Template.ShopPageTemplate
{
	/// <summary>
	/// Sell_Combination_luck:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class Sell_Combination_luck
	{
		public Sell_Combination_luck()
		{}
		#region Model
		//private string _key;
		private int _pid;
		private string _describe;
		private int _templateno=1;
		private string _windescribe;
		private DateTime _btime;
		private DateTime _etime;
		/// <summary>
		/// 
		/// </summary>
        //public string KEY
        //{
        //    set{ _key=value;}
        //    get{return _key;}
        //}
		/// <summary>
		/// 
		/// </summary>
		public int Pid
		{
			set{ _pid=value;}
			get{return _pid;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string Describe
		{
			set{ _describe=value;}
			get{return _describe;}
		}
		/// <summary>
		/// 
		/// </summary>
		public int TemplateNo
		{
			set{ _templateno=value;}
			get{return _templateno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string WinDescribe
		{
			set{ _windescribe=value;}
			get{return _windescribe;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime BTime
		{
			set{ _btime=value;}
			get{return _btime;}
		}
		/// <summary>
		/// 
		/// </summary>
		public DateTime ETime
		{
			set{ _etime=value;}
			get{return _etime;}
		}
		#endregion Model

	}
}

