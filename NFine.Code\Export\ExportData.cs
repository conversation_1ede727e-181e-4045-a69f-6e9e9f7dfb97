﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace NFine.Code.Export
{
    public class ExportData
    {
        /// <summary>
        /// 导出数据
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data">数据</param>
        /// <param name="filename">文件名称</param>
        /// <param name="fileformat">文件类型</param>
        public void ExportToExcel<T>(List<T> data, string filename, string fileformat)
        {
            string reportname = filename + "." + fileformat;
            string DataStr = "";///数据 字符串    
            string HeadStr = "";///头部数据 字符串    
            string SubStr = ""; //中间的数据
            //全局索引
            //int gloal_index = 0;
            System.Reflection.PropertyInfo[] oProps = null;
            List<int> allListIndex = new List<int>();
            int index = 0;
            for (int i = 0; i < data.Count; i++)
            {
                DataStr = "";
                if (oProps == null)
                {
                    oProps = ((Type)data[i].GetType()).GetProperties();
                }
                if (i == 0)
                {
                    foreach (var item in oProps)
                    {
                        try
                        {
                            string des = ((DescriptionAttribute)Attribute.GetCustomAttribute(item, typeof(DescriptionAttribute))).Description;
                            if (!string.IsNullOrEmpty(des))
                            {
                                if (!string.IsNullOrEmpty(HeadStr))
                                {
                                    HeadStr += ',';
                                }
                                HeadStr += des;
                                //dt.Columns.Add(des, item.PropertyType);
                                allListIndex.Add(index);

                            }
                        }
                        catch (Exception)
                        {

                        }
                        index++;
                    }
                    HeadStr += "\r\n";

                }
                for (int j = 0; j < allListIndex.Count; j++)
                {
                    var nowindex = allListIndex[j];
                    // var thisdata = oProps[nowindex].Name;
                    var thisvalue = oProps[nowindex].GetValue(data[i]);
                    if (thisvalue != null && thisvalue.ToString().Length > 11)
                    {
                        try
                        {
                            if (thisvalue.ToString().Contains("\n"))
                                thisvalue = thisvalue.ToString().Replace("\n", "");
                            //thisvalue = thisvalue
                            //long aa = long.Parse(thisvalue.ToString());
                            thisvalue = " \t" + thisvalue;

                        }
                        catch (Exception ex)
                        {

                        }

                    }


                    if (!string.IsNullOrEmpty(DataStr))
                    {
                        DataStr += ',';
                    }
                    DataStr += thisvalue;
                }
                DataStr += "\r\n";
                SubStr += DataStr;
            }
            string LastStr = HeadStr + SubStr;
            byte[] bytes = System.Text.Encoding.Default.GetBytes(LastStr);
            HttpContext.Current.Response.Clear();
            HttpContext.Current.Response.ClearHeaders();
            HttpContext.Current.Response.Buffer = false;
            HttpContext.Current.Response.AppendHeader("Content-Disposition", "attachment;filename=" + HttpUtility.UrlEncode(reportname, System.Text.Encoding.UTF8));
            HttpContext.Current.Response.ContentEncoding = System.Text.Encoding.UTF8;
            HttpContext.Current.Response.Charset = "UTF-8";
            HttpContext.Current.Response.ContentType = "application/vnd.ms-excel";
            HttpContext.Current.Response.AddHeader("Content-Length", bytes.Length.ToString());
            HttpContext.Current.Response.OutputStream.Write(bytes, 0, bytes.Length);
            HttpContext.Current.Response.Flush();
            HttpContext.Current.Response.Close();



            //FileDownHelper.DownLoadold(filepath, filename);
            // File.WriteAllText(@"D:\data\Test.csv", LastStr);





        }


    }
}
