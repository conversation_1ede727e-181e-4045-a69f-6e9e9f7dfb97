﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.MemberPurchase
{
   public class purchase_details
    {
        /// <summary>
        /// 
        /// </summary>
        public string GrouponKey { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ProjectKey { get; set; }
        /// <summary>
        /// 深圳房费会员购
        /// </summary>
        public string GrouponName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double GoodsPrice { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public double GrouponPrice { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string ImgUrl { get; set; }
        /// <summary>
        /// 周一至周日房费时段
        /// </summary>
        public string Period { get; set; }
        /// <summary>
        /// 1、本券从购买次日起30天内有效（购买当天不可使用），套小房套餐：黄金时段20:00-22:00时段内进房欢唱7小时+百威罐/雪花纯生纤体罐1打（2选1）+缤纷果盘1个+特色小食2份+特色茶饮1渣+纸巾1盒；2、到店加100元即可升级为套Party中房消费；3、其中10月1日至10月8日、12月24日至12月25日不可用；4、到店另付开机费：小房20元/间，中房30元/间；5、不含消耗品费，需按实际消费人数收取3元/位消耗品费； 6、本券在确认支付后不可取消、更改或退款，使用本券需至少提前1天预约；7、本券不设找零或兑换现金，不可与其他线上优惠、优惠券及店内其他优惠同时享受；8、我司对本会员购在法律允许的情况下享有最终解释权。
        /// </summary>
        public string Explain { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string FdNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CardModelNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsSpecialOffer { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsUse { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsDel { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string UseShopIds { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int UseAreaNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int SortNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string UserId { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string UserName { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? InputTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int ValidModelNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? Valid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? ValidEnd { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int SaleModelNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? SaleStartTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? SaleEndTime { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsInvalid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CodeNumber { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CodeSaleMin { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CodeSaleMax { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int BookDay { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsMember { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int SingleConsume { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int DisableDay { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int CardLableNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int BuildNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string CodeFormat { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string UseAreaField { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int UseModelNo { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int SelType { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? NValid { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public DateTime? NValidEnd { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public int AreaNo { get; set; }
        /// <summary>
        /// 深圳地区
        /// </summary>
        public string AreaName { get; set; }
        /// <summary>
        /// 仅限深圳地区可见
        /// </summary>
        public string AreaExplain { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string AreaField { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public bool IsEnable { get; set; }
       /// <summary>
       /// 
       /// </summary>
        public int HtmlTemplateNo { get; set; }

    }
}
