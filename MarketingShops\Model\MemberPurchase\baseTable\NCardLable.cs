﻿/**  版本信息模板在安装目录下，可自行修改。
* NCardLable.cs
*
* 功 能： N/A
* 类 名： NCardLable
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-09-16 15:14:41   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.MemberPurchase.baseTable
{
	/// <summary>
	/// NCardLable:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class NCardLable
	{
		public NCardLable()
		{}
		#region Model
		private int _cardlableno;
		private string _cardlablename;
		private string _cardlableexplain;
		/// <summary>
		/// 
		/// </summary>
		public int CardLableNo
		{
			set{ _cardlableno=value;}
			get{return _cardlableno;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CardLableName
		{
			set{ _cardlablename=value;}
			get{return _cardlablename;}
		}
		/// <summary>
		/// 
		/// </summary>
		public string CardLableExplain
		{
			set{ _cardlableexplain=value;}
			get{return _cardlableexplain;}
		}
		#endregion Model

	}
}

