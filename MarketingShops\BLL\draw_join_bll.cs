﻿using MarketingShops.DAL;
using MarketingShops.Model;
using MarketingShops.Model.Draw;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL
{
   public  class draw_join_bll
    {
       draw_join_dal _dal = new draw_join_dal();
       /// <summary>
       /// 获取参与数据
       /// </summary>
       /// <param name="pagination"></param>
       /// <param name="data"></param>
       /// <returns></returns>
       public PageResultData GetAllDrawJoin_Page(Pagination pagination, draw_win data)
        {
            string seltj = GetSelTj(data);
            return _dal.GetAllDrawJoin_Page(seltj, pagination);
        }
       ///// <summary>
       ///// 获取所有预定中奖数据
       ///// </summary>
       ///// <param name="data"></param>
       ///// <returns></returns>
       //public PageResultData GetWinDrawJoin_Page(Pagination pagination, draw_win data)
       //{
       //    string seltj = GetSelTj(data);
       //    return _dal.GetWinDrawJoin_Page(seltj, pagination);
       //}

       /// <summary>
       /// 获取抽取人员数据
       /// </summary>
       /// <param name="data"></param>
       /// <returns></returns>
       public List<draw_win> GetDrawBaseData(draw_win data)
       {
           string seltj = GetSelTj(data);
           return _dal.GetDrawBaseData(seltj);
       }

       

       /// <summary>
       /// 查询拼接条件
       /// </summary>
       /// <param name="data"></param>
       /// <returns></returns>
       public string GetSelTj(draw_win data)
       {
           string seltj = " 1 = 1";
           if (!string.IsNullOrEmpty(data.j_name))
           {
               seltj += " and a.j_name like '%" + data.j_name + "%' ";

           }
           if (!string.IsNullOrEmpty(data.j_phone))
           {
               seltj += " and a.j_phone like '%" + data.j_phone + "%' ";

           }
           if (!string.IsNullOrEmpty(data.j_site))
           {
               seltj += " and a.j_site like '%" + data.j_site + "%' ";

           }
           if (data.d_id != 0)
           {
               seltj += " and a.d_id = '" + data.d_id + "' ";

           }
           if (data.c_id != 0)
           {
               seltj += " and a.c_id = '" + data.c_id + "' ";
           }
           if (data.wintype != 0) {
               seltj += " and wintype != 0 ";
           }

           return seltj;
       
       }

       /// <summary>
       /// 中奖的设置与取消
       /// </summary>
       /// <param name="d_id"></param>
       /// <returns></returns>
       public int SubmitForm(int j_id, int wintype)
       {
           if (wintype == 0) ///添加中奖，
           {
               return _dal.AddDraw_Win(j_id);
           }
           else
           { //取消中奖
               return _dal.DelDraw_Win(j_id);
           }
       }



    }
}
