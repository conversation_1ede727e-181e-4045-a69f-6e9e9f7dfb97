﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_detailed.cs
*
* 功 能： N/A
* 类 名： buy_bill_detailed
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-25 15:55:21   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using NFine.Code;
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_detailed:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class buy_bill_detailed
	{
		public buy_bill_detailed()
		{}
		#region Model
        private Guid _detailedno;
        private string _g_id;
        private string _g_name;
        private string _sku_id;
        private string _skuname;
        private string _photo;
        private string _orderid;
        private int _buy_num;
        private string _showprice = "";
        private int _cash = 0;
        private int _integral = 0;
        private int _recharge = 0;
        private int _weight = 0;
        private int _paymethodcou = 0;
        private int _stateno = 1;

		/// <summary>
        /// 主键id
		/// </summary>
        public Guid detailedno
		{
            set { _detailedno = value; }
            get { return _detailedno; }
		}
		/// <summary>
        /// 商品ID
		/// </summary>
		public string g_id
		{
			set{ _g_id=value;}
			get{return _g_id;}
		}
		/// <summary>
        /// 商品名称
		/// </summary>
		public string g_name
		{
			set{ _g_name=value;}
			get{return _g_name;}
		}
		/// <summary>
        /// 规格ID
		/// </summary>
		public string sku_id
		{
			set{ _sku_id=value;}
			get{return _sku_id;}
		}
		/// <summary>
        /// 规格名称
		/// </summary>
		public string skuname
		{
			set{ _skuname=value;}
			get{return _skuname;}
		}
		/// <summary>
        /// 商品图
		/// </summary>
		public string photo
		{
			set{ _photo=value;}
			get{return _photo;}
		}
        /// <summary>
        /// 订单号
        /// </summary>
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        /// <summary>
        /// 购买数量
        /// </summary>
        public int buy_num
		{
			set{ _buy_num=value;}
			get{return _buy_num;}
		}
        /// <summary>
        /// 显示金额
        /// </summary>
        public string showprice
        {
            set { _showprice = value; }
            get { return _showprice; }
        }
		/// <summary>
        /// 现金金额(单价)
		/// </summary>
		public int cash
		{
			set{ _cash=value;}
			get{return _cash;}
		}
		/// <summary>
        /// 积分（单价）
		/// </summary>
		public int integral
		{
			set{ _integral=value;}
			get{return _integral;}
		}
		/// <summary>
        /// 充值（单价）
		/// </summary>
		public int recharge
		{
			set{ _recharge=value;}
			get{return _recharge;}
		}
		/// <summary>
        /// 支付模式总数，默认0
		/// </summary>
		public int payMethodCou
		{
			set{ _paymethodcou=value;}
			get{return _paymethodcou;}
		}
		/// <summary>
        /// 明细状态ID
		/// </summary>
		public int stateNo
		{
			set{ _stateno=value;}
			get{return _stateno;}
		}
        /// <summary>
        /// 商品重量
        /// </summary>
        public int weight
        {
            set { _weight = value; }
            get { return _weight; }
        }
        #endregion Model



    }
}

