﻿using MarketingShops.Model;
using MarketingShops.Model.LogEdit;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.LogEdit
{
   public class LogEdit_dal
    {
       public PageResultData GetClassificationEdit(ClassificationLog template, Pagination Page, string BTime, string ETime)
       {
           string variable = "table_name='" + template.table_name.Trim() + "'";
           if (!string.IsNullOrEmpty(BTime) && !string.IsNullOrEmpty(ETime))
           {
               ETime += ":59";
               variable += " and edittime between '" + BTime.Trim() + "' and '" + ETime.Trim() + "'";
           }
           if (!string.IsNullOrEmpty(template.primary))
               variable += " and [primary]='" + template.primary.Trim() + "'";
           if (!string.IsNullOrEmpty(template.username))
               variable += " and [username]='" + template.username.Trim() + "'";

           string sql = "select {0} from log_marketing_edit where " + variable;
           return DbHelp_MarketingShops.paging<log_marketing_edit>(sql, "*", Page.sidx, Page.page, Page.rows);
       }
    }
}
