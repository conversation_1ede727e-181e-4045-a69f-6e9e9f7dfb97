﻿using MarketingShops.BLL.MemberPurchase;
using MarketingShops.Model;
using MarketingShops.Model.MemberPurchase;
using MarketingShops.Model.MemberPurchase.baseTable;
using MarketingShops.Model.MemberPurchase.search;
using MarketingShops.Model.trade;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.MemberPurchase
{
   public class MemberPurchase_dal
    {
        #region 查询
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="condition">查询条件</param>
        /// <param name="Page">页码信息</param>
        /// <returns></returns>
       public List<purchase_search_data> GetGridInfo(purchase_search_params sys, Pagination Page)
        {
            string condition = "";
            if (!string.IsNullOrEmpty(sys.GrouponName))
                condition += "&GrouponName=" + sys.GrouponName.Trim();
            if (sys.UseAreaNo > 0)
                condition += "&UseAreaNo=" + sys.UseAreaNo;
            if (sys.ProjectKey != null)
                condition += "&ProjectKey=" + sys.ProjectKey;

           string json = api_bll.GetDataList(condition, Page.page, Page.rows);
           dataPost<purchase_search_data> data1 = json.ToObject<dataPost<purchase_search_data>>();
           List<purchase_search_data> Alldata = null;
           if (data1.success)
               Alldata = data1.ObjceData;
           else
               throw new Exception(data1.errorCode);
            return Alldata;
        }
       /// <summary>
       /// 获取会员购详情
       /// </summary>
       /// <param name="GrouponKey">会员购key</param>
       /// <returns></returns>
       public purchase_details GetGrouponInfo(Guid GrouponKey)
       {
           string json = api_bll.GetGrouponInfo(GrouponKey);
           dataPost<purchase_details> data = json.ToObject<dataPost<purchase_details>>();
           List<purchase_details> Alldata = null;
           if (data.success)
           {
               Alldata = data.ObjceData;
               if (Alldata.Count > 0) {
                   json = api_bll.GetGrouponHtmlInfo(GrouponKey);
                   dataPost<NHtmlTemplate> data1 = json.ToObject<dataPost<NHtmlTemplate>>();
                   if (data1.success)
                   {
                       List<NHtmlTemplate> html = data1.ObjceData;
                       Alldata[0].HtmlTemplateNo = html[0].HtmlTemplateNo;
                   }
                   else
                       throw new Exception(data1.errorCode);
               }
           }
           else
               throw new Exception(data.errorCode);
           return Alldata[0];
       }
       /// <summary>
       /// 获取下拉框数据
       /// </summary>
       /// <typeparam name="T"></typeparam>
       /// <param name="tablename"></param>
       /// <returns></returns>
       public List<T> GetComboBoxDataList<T>(string tablename)
       {
           return DbHelp_MIMS.ado.GetDataList<T>("select * from " + tablename + "");
       }
       /// <summary>
       /// 获取会员购项目
       /// </summary>
       /// <returns></returns>
       public List<projectModel> GetProjectList()
       {
          string json= api_bll.GetProjectList();
          dataPost<projectModel> data = json.ToObject<dataPost<projectModel>>();
          if (data.success)
              return data.ObjceData;
          else
              throw new Exception(data.errorCode);
       }
        #endregion
    }
}
