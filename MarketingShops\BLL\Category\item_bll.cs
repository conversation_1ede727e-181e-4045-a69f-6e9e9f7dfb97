﻿using MarketingShops.DAL.Category;
using MarketingShops.Model;
using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.Category
{
   public class item_bll
    {
       private static item_dal _dal = new item_dal();
       /// <summary>
       /// 获取项目信息
       /// </summary>
       /// <param name="pagination">分页信息</param>
       /// <param name="tcp_name">项目名称</param>
       /// <returns></returns>
       public PageResultData GetGridInfo(Pagination pagination, string tcp_name)
       {
          
           return _dal.GetGridInfo(tcp_name, pagination);
       }
       /// <summary>
       /// 获取项目详情
       /// </summary>
       /// <param name="keyValue">项目自增id</param>
       /// <returns></returns>
       public item GetItemInfo(int keyValue)
       {
           return _dal.GetItemInfo(keyValue);
       }
       /// <summary>
       /// 修改项目信息
       /// </summary>
       /// <param name="pro">项目信息</param>
       /// <returns></returns>
       public int UpdateItemt(item pro)
       {
           return _dal.UpdateItem(pro);
       }
       /// <summary>
       /// 新增项目
       /// </summary>
       /// <param name="pro">项目信息</param>
       /// <returns></returns>
       public int AddItem(item pro)
       {
           return _dal.AddItem(pro);
       }
       /// <summary>
       /// 删除项目
       /// </summary>
       /// <param name="id">项目自增id</param>
       /// <returns></returns>
       public int DeleteItem(int id)
       {
           return _dal.DeleteItem(id);
       }
    }
}
