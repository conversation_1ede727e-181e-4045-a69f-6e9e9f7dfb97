                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    /*******************************************************************************
 * Copyright ? 2016 NFine.Framework ��Ȩ����
 * Author: NFine
 * Description: NFine���ٿ���ƽ̨
 * Website��http://www.nfine.cn
*********************************************************************************/
using NFine.Application.SystemManage;
using NFine.Code;
using NFine.Domain.Entity.SystemManage;
using System.Collections.Generic;
using System.Text;
using System.Web.Mvc;
using WorkGaugeCore.DAL;

namespace NFine.Web.Controllers
{
    [HandlerLogin]
    public class HomeController : Controller
    {
        [HttpGet]
        public ActionResult Index()
        {

            var current = NFine.Code.OperatorProvider.Provider.GetCurrent();

            if (current == null)
                return RedirectToAction("Index", "Login");
            else
                return View();
        }

        [HttpGet]
        public ActionResult IndexClient()
        {

            var current = NFine.Code.OperatorProvider.Provider.GetCurrent();

            if (current == null)
                return RedirectToAction("IndexClient", "Login");
            else
                return View();
        }
        [HttpGet]
        public ActionResult Default()
        {
            return View();
        }
        [HttpGet]
        public ActionResult Page_1()
        {

            return View();
        }
        [HttpGet]
        public ActionResult page_test()
        {

            return View();
        }
        public ContentResult ReportCout()
        {
            return Content(string.Empty);
            //var latelyproject_7 = new ReportCouDal().latelyproject_7();
            //return Content(new { latelyproject_7 }.ToJson());
        }

        [HttpGet]
        public ActionResult About()
        {
            return View();
        }
    }
}
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                              