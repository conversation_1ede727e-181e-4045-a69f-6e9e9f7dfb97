﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_detailed_cost.cs
*
* 功 能： N/A
* 类 名： buy_bill_detailed_cost
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-25 15:55:22   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_detailed_cost:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class buy_bill_detailed_cost
	{
		public buy_bill_detailed_cost()
		{}
		#region Model
        //private Guid _detailedno;
		private int _total_cash=0;
		private int _total_integral=0;
		private int _total_recharge=0;
        private int _total_weight = 0;
		/// <summary>
		/// 
		/// </summary>
        //public Guid detailedno
        //{
        //    set{ _detailedno=value;}
        //    get{return _detailedno;}
        //}
		/// <summary>
        /// 现金金额小计
		/// </summary>
		public int total_cash
		{
			set{ _total_cash=value;}
			get{return _total_cash;}
		}
		/// <summary>
        /// 积分小计
		/// </summary>
		public int total_integral
		{
			set{ _total_integral=value;}
			get{return _total_integral;}
		}
		/// <summary>
        /// 充值小计
		/// </summary>
		public int total_recharge
		{
			set{ _total_recharge=value;}
			get{return _total_recharge;}
		}
        /// <summary>
        /// 重量小计
        /// </summary>
        public int total_weight
		{
            set { _total_weight = value; }
            get { return _total_weight; }
		}
        
		#endregion Model

	}
}

