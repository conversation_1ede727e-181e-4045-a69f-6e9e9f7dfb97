﻿using NFine.Code;
using NFine.Domain._03_Entity.SystemManage;
using NFine.Domain._04_IRepository.SystemManage;
using NFine.Repository.SystemManage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Application.SystemManage
{
    /// <summary>
    /// 站点信息管理
    /// </summary>
    public class SiteApp
    {
        private ISiteRepository service = new SiteRepository();

        public Dictionary<string, string> GetList()
        {
            Dictionary<string, string> dic = new Dictionary<string, string>();
            var datalist = service.FindList("select* from Sys_SiteConfig").ToList();
            if (datalist != null)
            {
                foreach (var item in datalist)
                {
                    dic.Add(item.ikey, item.val);
                }
            }
            return dic;
        }

        public int SubmitForm(List<SiteEntity> sitelist)
        {
            int result = 0;
             string upsql = "";
            if (sitelist != null && sitelist.Count > 0)
            {
               
                foreach (var item in sitelist)
                {
                    upsql += "update Sys_SiteConfig set val = '"+item.val+"',des = '"+item.des+"'  where ikey = '"+item.ikey+"'   ";
                }
                try
                {
                    service.FindList(upsql).ToList();
                    result = 1;
                }
                catch (Exception)
                {
                    result = 0;
                }
               
            }
            else {
                result= 0;
            }
            return result;
          
        }


    }
}
