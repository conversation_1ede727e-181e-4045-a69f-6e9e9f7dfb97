﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.LogEdit
{
    /// <summary>
    /// 操作日志
    /// </summary>
    public class log_marketing_edit
    {
        /// <summary>
        /// 自增id
        /// </summary>
        public int id { get; set; }
        /// <summary>
        /// 类型（delete、update、insert）
        /// </summary>
        public string type { get; set; }
        /// <summary>
        /// 操作的key
        /// </summary>
        public string primary { get; set; }
        /// <summary>
        /// 内容
        /// </summary>
        public string title { get; set; }
        /// <summary>
        /// 表名
        /// </summary>
        public string table_name { get; set; }
        /// <summary>
        /// 操作人id
        /// </summary>
        public string userid { get; set; }
        /// <summary>
        /// 操作人
        /// </summary>
        public string username { get; set; }
        /// <summary>
        /// 数据
        /// </summary>
        public string json { get; set; }
        /// <summary>
        /// 操作时间
        /// </summary>
        public DateTime? edittime { get; set; }
    }
}
