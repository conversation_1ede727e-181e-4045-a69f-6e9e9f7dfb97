﻿using MarketingShops.Model;
using MarketingShops.Model.Therefunddetail.Search;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.RefundTarde
{
    /// <summary>
    /// 退款管理
    /// </summary>
    public class therefunddetail_dal
    {
        #region 查询（分页）
        /// <summary>
        /// 系统查询
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">页码信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(string seltj, Pagination Page)
        {
            string sql = @"SELECT {0} FROM dbo.buy_bill_core a join buy_bill_contacts b on a.orderid=b.orderid where " + seltj;
            return DbHelp_MarketingShops.paging<the_refund_detail>(sql, @"a.[orderid]  
      ,[total_num]
      ,[total_fee]
      ,[cash_fee]
      ,[discount_fee]
      ,[Userid]
      ,[memberKey]
      ,[billState]
      ,[isRefund],[phone],[inTime],[total_integral]", Page.sidx, Page.page, Page.rows);
        }
        #endregion
    }
}
