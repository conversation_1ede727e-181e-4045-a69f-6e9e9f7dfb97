﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.refund
{
   public static class refund_dal
    {
        /// <summary>
        /// 退款
        /// 1.检测是否需要返还积分
        /// 2.检测积分是否返回成功
        /// 3.修改账单数据
        /// </summary>
        /// <param name="orderid">订单号</param>
        /// <param name="total_integral">积分</param>
        /// <returns></returns>
       public static int OrderRefund(string orderid, int total_integral)
        {
            if (total_integral > 0)
            {
                int refund = DbHelp_MIMS.ado.OperationData("exec MarketingShops_deduction @isRefund=1,@orderid=@id", new { id = orderid });
                if (refund > 0)
                {
                    return DbHelp_MarketingShops.ado.OperationData("update buy_bill_core set isRefund=1 where orderid=@orderid ", new { orderid = orderid });
                }
                else throw new Exception("会员积分返还失败");
            }
            else
            {
                return DbHelp_MarketingShops.ado.OperationData("update buy_bill_core set isRefund=1 where orderid=@orderid ", new { orderid = orderid });
            }
        }
    }
}
