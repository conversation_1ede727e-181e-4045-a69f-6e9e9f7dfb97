﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_contacts.cs
*
* 功 能： N/A
* 类 名： buy_bill_contacts
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-25 15:55:21   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_contacts:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class buy_bill_contacts
	{
		public buy_bill_contacts()
		{}
		#region Model
		private string _contacts_no;
        //private string _orderid;
        //private string _mail_no;
		private string _name;
		private string _phone;
		private string _region;
		private string _detailed;
		/// <summary>
        /// 主键ID
		/// </summary>
		public string contacts_no
		{
			set{ _contacts_no=value;}
			get{return _contacts_no;}
		}
		/// <summary>
		/// 
		/// </summary>
        //public string orderid
        //{
        //    set{ _orderid=value;}
        //    get{return _orderid;}
        //}
        ///// <summary>
        ///// 
        ///// </summary>
        //public string mail_no
        //{
        //    set{ _mail_no=value;}
        //    get{return _mail_no;}
        //}
		/// <summary>
        /// 收件人名称
		/// </summary>
		public string name
		{
			set{ _name=value;}
			get{return _name;}
		}
		/// <summary>
        /// 收件人电话
		/// </summary>
		public string phone
		{
			set{ _phone=value;}
			get{return _phone;}
		}
		/// <summary>
        /// 区域信息
		/// </summary>
		public string region
		{
			set{ _region=value;}
			get{return _region;}
		}
		/// <summary>
        /// 详细地址
		/// </summary>
		public string detailed
		{
			set{ _detailed=value;}
			get{return _detailed;}
		}
		#endregion Model

	}
}

