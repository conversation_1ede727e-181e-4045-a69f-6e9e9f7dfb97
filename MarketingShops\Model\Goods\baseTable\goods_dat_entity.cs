﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods.baseTable
{
    /// <summary>
    /// 商品信息表
    /// </summary>
    public class goods_dat_entity
    {
       /// <summary>
       /// 编号
       /// </summary>
       public string g_id { get; set; }
        /// <summary>
        /// 商品项目id
        /// </summary>
       public string pid { get; set; }
        /// <summary>
        /// 货号（条形码）
        /// </summary>
       public string productno { get; set; }
       /// <summary>
       /// 名称
       /// </summary>
       public string name { get; set; }
       /// <summary>
       /// 总库存
       /// </summary>
       public int stocknums { get; set; }
       /// <summary>
       /// 图片
       /// </summary>
       public string photo { get; set; }
       /// <summary>
       /// 销售量
       /// </summary>
       public int salesnums { get; set; }
       /// <summary>
       /// 售价
       /// </summary>
       public double sprice { get; set; }
        /// <summary>
        /// 原价
        /// </summary>
       public double yprice { get; set; }
       /// <summary>
       /// 状态
       /// </summary>
       public int state { get; set; }
       ///// <summary>
       ///// 商家编码
       ///// </summary>
       public string code { get; set; }
        /// <summary>
        /// 更新时间
        /// </summary>
       public DateTime? updatetime { get; set; }
    }
}
