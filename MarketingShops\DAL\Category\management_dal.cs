﻿using MarketingShops.Model;
using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Category
{
   public class management_dal
    {
        /// <summary>
        /// 获取项目信息
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">分页信息</param>
        /// <returns></returns>
       public PageResultData GetGridInfo(string tcp_name,string tcc_name, Pagination Page)
        {
            string variable = "1=1";
            if (!string.IsNullOrEmpty(tcp_name))
                variable += "and [tcp_name]='" + tcp_name.Trim() + " '";
            if (!string.IsNullOrEmpty(tcc_name))
                variable += "and [tcc_name]='" + tcc_name.Trim() + " '";

            string sql = "select {0} from dbo.template_category_view where " + variable;
            PageResultData data=DbHelp_MarketingShops.paging<management>(sql, "*", Page.sidx, Page.page, Page.rows);
            List<management> rowsdata = data.rows as List<management>;
            rowsdata = rowsdata.GroupBy(i => i.tcc_id).Select(r => r.First()).ToList();//根据栏目分组
            data.rows = rowsdata;
            return data;

        }
        /// <summary>
        /// 获取详情信息
        /// </summary>
        /// <param name="tcc_id">关联表id</param>
        /// <returns></returns>
        public  List<management> GetDetailsInfo(int tcc_id)
        {
           //List<management> data = DbHelp_MarketingShops.ado.GetDataList<management>("select * from template_ategory_view where tcc_id=" + tcc_id);
           //if (data != null && data.Count > 0)
           //{
           //    List<item> itemlist = DbHelp_MarketingShops.ado.GetDataList<item>("select * from template_category_item");
           //    if (itemlist != null && itemlist.Count > 0)
           //    {
           //        for (int i = 0; i < data.Count; i++)
           //        {

           //            for (int j = 0; j < itemlist.Count; j++)
           //            {
           //                if (itemlist[j].tci_id == data[i].tci_id)
           //                {
           //                    data[i].tci_name = itemlist[j].tci_name;
           //                }
           //            }
           //        }
           //    }
           //}
           //return data;
            return DbHelp_MarketingShops.ado.GetDataList<management>("select * from template_category_view where tcc_id=" + tcc_id); 
        }
       
        public int AddConfig(List<management> obj)
        {
            var userinfo = OperatorProvider.Provider.GetCurrent();
            StringBuilder sb = new StringBuilder("declare @name varchar(50)");
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            for (int i = 0; i < obj.Count; i++) 
            {
                sb.Append("if not exists(select * from template_category_join_project_column where tcp_id=" + obj[i].tcp_id + " and tcc_id=" + obj[i].tcc_id + " and tci_id=" + obj[i].tci_id + ")  ");
                sb.Append("begin ");
                sb.Append(@"insert into template_category_join_project_column (tcp_id,tcc_id,tci_id,tcpj_enableshow,tcpj_sku)values(" + obj[i].tcp_id + "," + obj[i].tcc_id + "," + obj[i].tci_id + ",'" + obj[i].tcpj_enableshow + "','" + obj[i].tcpj_sku + "')  ");
                sb.Append(@"select @name=tcc_name from dbo.template_category_column where tcc_id=" + obj[i].tcc_id + "");  
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
               ([type] ,[table_name] ,[edittime]
               ,[userid]
               ,[username]
               ,[json],[primary],[title])
         VALUES
               ('insert','template_category_join_project_column', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + obj[i].ToJson() + "',IDENT_CURRENT('[template_category_join_project_column]') ,@name)  ");
                sb.Append("end ");
            }
            sb.Append("Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
       /// <summary>
       /// 修改配置信息
       /// </summary>
       /// <param name="obj"></param>
       /// <returns></returns>
        public int UpdateConfig(List<management> obj) 
       {
            var userinfo = OperatorProvider.Provider.GetCurrent();
            StringBuilder sb = new StringBuilder();
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            for (int i = 0; i < obj.Count; i++) 
            {
                sb.Append("if not exists(select * from template_category_join_project_column where tcp_id=" + obj[i].tcp_id + " and tcc_id=" + obj[i].tcc_id + " and tci_id=" + obj[i].tci_id + ")  ");
                sb.Append("begin ");
                sb.Append(@"insert into template_category_join_project_column (tcp_id,tcc_id,tci_id,tcpj_enableshow,tcpj_sku)values(" + obj[i].tcp_id + " ," + obj[i].tcc_id + "," + obj[i].tci_id + ",'" + obj[i].tcpj_enableshow + "','" + obj[i].tcpj_sku + "')  ");
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
               ([type] ,[table_name] ,[edittime]
               ,[userid]
               ,[username]
               ,[json],[primary],[title])
         VALUES
               ('insert','template_category_join_project_column', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + obj[i].ToJson() + "',IDENT_CURRENT('[template_category_join_project_column]') ,'" + obj[i].tcc_name + "')  ");
                sb.Append("end ");
                sb.Append("else ");
                sb.Append("begin ");
                sb.Append(@"update template_category_join_project_column set tcp_id=" + obj[i].tcp_id + ",tci_id=" + obj[i].tci_id + ",tcpj_enableshow='" + obj[i].tcpj_enableshow + "',tcpj_sku='" + obj[i].tcpj_sku + "' where tcpj_id=" + obj[i].tcpj_id + " ");
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
               ([type] ,[table_name] ,[edittime]
               ,[userid]
               ,[username]
               ,[json],[primary],[title])
         VALUES
               ('update','template_category_join_project_column', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + obj[i].ToJson() + "','" + obj[i].tcpj_id + "','" + obj[i].tcc_name + "')  ");

                sb.Append("end ");
            }
            sb.Append("Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
       }
       /// <summary>
       /// 删除栏目
       /// </summary>
       /// <param name="tcc_id"></param>
       /// <returns></returns>
        public int DeleteConfig(int tcc_id) 
        {
            var userinfo = OperatorProvider.Provider.GetCurrent();
           //获取栏目详情
            List<management> data = GetDetailsInfo(tcc_id);// DbHelp_MarketingShops.ado.GetDataList<management>("select * from dbo.template_ategory_view where tcc_id=" + tcc_id);
            StringBuilder sb = new StringBuilder("SET XACT_ABORT ON   Begin Tran ");
            sb.Append("delete from template_category_join_project_column where tcc_id=" + tcc_id);
            sb.Append(@" INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
               ([type] ,[table_name] ,[edittime]
               ,[userid]
               ,[username]
               ,[json],[primary],[title])
         VALUES
               ('delete','template_category_join_project_column', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "','" + tcc_id + "','" + data[0].tcc_name + "')  ");
            sb.Append("Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
        /// <summary>
        /// 获取项目信息
        /// </summary>
        /// <returns></returns>
        public List<project> GetProjectInfo() 
        {
            return DbHelp_MarketingShops.ado.GetDataList<project>("select * from template_category_project where tcp_enableshow=1");
        }
        /// <summary>
        /// 获取栏目信息
        /// </summary>
        /// <returns></returns>
        public List<column> GetColumnInfo()
        {
            return DbHelp_MarketingShops.ado.GetDataList<column>("select * from template_category_column where tcc_enableshow=1");
        }
        /// <summary>
        /// 获取项信息
        /// </summary>
        /// <returns></returns>
        public List<item> GetItemInfo()
        {
            return DbHelp_MarketingShops.ado.GetDataList<item>("select * from template_category_item");
        }
       
    }
}
