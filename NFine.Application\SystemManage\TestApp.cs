﻿using NFine.Code;
using NFine.Domain._03_Entity.TestManage;
using NFine.Domain._04_IRepository.SystemManage;
using NFine.Repository.SystemManage;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Application.SystemManage
{
    public class TestApp
    {

        private IStableRepository service = new TestRepository();

        public List<StableEntity> GetList()
        {
            
            return service.FindList("select* from stable").ToList();
        }
    }

}
