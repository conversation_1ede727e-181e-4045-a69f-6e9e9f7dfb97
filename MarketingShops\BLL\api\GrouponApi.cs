﻿using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.api
{
    /// <summary>
    /// 会员购api
    /// </summary>
    public class GrouponApi
    {
       public string baseurl = "http://183.63.130.69:88/ExecUse/Index?Ex=Ex_NGrouponInfo";
        /// <summary>
        /// 分页页面查询
        /// </summary>
        /// <param name="url">api接口地址</param>
        /// <param name="condition">查询条件</param>
        /// <param name="PageIndex">页码</param>
        /// <param name="PageSize">页数</param>
        /// <returns>json</returns>
        public string GetGridJson(string condition, int PageIndex, int PageSize)
        {
            string con = "";
            baseurl += "&t=0&PageIndex=" + PageIndex + "&PageSize=" + PageSize + "&IsGetCount=1";
            if (!string.IsNullOrEmpty(condition)) con = condition;
            string json = HttpMethods.HttpPost(baseurl + con);
            json = json.Trim().TrimStart('(').TrimEnd(')');
            return json;
        }
        /// <summary>
        /// 查询
        /// </summary>
        /// <param name="url">api接口地址</param>
        /// <param name="condition">查询条件</param>
        /// <returns></returns>
        public string GetDataJosn(string condition="")
        {
            string json = HttpMethods.HttpPost(baseurl + condition);
            json = json.Trim().TrimStart('(').TrimEnd(')');
            return json;
        }
    }
}
