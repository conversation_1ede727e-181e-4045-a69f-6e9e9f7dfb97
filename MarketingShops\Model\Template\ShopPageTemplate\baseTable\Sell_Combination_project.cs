﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Template.PageShopTemplate
{
    public class Sell_Combination_project
    {
        #region Model
        private string _key;
        private string _templateno;
        private string _title;
        private string _banner;
        private bool _isshowmember = false;
        private int _state;
        private DateTime _btime;
        private DateTime _etime;
        private string _describe;
       // private DateTime _intime = DateTime.Now;
        /// <summary>
        /// 组合购key
        /// </summary>
        public string KEY
        {
            set { _key = value; }
            get { return _key; }
        }
        /// <summary>
        /// 模版编号
        /// </summary>
        public string TemplateNo
        {
            set { _templateno = value; }
            get { return _templateno; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string Title
        {
            set { _title = value; }
            get { return _title; }
        }
        /// <summary>
        /// 广告图
        /// </summary>
        public string Banner
        {
            set { _banner = value; }
            get { return _banner; }
        }
        /// <summary>
        /// 是否展示会员信息
        /// </summary>
        public bool IsShowMember
        {
            set { _isshowmember = value; }
            get { return _isshowmember; }
        }
        /// <summary>
        /// 状态
        /// </summary>
        public int State
        {
            set { _state = value; }
            get { return _state; }
        }
        /// <summary>
        /// 活动开始时间
        /// </summary>
        public DateTime BTime
        {
            set { _btime = value; }
            get { return _btime; }
        }
        /// <summary>
        /// 活动结束时间
        /// </summary>
        public DateTime ETime
        {
            set { _etime = value; }
            get { return _etime; }
        }
        /// <summary>
        /// 描述
        /// </summary>
        public string Describe
        {
            set { _describe = value; }
            get { return _describe; }
        }
        /// <summary>
        /// 录入时间
        /// </summary>
        //public DateTime InTime
        //{
        //    set { _intime = value; }
        //    get { return _intime; }
        //}

        #endregion Model
       
    }
}
