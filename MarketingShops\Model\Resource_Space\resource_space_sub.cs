﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Img_Manage
{
    public class resource_space_sub
    {
       /// <summary>
       /// 自增id
       /// </summary>
       public int s_id { get; set; }
       /// <summary>
       /// 名称
       /// </summary>
       public string s_name { get; set; }
       /// <summary>
       /// 项目id
       /// </summary>
       public int p_id { get; set; }
       /// <summary>
       /// 路径
       /// </summary>
       public string s_baseurl { get; set; }
       /// <summary>
       /// 限制格式
       /// </summary>
       public string s_format { get; set; }
        /// <summary>
        /// 图标标识
        /// </summary>
       public string s_icon { get; set; }

       /// <summary>
       /// 限制格式集合
       /// </summary>
       public string[] formatlist
       {
           get
           {
               if (!string.IsNullOrEmpty(s_format))
               {
                   return s_format.Split('|');
               }
               else { 
                return null;
               }
              
           }
       }
      
       ///// <summary>
       ///// 限制格式集合
       ///// </summary>
       //public  List<string> formatlist
       //{
       //    get
       //    {
       //        List<string> resultdata = new List<string>();
       //        if (!string.IsNullOrEmpty(s_format))
       //        {
       //            string[] data = s_format.Split('|');
       //            foreach (var item in data)
       //            {
       //                resultdata.Add(item);
       //            }                  
       //        }
       //        return resultdata;
             
       //    }
       //}

    }
}
