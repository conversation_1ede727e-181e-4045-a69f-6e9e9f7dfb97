﻿using MarketingShops.Model;
using MarketingShops.Model.Draw;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL
{
  public  class draw_join_dal
    {
      /// <summary>
      /// 获取所有参与数据
      /// </summary>
      /// <param name="seltj"></param>
      /// <param name="Page"></param>
      /// <returns></returns>
      public PageResultData GetAllDrawJoin_Page(string seltj, Pagination Page)
      {
          string sql = (@"select {0} from draw_join a join draw_join_state b on a.j_stateid = b.j_stateid join draw_config c on a.c_id= c.c_id join draw d on a.d_id = d.d_id left join draw_win e on a.j_id=e.j_id where " + seltj + "");
          return DbHelp_MarketingShops.paging<draw_win>(sql, "a.*,b.j_statename,c.c_name,d.d_name,isnull(e.wintype,0)as wintype", Page.sidx, Page.page, Page.rows);
      }

      ///// <summary>
      ///// 获取所有预定中奖数据
      ///// </summary>
      ///// <param name="seltj"></param>
      ///// <returns></returns>
      //public PageResultData GetWinDrawJoin_Page(string seltj, Pagination Page)
      //{
      //    string sql = ("select {0} from draw_join a join draw_join_state b on a.j_stateid = b.j_stateid join draw_config c on a.c_id= c.c_id join draw d on a.d_id = d.d_id  join draw_win e on a.j_id=e.j_id where " + seltj + "");
      //    return DbHelp_MarketingShops.paging<draw_win>(sql, "a.*,b.j_statename,c.c_name,d.d_name,isnull(e.wintype,0)as wintype", Page.sidx, Page.page, Page.rows);
      //}


      /// <summary>
      /// 获取抽取方法需要的基础数据
      /// </summary>
      /// <param name="seltj"></param>
      /// <returns></returns>
      public List<draw_win> GetDrawBaseData(string seltj)
      {
          string sql = ("select a.*,isnull(b.wintype,0)as wintype from draw_join a left join  draw_win b on a.j_id= b.j_id where " + seltj + "");
          return DbHelp_MarketingShops.ado.GetDataList<draw_win>(sql);
      }

      /// <summary>
      /// 获取已预设抽取数据
      /// </summary>
      /// <param name="seltj"></param>
      /// <returns></returns>
      public List<draw_win> GetDrawWinData(string seltj)
      {
          string sql = ("select * from draw_join a join draw_win b on a.j_id = b.j_id  where " + seltj + " ");
          return DbHelp_MarketingShops.ado.GetDataList<draw_win>(sql);
      }


      /// <summary>
      /// 添加预中奖
      /// </summary>
      /// <param name="j_id"></param>
      /// <returns></returns>
      public int AddDraw_Win(int j_id) {
          string sql = "insert draw_win (j_id,j_stateid,wintype) values ('" + j_id + "',3,2)";
          return DbHelp_MarketingShops.ado.OperationData(sql);
      
      }
      /// <summary>
      /// 删除预中奖
      /// </summary>
      /// <param name="j_id"></param>
      /// <returns></returns>
      public int DelDraw_Win(int j_id)
      {
          string sql = "delete from draw_win where j_id="+j_id+"";
          return DbHelp_MarketingShops.ado.OperationData(sql);

      }


    }
}
