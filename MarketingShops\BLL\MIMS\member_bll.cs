﻿using MarketingShops.DAL.MIMS;
using MarketingShops.Model.trade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.MIMS
{
    /// <summary>
    /// 会员数据管理
    /// </summary>
   public static class member_bll
    {
        /// <summary>
        /// 获取会员信息
        /// </summary>
        /// <param name="MemberKey">会员key</param>
        /// <returns>会员信息</returns>
       public static MemberInfo GetMemberInfoByKey(string memberkey) 
       {
           return member_dal.GetMemberInfoByKey(memberkey);
       }
    }
}
