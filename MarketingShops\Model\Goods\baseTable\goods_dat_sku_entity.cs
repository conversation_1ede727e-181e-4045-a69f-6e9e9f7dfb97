﻿using MarketingShops.Model.Goods;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Goods.baseTable
{
    /// <summary>
    /// Sku-规格库存
    /// </summary>
   public class goods_dat_sku_entity 
    {
       /// <summary>
       /// 规格编号
       /// </summary>
        public string sku_id { get; set; }
       /// <summary>
       /// 名称
       /// </summary>
        public string name { get; set; }
       /// <summary>
        /// 库存
       /// </summary>
        public int stock { get; set; }
       /// <summary>
       /// 图片
       /// </summary>
        public string photo { get; set; }
       /// <summary>
       /// 原价
       /// </summary>
        public double yprice { get; set; }
       /// <summary>
       /// 售价
       /// </summary>
        public double sprice { get; set; }
       /// <summary>
       /// 商家编号
       /// </summary>
       public string code { get; set; }
       /// <summary>
       /// 销售量
       /// </summary>
       public int salenum { get; set; }
       /// <summary>
       /// 重量
       /// </summary>
       public double weight { get; set; }
       /// <summary>
       /// 特殊支付方式数量
       /// </summary>
       public int paynum { get; set; }
       /// <summary>
       /// 
       /// </summary>
       public string sku_list_json { get; set; }

       /// <summary>
       /// 支付信息
       /// </summary>
       public List<goods_dat_sku_paymentMethod> paymentMethod { get; set; }


    }
}
