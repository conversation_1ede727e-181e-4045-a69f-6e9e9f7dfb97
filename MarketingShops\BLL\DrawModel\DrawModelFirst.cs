﻿using MarketingShops.Model.Draw;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.DrawModel
{
    /// <summary>
    /// 优先抽取
    /// </summary>
   public class DrawModelFirst:DrawModelBase
    {
       public override List<draw_win> GetWinData()
       {
           List<draw_win> data1 = new List<draw_win>();
           return data1;
       }
    }
}
