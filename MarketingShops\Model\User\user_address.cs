﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.User
{
    public class user_address
    {
        /// <summary>
        /// 收件地址ID
        /// </summary>
        public int ud_id { get; set; }
        /// <summary>
        /// 用户编号
        /// </summary>
        public string openid { get; set; }
        /// <summary>
        /// 收件人
        /// </summary>
        public string name { get; set; }
        /// <summary>
        /// 联系电话
        /// </summary>
        public string phone { get; set; }
        /// <summary>
        /// 地区
        /// </summary>
        public string region { get; set; }
        /// <summary>
        /// 详细地址
        /// </summary>
        public string detailed { get; set; }
        /// <summary>
        /// 是否默认地址
        /// </summary>
        public bool isdefault { get; set; }
        /// <summary>
        /// 插入时间
        /// </summary>
        public DateTime inputtime { get; set; }
    }
}
