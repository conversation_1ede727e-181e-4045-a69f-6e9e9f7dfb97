﻿using MarketingShops.Model.Draw;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.DrawModel
{
    /// <summary>
    /// 随机抽取
    /// </summary>
    public class DrawModelRandom:DrawModelBase
    {
        public override List<draw_win> GetWinData()
        {
            List<draw_win> JoinList = this.GetDrawBaseData();
            List<draw_win> resultlist = new List<draw_win>();
            int winnum = JoinList.FindAll(a => a.wintype != 0).Count; //已经抽取的数
            int neednum = this.data.drawnum - winnum;
            if (neednum < 0)
                neednum = 0;

            List<draw_win> basedata = JoinList.FindAll(a=>a.wintype ==0).ToList(); //未抽取的数据
            basedata = basedata.OrderBy(x => Guid.NewGuid()).ToList(); //随机排序集合

            foreach (var item in basedata)
            {
                if (resultlist.Count < neednum)
                {
                    resultlist.Add(item);
                }
                else
                    break;
            }
            return resultlist;
        }

    }
}
