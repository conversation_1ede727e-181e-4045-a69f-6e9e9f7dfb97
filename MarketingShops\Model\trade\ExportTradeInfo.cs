﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.trade
{
  public class ExportTradeInfo
  {
      #region Model
        private string _orderid;
        private string _transactionid;
        private string _name;
        private string _phone;
        public string _memberPhoneNumber;
        private string _region;
        private string _detailed;
        private DateTime? _paytime;
        private DateTime _intime = DateTime.Now;
        private string _g_name;
        private int _total_num;
        private decimal _total_fee = 0M;
        private decimal _cash_fee = 0M;
        private decimal _total_goodsfee = 0M;
        private decimal _discount_fee = 0M;
        private decimal _freight_fee = 0M;
        private int _total_integral = 0;
        private int _total_recharge = 0;
        private int _total_weight = 0;
        private string _company;
        private string _mail_number;
        private string _userid;

        /// <summary>
        /// 订单编号
        /// </summary>
      [Description("订单编号")] 
      public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
      /// <summary>
      /// 交易凭证
      /// </summary>
      [Description("交易凭证")] 
      public string transactionId
      {
          set { _transactionid = value; }
          get { return _transactionid; }
      }
        /// <summary>
        /// 收件人名称
        /// </summary>
      [Description("顾客姓名")]  
      public string name
        {
            set { _name = value; }
            get { return _name; }
        }
        /// <summary>
        /// 收件人电话
        /// </summary>
      [Description("顾客电话")]  
      public string phone
        {
            set { _phone = value; }
            get { return _phone; }
        }
      /// <summary>
      /// 会员电话
      /// </summary>
       [Description("会员电话")]
      public string memberPhoneNumber
        {
            set { _memberPhoneNumber = value; }
            get { return _memberPhoneNumber; }
        }
      
        /// <summary>
        /// 区域信息
        /// </summary>
      [Description("区域信息")] 
      public string region
        {
            set { _region = value; }
            get { return _region; }
        }
        /// <summary>
        /// 详细地址
        /// </summary>
      [Description("详细地址")]  
      public string detailed
        {
            set { _detailed = value; }
            get { return _detailed; }
        }
        /// <summary>
        /// 商品名称
        /// </summary>
      [Description("商品")]  
      public string g_name
        {
            set { _g_name = value; }
            get { return _g_name; }
        }
        /// <summary>
        /// 购买数量
        /// </summary>
      [Description("总数")]  
      public int total_num
        {
            set { _total_num = value; }
            get { return _total_num; }
        }
      /// <summary>
      /// 实际支付金额
      /// </summary>
      [Description("实付金额")]
      public decimal cash_fee
      {
          set { _cash_fee = value; }
          get { return _cash_fee; }
      }
      /// <summary>
      /// 总支付金额
      /// </summary>
      [Description("应付金额")]
      public decimal total_fee
      {
          set { _total_fee = value; }
          get { return _total_fee; }
      }

      /// <summary>
      /// 商品金额
      /// </summary>
      [Description("商品金额")]
      public decimal total_goodsfee
      {
          set { _total_goodsfee = value; }
          get { return _total_goodsfee; }
      }
      /// <summary>
      /// 优惠金额/分
      /// </summary>
      [Description("优惠金额")]
      public decimal discount_fee
      {
          set { _discount_fee = value; }
          get { return _discount_fee; }
      }
      /// <summary>
      /// 运费
      /// </summary>
      [Description("运费")]
      public decimal freight_fee
      {
          set { _freight_fee = value; }
          get { return _freight_fee; }
      }
      /// <summary>
      /// 总积分
      /// </summary>
      [Description("积分")]
      public int total_integral
      {
          set { _total_integral = value; }
          get { return _total_integral; }
      }
      /// <summary>
      /// 充值
      /// </summary>
      [Description("充值")]
      public int total_recharge
      {
          set { _total_recharge = value; }
          get { return _total_recharge; }
      }
      /// <summary>
       /// 总重量
      /// </summary>
       [Description("总重量(g)")]
      public int total_weight
      {
          set { _total_weight = value; }
          get { return _total_weight; }
      }
       /// <summary>
       /// 下单时间
       /// </summary>
       [Description("下单时间")]
       public DateTime InTime
       {
           set { _intime = value; }
           get { return _intime; }
       }
      /// <summary>
      /// 支付时间
      /// </summary>
      [Description("支付时间")]
      public DateTime? payTime
      {
          set { _paytime = value; }
          get { return _paytime; }
      }
      
        /// <summary>
        /// 物流公司
        /// </summary>
      [Description("物流公司")] 
      public string company
        {
            set { _company = value; }
            get { return _company; }
        }
      [Description("物流单号")]
      public string mail_number
      {
          set { _mail_number = value; }
          get { return _mail_number; }
      }
        /// <summary>
        /// 操作员工
        /// </summary>
      [Description("操作员工")] 
      public string Userid
        {
            set { _userid = value; }
            get { return _userid; }
        }
     
      #endregion
  }
}
