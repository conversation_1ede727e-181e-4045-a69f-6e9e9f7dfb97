﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7BEFE33B-32C5-4B8F-AA5A-42B3F64ECDE9}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>MarketingShops</RootNamespace>
    <AssemblyName>MarketingShops</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="HDtool, Version=1.0.0.2, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>DLL\HDtool.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BLL\api\GrouponApi.cs" />
    <Compile Include="BLL\MemberPurchase\api_bll.cs" />
    <Compile Include="BLL\MemberPurchase\base_bll.cs" />
    <Compile Include="BLL\MIMS\member_bll.cs" />
    <Compile Include="BLL\Miniapp\trade\trade_mini_bll.cs" />
    <Compile Include="BLL\RefundTardeDetailed\refund_bill_detailed_bll.cs" />
    <Compile Include="BLL\RefundTarde\therefunddetail_bll.cs" />
    <Compile Include="BLL\refund\refund_bll.cs" />
    <Compile Include="BLL\Report\MidAutumn_Bll.cs" />
    <Compile Include="BLL\trade\trade_contacts_bll.cs" />
    <Compile Include="BLL\trade\trade_export_bll.cs" />
    <Compile Include="BLL\trade\trade_freight_bll.cs" />
    <Compile Include="BLL\trade\trade_member_bll.cs" />
    <Compile Include="BLL\trade\trade_order_bll.cs" />
    <Compile Include="BLL\trade\trade_holder.cs" />
    <Compile Include="BLL\Category\column_bll.cs" />
    <Compile Include="BLL\Category\dictionary_bll.cs" />
    <Compile Include="BLL\Category\item_bll.cs" />
    <Compile Include="BLL\Category\management_bll.cs" />
    <Compile Include="BLL\Category\project_bll.cs" />
    <Compile Include="BLL\Classification\Classification_Bll.cs" />
    <Compile Include="BLL\DrawModel\DrawModelBase.cs" />
    <Compile Include="BLL\DrawModel\DrawModelFirst.cs" />
    <Compile Include="BLL\DrawModel\DrawModelRandom.cs" />
    <Compile Include="BLL\draw_bll.cs" />
    <Compile Include="BLL\draw_join_bll.cs" />
    <Compile Include="BLL\Factory\DrawFactory.cs" />
    <Compile Include="BLL\goods_bll.cs" />
    <Compile Include="BLL\LogEdit\LogEdit_bll.cs" />
    <Compile Include="BLL\resource_space_file_bll.cs" />
    <Compile Include="BLL\resource_spacesub_bll.cs" />
    <Compile Include="BLL\Template\ShopPageBLL.cs" />
    <Compile Include="BLL\trade\trade_order_details_bll.cs" />
    <Compile Include="BLL\trade\trade_refune_bll.cs" />
    <Compile Include="BLL\user_address_bll.cs" />
    <Compile Include="BLL\user_bll.cs" />
    <Compile Include="Class1.cs" />
    <Compile Include="DAL\MemberPurchase\MemberPurchase_dal.cs" />
    <Compile Include="DAL\MIMS\member_dal.cs" />
    <Compile Include="DAL\Miniapp\trade\trade_mini_dal.cs" />
    <Compile Include="DAL\RefundTardeDetailed\refund_bill_detailed_dal.cs" />
    <Compile Include="DAL\RefundTarde\therefunddetail_dal.cs" />
    <Compile Include="DAL\refund\refund_dal.cs" />
    <Compile Include="DAL\Report\MidAutumn_Dal.cs" />
    <Compile Include="DAL\trade\trade_contacts_dal.cs" />
    <Compile Include="DAL\Category\column_dal.cs" />
    <Compile Include="DAL\Category\dictionary_dal.cs" />
    <Compile Include="DAL\Category\item_dal.cs" />
    <Compile Include="DAL\Category\management_dal.cs" />
    <Compile Include="DAL\Category\project_dal.cs" />
    <Compile Include="DAL\Classification\Classification_Dal.cs" />
    <Compile Include="DAL\DbHelp_MarketingShops.cs" />
    <Compile Include="DAL\draw_dal.cs" />
    <Compile Include="DAL\draw_join_dal.cs" />
    <Compile Include="DAL\goods_dal.cs" />
    <Compile Include="DAL\LogEdit\LogEdit_dal.cs" />
    <Compile Include="DAL\resource_space_file_dal.cs" />
    <Compile Include="DAL\resource_space_sub_dal.cs" />
    <Compile Include="DAL\Template\ShopPageDAL.cs" />
    <Compile Include="DAL\trade\trade_export_dal.cs" />
    <Compile Include="DAL\trade\trade_freight_dal.cs" />
    <Compile Include="DAL\trade\trade_order_dal.cs" />
    <Compile Include="DAL\tradedetails\trade_order_details_dal.cs" />
    <Compile Include="DAL\trade\trade_search_dal.cs" />
    <Compile Include="DAL\user_address_dal.cs" />
    <Compile Include="DAL\user_dal.cs" />
    <Compile Include="DAL\DbHelp_MIMS.cs" />
    <Compile Include="Model\Goods\enumTable\good_enum_freight.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NAreaInfo.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NBuildModel.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NCardLable.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NCardModel.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NHtmlTemplate.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NSaleModel.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NShopInfo.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\NValidModel.cs" />
    <Compile Include="Model\MemberPurchase\baseTable\projectModel.cs" />
    <Compile Include="Model\MemberPurchase\purchase_details.cs" />
    <Compile Include="Model\MemberPurchase\search\purchase_search_data.cs" />
    <Compile Include="Model\MemberPurchase\search\purchase_search_params.cs" />
    <Compile Include="Model\RefundTardeDetailed\Export\export_refund_bill_detailed.cs" />
    <Compile Include="Model\RefundTardeDetailed\Search\refund_bill_detailed.cs" />
    <Compile Include="Model\RefundTarde\ExportRefund.cs" />
    <Compile Include="Model\Report\MidAutumnModel.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\search\sell_search_params.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\baseTable\Sell_Combination_luck.cs" />
    <Compile Include="Model\RefundTarde\Search\the_refund_detail.cs" />
    <Compile Include="Model\dataPost.cs" />
    <Compile Include="Model\trade\ExportTradeInfo.cs" />
    <Compile Include="Model\MIMS\MemberInfo.cs" />
    <Compile Include="Model\trade\search\trade_search_params.cs" />
    <Compile Include="Model\trade\tradedetails\Exportbilldetailed.cs" />
    <Compile Include="Model\trade\tradedetails\MSDetails.cs" />
    <Compile Include="Model\trade\TradeInfo.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_mail.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_contacts.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_core.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_detailed.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_detailed_cost.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_detailed_paymentMethod.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_payment.cs" />
    <Compile Include="Model\trade\baseTable\buy_bill_secondary.cs" />
    <Compile Include="Model\trade\search\trade_search_data.cs" />
    <Compile Include="Model\Category\column.cs" />
    <Compile Include="Model\Category\item.cs" />
    <Compile Include="Model\Category\item_dictionary.cs" />
    <Compile Include="Model\Category\management.cs" />
    <Compile Include="Model\Category\project.cs" />
    <Compile Include="Model\Classification\goods_classification.cs" />
    <Compile Include="Model\Classification\ClassificationBase.cs" />
    <Compile Include="Model\Draw\draw.cs" />
    <Compile Include="Model\Draw\draw_config.cs" />
    <Compile Include="Model\Draw\draw_enum_model.cs" />
    <Compile Include="Model\Draw\draw_enum_model_enum.cs" />
    <Compile Include="Model\Draw\draw_enum_state.cs" />
    <Compile Include="Model\Draw\draw_join.cs" />
    <Compile Include="Model\Draw\draw_join_state.cs" />
    <Compile Include="Model\Draw\draw_win.cs" />
    <Compile Include="Model\Goods\GoodEditBaseModel.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_sku_paymentMethod_ex.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_details.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_details_material.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_entity_ex.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_sku_paymentMethod.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_spu_entity.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_spu_entity_list.cs" />
    <Compile Include="Model\Goods\baseTable\goods_logistics.cs" />
    <Compile Include="Model\Goods\baseTable\goods_manage_valid.cs" />
    <Compile Include="Model\Goods\search\goods_search_data.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Model\Goods\enumTable\good_enum_column.cs" />
    <Compile Include="Model\Goods\search\good_table.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="Model\Goods\search\goods_search_params.cs" />
    <Compile Include="Model\Goods\templatedata_model.cs" />
    <Compile Include="Model\Goods\GetGoodModel.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_child.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_entity.cs" />
    <Compile Include="Model\Goods\EditGoodModel.cs" />
    <Compile Include="Model\Goods\good_fill.cs" />
    <Compile Include="Model\Goods\good_item.cs" />
    <Compile Include="Model\Goods\enumTable\good_enum_merchanty.cs" />
    <Compile Include="Model\Goods\enumTable\good_enum_system.cs" />
    <Compile Include="Model\Goods\baseTable\goods_dat_sku_entity.cs" />
    <Compile Include="Model\LogEdit\ClassificationLog.cs" />
    <Compile Include="Model\LogEdit\log_marketing_edit.cs" />
    <Compile Include="Model\Resource_Space\resource_space_file.cs" />
    <Compile Include="Model\Resource_Space\resource_space_sub.cs" />
    <Compile Include="Model\PageResultData.cs" />
    <Compile Include="Model\ResultModel.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\edit\GoodList.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\edit\sell_edit_model.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\edit\GetSellInfo.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\baseTable\Sell_Combination_goods_item.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\search\sell_search_data.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\baseTable\Sell_Combination_goods_project.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\baseTable\Sell_Combination_project.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\enumTable\state.cs" />
    <Compile Include="Model\Template\ShopPageTemplate\enumTable\template.cs" />
    <Compile Include="Model\User\userinfo.cs" />
    <Compile Include="Model\User\user_address.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="DLL\Dapper.dll" />
    <Content Include="DLL\HDtool.dll" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\NFine.Code\NFine.Code.csproj">
      <Project>{db19ce03-c307-43fe-a209-08aa4ae10e21}</Project>
      <Name>NFine.Code</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Model\RefundTarde\param\" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>