﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.trade.Search
{
   public class trade_search_params
   {
        #region Model
        private bool _isrefund;
        private int? _billstate;
        private int _total_num=-1;
        private string _btime;
        private string _etime;
        private string _orderid ;
        private string _phone;
        private string _memberPhoneNumber;
        private int? _discount_fee1=-1;
        private int? _discount_fee2=-1;

        public bool isRefund
        {
            set { _isrefund = value; }
            get { return _isrefund; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int? billState
        {
            set { _billstate = value; }
            get { return _billstate; }
        }
        /// <summary>
        /// 
        /// </summary>
        public int total_num
        {
            set { _total_num = value; }
            get { return _total_num; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string BTime
        {
            set { _btime = value; }
            get { return _btime; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string ETime
        {
            set { _etime = value; }
            get { return _etime; }
        }
        /// <summary>
        /// 
        /// </summary>
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        public string phone
        {
            set { _phone = value; }
            get { return _phone; }
        }
        public string memberPhoneNumber
        {
            set { _memberPhoneNumber = value; }
            get { return _memberPhoneNumber; }
        }
        
        public int? discount_fee1
        {
            set { _discount_fee1 = value; }
            get { return _discount_fee1; }
        }
        public int? discount_fee2
        {
            set { _discount_fee2 = value; }
            get { return _discount_fee2; }
        }
        #endregion
   }
}
