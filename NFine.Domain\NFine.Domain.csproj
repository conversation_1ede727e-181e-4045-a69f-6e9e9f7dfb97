﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{7DC886CD-B8A0-44E8-AADD-57FCB0CFECB8}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NFine.Domain</RootNamespace>
    <AssemblyName>NFine.Domain</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="01 Infrastructure\ICreationAudited.cs" />
    <Compile Include="01 Infrastructure\IDeleteAudited.cs" />
    <Compile Include="01 Infrastructure\IEntity.cs" />
    <Compile Include="01 Infrastructure\IModificationAudited.cs" />
    <Compile Include="03 Entity\SystemManage\AreaEntity.cs" />
    <Compile Include="03 Entity\SystemManage\FileDownLoadEntity.cs" />
    <Compile Include="03 Entity\SystemManage\ModuleButtonEntity.cs" />
    <Compile Include="03 Entity\SystemManage\ModuleEntity.cs" />
    <Compile Include="03 Entity\SystemManage\RoleAuthorizeEntity.cs" />
    <Compile Include="03 Entity\SystemManage\SiteEntity.cs" />
    <Compile Include="03 Entity\SystemManage\UserLogOnEntity.cs" />
    <Compile Include="03 Entity\SystemManage\ItemsDetailEntity.cs" />
    <Compile Include="03 Entity\SystemManage\UserOwnEntity.cs" />
    <Compile Include="03 Entity\SystemSecurity\DbBackupEntity.cs" />
    <Compile Include="03 Entity\SystemSecurity\FilterIPEntity.cs" />
    <Compile Include="03 Entity\SystemSecurity\LogEntity.cs" />
    <Compile Include="02 ViewModel\AuthorizeActionModel.cs" />
    <Compile Include="03 Entity\TestManage\StableEntity.cs" />
    <Compile Include="04 IRepository\SystemManage\IFileDownLoadRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IAreaRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IItemsDetailRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IItemsRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IModuleButtonRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IModuleRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IOrganizeRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IRoleAuthorizeRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IRoleRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\ISiteRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IStableRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IUserLogOnRepository.cs" />
    <Compile Include="04 IRepository\SystemManage\IUserRepository.cs" />
    <Compile Include="04 IRepository\SystemSecurity\IDbBackupRepository.cs" />
    <Compile Include="04 IRepository\SystemSecurity\IFilterIPRepository.cs" />
    <Compile Include="04 IRepository\SystemSecurity\ILogRepository.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="03 Entity\SystemManage\ItemsEntity.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="03 Entity\SystemManage\OrganizeEntity.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="03 Entity\SystemManage\RoleEntity.cs" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="03 Entity\SystemManage\UserEntity.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\NFine.Code\NFine.Code.csproj">
      <Project>{db19ce03-c307-43fe-a209-08aa4ae10e21}</Project>
      <Name>NFine.Code</Name>
    </ProjectReference>
    <ProjectReference Include="..\NFine.Data\NFine.Data.csproj">
      <Project>{f71003e8-a836-48f4-9df6-df9095cebd18}</Project>
      <Name>NFine.Data</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>