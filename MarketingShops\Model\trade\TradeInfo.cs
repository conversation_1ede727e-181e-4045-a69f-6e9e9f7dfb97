﻿using MarketingShops.Model.trade.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.trade
{
    /// <summary>
    /// 购买商品
    /// </summary>
   public class TradeInfo
    {
       /// <summary>
       /// 账单
       /// </summary>
       public buy_bill_core bill_core { get; set; }
       /// <summary>
       /// 联系人
       /// </summary>
       public buy_bill_contacts address { get; set; }
       /// <summary>
       /// 商品明细
       /// </summary>
       public List<GoodListInfo> goodsList { get; set; }
       /// <summary>
       /// 邮寄信息
       /// </summary>
       public object mail { get; set; }
       
    }
    /// <summary>
    /// 商品明细model
    /// </summary>
    public class GoodListInfo : buy_bill_detailed
    {
        /// <summary>
        /// 商品支付方式
        /// </summary>
        public List<buy_bill_detailed_paymentMethod> PaymentMethod { get; set; }
      /// <summary>
       /// 账单信息-购买明细费用
      /// </summary>
        public buy_bill_detailed_cost cost { get; set; }
    }
}
