﻿/**  版本信息模板在安装目录下，可自行修改。
* buy_bill_core.cs
*
* 功 能： N/A
* 类 名： buy_bill_core
*
* Ver    变更日期             负责人  变更内容
* ───────────────────────────────────
* V0.01  2021-08-25 15:55:21   N/A    初版
*
* Copyright (c) 2012 Maticsoft Corporation. All rights reserved.
*┌──────────────────────────────────┐
*│　此技术信息为本公司机密信息，未经本公司书面同意禁止向第三方披露．　│
*│　版权所有：动软卓越（北京）科技有限公司　　　　　　　　　　　　　　│
*└──────────────────────────────────┘
*/
using System;
namespace MarketingShops.Model.trade.baseTable
{
	/// <summary>
	/// buy_bill_core:实体类(属性说明自动提取数据库字段的描述信息)
	/// </summary>
	[Serializable]
	public partial class buy_bill_core
	{
		public buy_bill_core()
		{}
        #region Model
        private string _orderid;
        private string _body;
        private string _mail_no;
      //  private string _contacts_no;
        private int _total_num;
        private decimal _total_fee = 0M;
        private decimal _cash_fee = 0M;
        private decimal _total_goodsfee = 0M;
        private decimal _discount_fee = 0M;
        private decimal _freight_fee = 0M;
        private int _total_integral = 0;
        private int _total_recharge = 0;
        private int _total_weight = 0;
        private string _openid;
        private string _userid;
        private string _memberkey;
        private string _memberPhoneNumber;
        private int _billstate = 1;
        private bool _isrefund = false;
        private string _scene;
        private DateTime? _inTime = DateTime.Now;
        private string _transactionid;
        /// <summary>
        /// 订单id
        /// </summary>
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }
        /// <summary>
        /// 商品名称
        /// </summary>
        public string body
        {
            set { _body = value; }
            get { return _body; }
        }
        /// <summary>
        /// 物流id
        /// </summary>
        public string mail_no
        {
            set { _mail_no = value; }
            get { return _mail_no; }
        }
        /// <summary>
        /// 收货人id
        /// </summary>
        //public string contacts_no
        //{
        //    set { _contacts_no = value; }
        //    get { return _contacts_no; }
        //}
        /// <summary>
        /// 总数
        /// </summary>
        public int total_num
        {
            set { _total_num = value; }
            get { return _total_num; }
        }
        /// <summary>
        /// 应付金额
        /// </summary>
        public decimal total_fee
        {
            set { _total_fee = value; }
            get { return _total_fee; }
        }
        /// <summary>
        /// 实付金额
        /// </summary>
        public decimal cash_fee
        {
            set { _cash_fee = value; }
            get { return _cash_fee; }
        }
        /// <summary>
        /// 商品金额
        /// </summary>
        public decimal total_goodsfee
        {
            set { _total_goodsfee = value; }
            get { return _total_goodsfee; }
        }
        /// <summary>
        /// 优惠金额
        /// </summary>
        public decimal discount_fee
        {
            set { _discount_fee = value; }
            get { return _discount_fee; }
        }
        /// <summary>
        /// 运费
        /// </summary>
        public decimal freight_fee
        {
            set { _freight_fee = value; }
            get { return _freight_fee; }
        }
        /// <summary>
        /// 积分
        /// </summary>
        public int total_integral
        {
            set { _total_integral = value; }
            get { return _total_integral; }
        }
        /// <summary>
        /// 充值
        /// </summary>
        public int total_recharge
        {
            set { _total_recharge = value; }
            get { return _total_recharge; }
        }
        /// <summary>
        /// 重量
        /// </summary>
        public int total_weight
        {
            set { _total_weight = value; }
            get { return _total_weight; }
        }
        /// <summary>
        /// openid
        /// </summary>
        public string openid
        {
            set { _openid = value; }
            get { return _openid; }
        }
        /// <summary>
        /// 操作员工
        /// </summary>
        public string Userid
        {
            set { _userid = value; }
            get { return _userid; }
        }
        /// <summary>
        /// 会员key
        /// </summary>
        public string memberKey
        {
            set { _memberkey = value; }
            get { return _memberkey; }
        }
        /// <summary>
        /// 会员电话
        /// </summary>
        public string memberPhoneNumber
        {
            set { _memberPhoneNumber = value; }
            get { return _memberPhoneNumber; }
        }
        
        /// <summary>
        /// 订单状态
        /// </summary>
        public int billState
        {
            set { _billstate = value; }
            get { return _billstate; }
        }
        /// <summary>
        /// 是否退款
        /// </summary>
        public bool isRefund
        {
            set { _isrefund = value; }
            get { return _isrefund; }
        }
        /// <summary>
        /// 场景
        /// </summary>
        public string scene {
            set { _scene = value; }
            get { return _scene; }
        }
        /// <summary>
        /// 录入时间
        /// </summary>
        public DateTime? inTime {
            set { _inTime = value; }
            get { return _inTime; }
        }
        public string transactionId
        {
            set { _transactionid = value; }
            get { return _transactionid; }
        }
        #endregion Model
	}
}

