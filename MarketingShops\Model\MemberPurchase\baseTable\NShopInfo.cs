﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.MemberPurchase.baseTable
{
   public class NShopInfo
    {
        /// <summary>
        /// 
        /// </summary>
        public int ShopID { get; set; }
        /// <summary>
        /// 缤缤店
        /// </summary>
        public string ShopName { get; set; }
        ///// <summary>
        ///// 位于海珠广场附近
        ///// </summary>
        //public string address { get; set; }
        ///// <summary>
        ///// 
        ///// </summary>
        //public string orderurl { get; set; }
        ///// <summary>
        ///// 
        ///// </summary>
        //public string GrouponShow { get; set; }
        ///// <summary>
        ///// 
        ///// </summary>
        //public string OpenDate { get; set; }
        ///// <summary>
        ///// 
        ///// </summary>
        //public string CloseDate { get; set; }
        ///// <summary>
        ///// 
        ///// </summary>
        //public string UrlTitle { get; set; }
        ///// <summary>
        /// 
        /// </summary>
        public bool IsUse { get; set; }
       

    }
}
