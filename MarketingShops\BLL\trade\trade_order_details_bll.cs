﻿using MarketingShops.DAL.tradedetails;
using MarketingShops.Model;
using MarketingShops.Model.trade.baseTable;
using MarketingShops.Model.trade.tradedetails;
using NFine.Code;
using NFine.Code.Export;
using System;
using System.Collections.Generic;

namespace MarketingShops.BLL.trade
{
    /// <summary>
    /// 商品销售明细
    /// </summary>
    public class trade_order_details_bll
    {
        trade_order_details_dal _dal = new trade_order_details_dal();

        /// <summary>
        /// 系统查询
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <param name="Page">分页信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(buy_bill_detailed sys, Pagination Page)
        {
            string seltj = GetSearchStr(sys);
            return _dal.GetGridInfo(seltj, Page);
        }

        /// <summary>
        /// 获取订单详情
        /// </summary>
        /// <param name="orderid">订单编号</param>
        /// <returns></returns>
        public object GetTradeInfo(string orderid)
        {
            return _dal.GetTradeInfo(orderid);
        }


        /// <summary>
        /// 获取查询SQL语句
        /// </summary>
        /// <param name="sys">查询条件</param>
        /// <returns></returns>
        string GetSearchStr(buy_bill_detailed sys)
        {
            string seltj = " 1 = 1 ";
            if (!string.IsNullOrEmpty(sys.orderid))//订单号模糊查询
                seltj += "and [orderid] like '%" + sys.orderid + "%'";

            if (!string.IsNullOrEmpty(sys.g_name))//商品名称模糊查询
                seltj += " and [g_name] like '%" + sys.g_name + "%'";

            if (!string.IsNullOrEmpty(sys.skuname))//规格名称模糊查询
                seltj += " and [skuname] like '%" + sys.skuname + "%'";

            if (!string.IsNullOrEmpty(sys.sku_id))//规格ID模糊查询
                seltj += " and [sku_id] like '%" + sys.sku_id + "%'";

            if (sys.weight > 0)//商品重量

                seltj += " and [weight]='" + sys.weight + "'";

            if (sys.buy_num > 0)//购买数量
                seltj += " and [buy_num]='" + sys.buy_num + "'";

            return seltj;
        }


        /// <summary>
        /// 导出报表
        /// </summary>
        /// <param name="sys">导出条件</param>
        /// <returns></returns>
        public void ExportTradeList(buy_bill_detailed sys)
        {
            string seltj = GetSearchStr(sys);
            string filename = "商品销售明细报表" + DateTime.Now.ToString("yyyy-MM-dd");
            string fileformat = "csv";
            List<Exportbilldetailed> data = _dal.ExportTradeList(seltj);
            ExportData ex = new ExportData();
            ex.ExportToExcel<Exportbilldetailed>(data, filename, fileformat);
        }
    }
}
