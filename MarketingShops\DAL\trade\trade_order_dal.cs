﻿using MarketingShops.BLL.Miniapp.trade;
using MarketingShops.BLL.trade;
using MarketingShops.Model;
using MarketingShops.Model.trade;
using MarketingShops.Model.trade.baseTable;
using MarketingShops.Model.trade.Search;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.trade
{
    /// <summary>
    /// 订单专用dal
    /// </summary>
    public class trade_order_dal
    {
        #region 查询（分页）
        /// <summary>
        /// 系统查询
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">页码信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(trade_search_params sys, Pagination Page)
        {
            //处理订单号排序与订单号排序问题
            if (Page.sidx == "orderid")
                Page.sidx = "a.orderid";
            else if (Page.sidx == "billStateName")
                Page.sidx = "billState";

            if (Page.sidx == "inTime")
                Page.sord = "desc";

            //获取查询条件
            string seltj = trade_search_dal.GetSearchStr(sys);

            string sql = @"SELECT {0} FROM dbo.buy_bill_core a join buy_bill_contacts b on a.orderid=b.orderid where " + seltj;
            return DbHelp_MarketingShops.paging<trade_search_data>(sql, @"a.[orderid]  
      ,[total_num]
      ,[total_fee]
      ,[cash_fee]
      ,[discount_fee]
      ,[Userid]
      ,[memberKey]
      ,[billState]
      ,[isRefund],[phone],[inTime],[total_integral]", Page);
        }

        #endregion
        /// <summary>
        /// 获取订单详情
        /// </summary>
        /// <param name="orderid">订单编号</param>
        /// <returns></returns>
        public object GetTradeInfo(string orderid)
        {
            TradeInfo bill = trade_mini_bll.GetMiniTrandInfo(orderid.Trim());
            string sql = "select [scene],[payTime],[deliverTime],[dealTime],[upTime] ,[transaction_cou] ,[inTime] from buy_bill_secondary where orderid=@orderid";
            buy_bill_secondary bill_secondary = DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_secondary>(sql, new { orderid = orderid.Trim() });
            sql = "select [transactionId],[appid],[bankType],[feeType],[cashFee],[totalFee],[timeEnd],[body],[tradeType],[time_in] from buy_bill_payment where orderid=@orderid";
            buy_bill_payment bill_pay = DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_payment>(sql, new { orderid = orderid.Trim() });
            MemberInfo member = null;
            //获取会员数据
            if (!string.IsNullOrEmpty(bill.bill_core.memberKey))
            {
                member = trade_member_bll.GetMemberInfo(bill.bill_core.memberKey.Trim());
            }
            return new
            {
                bill,
                bill_secondary,
                bill_pay,
                member
            };
        }
        /// <summary>
        /// 下单（添加订单记录）
        /// </summary>
        /// <param name="model">订单记录</param>
        /// <returns></returns>
        public int AddTradeInfo(TradeInfo model, buy_bill_payment pay)
        {
            StringBuilder sb = new StringBuilder("SET XACT_ABORT OFF  BEGIN TRAN ");
            if (model != null)
            {
                #region 开始插入数据
                sb.Append(@"if not exists(select * from buy_bill_core where orderid=@orderid)  begin ");
                //账单表
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_core]
           ([orderid]
           ,[body]
           ,[mail_no]
           ,[contacts_no]
           ,[total_num]
           ,[total_fee]
           ,[cash_fee]
           ,[total_goodsfee]
           ,[discount_fee]
           ,[freight_fee]
           ,[total_integral]
           ,[total_recharge]
           ,[total_weight]
           ,[openid]
           ,[Userid]
           ,[memberKey]
           ,[billState]
           ,[isRefund],[memberPhoneNumber])
     VALUES
        (@orderid ,@body ,@mail_no,@contacts_no ,@total_num,@total_fee,@cash_fee,@total_goodsfee ,@discount_fee ,@freight_fee,@total_integral,@total_recharge,@total_weight,@openid,@Userid,@memberKey,@billState ,@isRefund,@memberPhoneNumber)");
                //账单字表
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_secondary]
               ([orderid]
               ,[scene]
        ,[transaction_cou],[inTime],[transactionId])
         VALUES
        (@orderid
        ,@scene
        ,1 ,@inTime,@transactionId)");
                List<GoodListInfo> goodsList = model.goodsList;
                for (int i = 0; i < goodsList.Count; i++)
                {
                    sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_detailed]
               ([detailedno]
               ,[g_id]
               ,[g_name]
               ,[sku_id]
               ,[skuname]
               ,[photo]
               ,[orderid]
               ,[buy_num]
               ,[showprice]
               ,[cash]
               ,[integral]
               ,[recharge]
               ,[weight]
               ,[payMethodCou]
               ,[stateNo])
         VALUES
               ('" + goodsList[i].detailedno + "' ,'" + goodsList[i].g_id + "' ,'" + goodsList[i].g_name + "','" + goodsList[i].sku_id + "' ,'" + goodsList[i].skuname + "' ,'" + goodsList[i].photo + "','" + model.bill_core.orderid + "'," + goodsList[i].buy_num + " ,'" + goodsList[i].showprice + "' ," + goodsList[i].cash + " ," + goodsList[i].integral + " ," + goodsList[i].recharge + "," + goodsList[i].weight + " ," + goodsList[i].payMethodCou + "," + goodsList[i].stateNo + ")");
                    //支付小计表
                    sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_detailed_cost]
               ([detailedno]
               ,[total_cash]
               ,[total_integral]
               ,[total_recharge]
               ,[total_weight])
         VALUES
               ('" + goodsList[i].detailedno + "'," + goodsList[i].cost.total_cash + " ," + goodsList[i].cost.total_integral + " ," + goodsList[i].cost.total_recharge + " ," + goodsList[i].cost.total_weight + ")");
                    //2021-09-27 jjjy 添加 更新商品销售数量
                    sb.Append(@"update goods_dat_entity set salesnums=salesnums+1 where g_id='" + goodsList[i].g_id + "' ");
                    //支付方式
                    for (int j = 0; j < goodsList[i].PaymentMethod.Count; j++)
                    {
                        var txt = goodsList[i].PaymentMethod[j].txt;
                        if (goodsList[i].PaymentMethod[j].txt.Contains("'"))
                            txt = goodsList[i].PaymentMethod[j].txt.Replace("'", "\"");

                        sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_detailed_paymentMethod]
               ([detailedno]
               ,[buy_num]
               ,[GDSKUPID]
               ,[cash]
               ,[integral]
               ,[recharge]
               ,[txt])
         VALUES
            ('" + goodsList[i].detailedno + "'," + goodsList[i].PaymentMethod[j].buy_num + "," + goodsList[i].PaymentMethod[j].GDSKUPID + " ," + goodsList[i].PaymentMethod[j].cash + " ," + goodsList[i].PaymentMethod[j].integral + "," + goodsList[i].PaymentMethod[j].recharge + " ,'" + txt + "')");
                    }
                }
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_contacts]
           ([contacts_no]
           ,[orderid]
           ,[mail_no]
           ,[name]
           ,[phone]
           ,[region]
           ,[detailed])
     VALUES
        (@contacts_no,@orderid,'',@name ,@phone,@region,@detailed)");

                sb.Append(" end ");
                #endregion
            }
            if (pay != null && !string.IsNullOrEmpty(pay.transactionId))
            {
                sb.Append(@"if not exists(select * from [MarketingShops].[dbo].[buy_bill_payment] where transactionId='" + pay.transactionId + "' ) begin ");
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[buy_bill_payment]
                ([transactionId]
                ,[orderid]
                ,[appid]
                ,[attach]
                ,[bankType]
                ,[feeType]
                ,[outTradeNo]
                ,[cashFee]
                ,[totalFee]
                ,[timeEnd]
                ,[openid]
                ,[body]
            ,[tradeType])
            VALUES
            ('" + pay.transactionId + "',@orderid,'" + pay.appid + "' ,'" + pay.attach + "','" + pay.bankType + "','" + pay.feeType + "','" + pay.outTradeNo + "'," + pay.cashFee + "," + pay.totalFee + ",'" + pay.timeEnd + "' ,@openid,@body,'" + pay.tradeType + "')");
                sb.Append(@" update [MarketingShops].[dbo].[buy_bill_secondary] set [transactionId]='" + pay.transactionId + "',[payTime]=getdate(),[upTime]=getdate() where [orderid]=@orderid");
                sb.Append(" end");
            }
            sb.Append(@" COMMIT TRAN");
            if (model.bill_core.Userid == null) model.bill_core.Userid = string.Empty;
            if (model.bill_core.openid == null) model.bill_core.openid = string.Empty;
            if (model.bill_core.memberKey == null) model.bill_core.memberKey = string.Empty;
            if (model.bill_core.memberPhoneNumber == null) model.bill_core.memberPhoneNumber = string.Empty;
            if (model.bill_core.scene == null) model.bill_core.scene = string.Empty;
            if (model.bill_core.transactionId == null) model.bill_core.transactionId = string.Empty;


            var data = new
            {
                //订单信息
                orderid = model.bill_core.orderid.Trim(),
                body = model.bill_core.body.Trim(),
                mail_no = "",
                contacts_no = model.address.contacts_no.Trim(),
                total_num = model.bill_core.total_num,
                total_fee = model.bill_core.total_fee,
                cash_fee = model.bill_core.cash_fee,
                total_goodsfee = model.bill_core.total_goodsfee,
                discount_fee = model.bill_core.discount_fee,
                freight_fee = model.bill_core.freight_fee,
                total_integral = model.bill_core.total_integral,
                total_recharge = model.bill_core.total_recharge,
                total_weight = model.bill_core.total_weight,
                openid = model.bill_core.openid.Trim(),
                Userid = model.bill_core.Userid.Trim(),
                memberKey = model.bill_core.memberKey.Trim(),
                memberPhoneNumber = model.bill_core.memberPhoneNumber.Trim(),
                billState = model.bill_core.billState,
                isRefund = model.bill_core.isRefund,
                scene = model.bill_core.scene.Trim(),
                inTime = model.bill_core.inTime,
                transactionId = model.bill_core.transactionId.Trim(),
                //收货人信息
                name = model.address.name.Trim(),
                phone = model.address.phone.Trim(),
                region = model.address.region.Trim(),
                detailed = model.address.detailed.Trim()
            };
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString(), data);
        }

        /// <summary>
        /// 订单完成
        /// </summary>
        /// <param name="orderid">订单号</param>
        /// <returns></returns>
        public int FinishOrder(string orderid)
        {
            StringBuilder sb = new StringBuilder(@"update [MarketingShops].[dbo].[buy_bill_secondary] set [dealTime]=getdate(),[upTime]=getdate() where [orderid]=@orderid");
            //修改订单状态 
            sb.Append(" update [MarketingShops].[dbo].[buy_bill_core] set billState=3 where [orderid]=@orderid");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString(), new { orderid = orderid.Trim() });
        }

    }
}
