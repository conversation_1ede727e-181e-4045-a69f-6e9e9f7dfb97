﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.trade.tradedetails
{
    public class Exportbilldetailed
    {
        #region Model

        private string _g_name;
        private string _skuname;
        private string _orderid;
        private int _buy_num;
        private int _cash = 0;
        private int _integral = 0;
        private int _recharge = 0;
        private int _weight = 0;

        /// <summary>
        /// 订单号
        /// </summary>
        [Description("订单号")]
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }

        /// <summary>
        /// 商品名称
        /// </summary>
        [Description("商品名称")]
        public string g_name
        {
            set { _g_name = value; }
            get { return _g_name; }
        }

        /// <summary>
        /// 规格名称
        /// </summary>
        [Description("规格名称")]
        public string skuname
        {
            set { _skuname = value; }
            get { return _skuname; }
        }


        /// <summary>
        /// 购买数量
        /// </summary>
        [Description("购买数量")]
        public int buy_num
        {
            set { _buy_num = value; }
            get { return _buy_num; }
        }


        /// <summary>
        /// 现金金额(单价)
        /// </summary>
        [Description("现金金额(单价)")]
        public int cash
        {
            set { _cash = value; }
            get { return _cash; }
        }
        /// <summary>
        /// 积分（单价）
        /// </summary>
        [Description("积分（单价）")]
        public int integral
        {
            set { _integral = value; }
            get { return _integral; }
        }
        /// <summary>
        /// 充值（单价）
        /// </summary>
        [Description("充值（单价）")]
        public int recharge
        {
            set { _recharge = value; }
            get { return _recharge; }
        }

      

        /// <summary>
        /// 商品重量
        /// </summary>
        [Description("商品重量")]
        public int weight
        {
            set { _weight = value; }
            get { return _weight; }
        }


       

        #endregion Model
    }
}
