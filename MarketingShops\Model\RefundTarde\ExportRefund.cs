﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.RefundTarde
{
    /// <summary>
    /// 导出订单退款
    /// 2021.09.27
    /// </summary>
    public class ExportRefund
    {
        #region Model
        private string _orderid;
        private string _g_name;
        private string _total_integral;
        private int _total_num;
        private string _phone;
        private decimal _total_fee = 0M;
        private decimal _cash_fee = 0M;
        private decimal _total_goodsfee = 0M;
        private decimal _discount_fee = 0M;
        private string _memberKey;
        private DateTime _intime = DateTime.Now;
        /// <summary>
        /// 订单编号
        /// </summary>
        [Description("订单编号")]
        public string orderid
        {
            set { _orderid = value; }
            get { return _orderid; }
        }

        /// <summary>
        /// 商品名称
        /// </summary>
        [Description("商品")]
        public string g_name
        {
            set { _g_name = value; }
            get { return _g_name; }
        }

        /// <summary>
        /// 积分
        /// </summary>
        [Description("积分")]
        public string total_integral
        {
            set { _total_integral = value; }
            get { return _total_integral; }
        }

        /// <summary>
        /// 会员key
        /// </summary>
        [Description("会员key")]
        public string memberKey
        {
            set { _memberKey = value; }
            get { return _memberKey; }
        }

        /// <summary>
        /// 购买数量
        /// </summary>
        [Description("总数")]
        public int total_num
        {
            set { _total_num = value; }
            get { return _total_num; }
        }
        /// <summary>
        /// 实际支付金额
        /// </summary>
        [Description("实付金额")]
        public decimal cash_fee
        {
            set { _cash_fee = value; }
            get { return _cash_fee; }
        }

        /// <summary>
        /// 总支付金额
        /// </summary>
        [Description("应付金额")]
        public decimal total_fee
        {
            set { _total_fee = value; }
            get { return _total_fee; }
        }

        /// <summary>
        /// 商品金额
        /// </summary>
        [Description("商品金额")]
        public decimal total_goodsfee
        {
            set { _total_goodsfee = value; }
            get { return _total_goodsfee; }
        }

        /// <summary>
        /// 优惠金额/分
        /// </summary>
        [Description("优惠金额")]
        public decimal discount_fee
        {
            set { _discount_fee = value; }
            get { return _discount_fee; }
        }

        /// <summary>
        /// 顾客电话
        /// </summary>
        [Description("顾客电话")]
        public string phone
        {
            set { _phone = value; }
            get { return _phone; }
        }

        /// <summary>
        /// 录入时间
        /// </summary>
        [Description("录入时间")]
        public DateTime InTime
        {
            set { _intime = value; }
            get { return _intime; }
        }
        #endregion
    }
}
