﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.MemberPurchase.baseTable
{
   public class NHtmlTemplate
    {
       
        /// <summary>
        /// 
        /// </summary>
        public int HtmlTemplateNo { get; set; }
        /// <summary>
        /// 会员购
        /// </summary>
        public string HtmlTemplateName { get; set; }
        ///// <summary>
        ///// 采用原有会员购模式
        ///// </summary>
        //public string HtmlTemplateExplain { get; set; }
        ///// <summary>
        ///// 
        ///// </summary>
        //public string HtmlTemplateUrl { get; set; }
        ///// <summary>
        ///// 
        ///// </summary>
        //public string HtmlTemplateContentUrl { get; set; }
       

    }
}
