﻿using MarketingShops.Model;
using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Category
{
    /*
     项内容数据库操作
     * 增删改操作均记录在操作表log_marketing_edit
     */
    public class item_dal
    {
        /// <summary>
        /// 获取项内容信息
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">分页信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(string tcp_name, Pagination Page)
        {
            string variable = "1=1";
            if (!string.IsNullOrEmpty(tcp_name))
                variable += "and [tci_name]='" + tcp_name.Trim() + " '";

            string sql = "select {0} from dbo.template_category_item where " + variable;
            return DbHelp_MarketingShops.paging<item>(sql, "*", Page.sidx, Page.page, Page.rows);
        }
        /// <summary>
        /// 获取项内容详情
        /// </summary>
        /// <param name="id">项内容自增id</param>
        /// <returns></returns>
        public item GetItemInfo(int id)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle<item>("select * from template_category_item where tci_id=" + id + " ");
        }
        
        /// <summary>
        /// 修改项内容信息
        /// </summary>
        /// <param name="it">项内容信息</param>
        /// <returns></returns>
        public int UpdateItem(item it)
        {
            StringBuilder sb = new StringBuilder();
            var userinfo = OperatorProvider.Provider.GetCurrent();
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            sb.Append(@"update template_category_item set tci_name='" + it.tci_name + "',name='" + it.name + "',value='" + it.value + "',regex='" + it.regex + "',errordesc='" + it.errordesc + "',mixlen=" + it.mixlen + ",maxlen=" + it.maxlen + ",tci_type='" + it.tci_type + "' where tci_id=" + it.tci_id);
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('update','template_category_item', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + it.ToJson() + "'," + it.tci_id + ",'" + it.tci_name + "')");
            sb.Append(" Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
        /// <summary>
        /// 新增项内容
        /// </summary>
        /// <param name="it">项内容信息</param>
        /// <returns></returns>
        public int AddItem(item it)
        {
            StringBuilder sb = new StringBuilder();
            var userinfo = OperatorProvider.Provider.GetCurrent();
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            sb.Append(@"insert into template_category_item(tci_name,name,value,regex,placeholder,errordesc,mixlen,maxlen,dicCou,tci_type) VALUES('" + it.tci_name + "' ,'" + it.name + "','" + it.value + "','" + it.regex + "','"+it.placeholder+"','" + it.errordesc + "'," + it.mixlen + "," + it.maxlen + "," + it.dicCou + ",'" + it.tci_type + "')");
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('insert','template_category_item', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + it.ToJson() + "'," + it.tci_id + ",'" + it.tci_name + "')");
            sb.Append("Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
        /// <summary>
        /// 删除项内容
        /// </summary>
        /// <param name="id">项内容自增id</param>
        /// <returns></returns>
        public int DeleteItem(int id)
        {
            var userinfo = OperatorProvider.Provider.GetCurrent();
            StringBuilder sb = new StringBuilder();
            item data = GetItemInfo(id);
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            sb.Append("delete from template_category_item where tci_id=" + id);
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('delete','template_category_item', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "'," + data.tci_id + ",'" + data.tci_name + "')");
            sb.Append(" Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
    }
}
