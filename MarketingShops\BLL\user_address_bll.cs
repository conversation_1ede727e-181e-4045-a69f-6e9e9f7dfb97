﻿using MarketingShops.DAL;
using MarketingShops.Model;
using MarketingShops.Model.User;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL
{
    public class user_address_bll
    {
        user_address_dal _dal = new user_address_dal();
        /// <summary>
        /// 获取用户数据
        /// </summary>
        /// <param name="p_id"></param>
        /// <returns></returns>
        public List<user_address> GetAddressData(user_address data)
        {
            return _dal.GetAddressData(GetDataTj(data));
        }

        /// <summary>
        /// 查询条件
        /// </summary>
        /// <returns></returns>
        public string GetDataTj(user_address data)
        {
            StringBuilder sb = new StringBuilder("1=1");
            if (!string.IsNullOrEmpty(data.openid))
            {
                sb.Append(" and openid = '");
                sb.Append(data.openid);
                sb.Append("'");
            }
            if (!string.IsNullOrEmpty(data.name))
            {
                sb.Append(" and name like '%");
                sb.Append(data.name);
                sb.Append("%'");
            }
            if (!string.IsNullOrEmpty(data.phone))
            {
                sb.Append(" and phone like '%");
                sb.Append(data.phone);
                sb.Append("%'");
            }
            if (!string.IsNullOrEmpty(data.region))
            {
                sb.Append(" and region like '%");
                sb.Append(data.region);
                sb.Append("%'");
            }
            if (!string.IsNullOrEmpty(data.detailed))
            {
                sb.Append(" and detailed like '%");
                sb.Append(data.detailed);
                sb.Append("%'");
            }
            return sb.ToString();
        }

        /// <summary>
        /// 插入用户数据
        /// </summary>
        /// <returns></returns>
        public ResultModel AddAddress(user_address data)
        {
            ResultModel result = new ResultModel();
            try
            {
                result = CheckData(data);
                if (result.MsgState == 1) //检测通过
                {
                    result = _dal.AddAddress(data);
                }


            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }

            return result;
        }


        /// <summary>
        /// 修改用户数据
        /// </summary>
        /// <returns></returns>
        public ResultModel UpAddress(user_address data)
        {
            ResultModel result = new ResultModel();
            try
            {
                result = CheckData(data);
                if (result.MsgState == 1) //检测通过
                {
                    result = _dal.UpAddress(data);
                }

            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }

            return result;
        }

        /// <summary>
        /// 删除用户数据
        /// </summary>
        /// <returns></returns>
        public ResultModel DelAddress(int ud_id)
        {
            ResultModel result = new ResultModel();
            try
            {
                if (ud_id != 0) //检测通过
                {
                    result = _dal.DelAddress(ud_id);
                }
                else {
                    result.Msg = "传递id不可为0";
                }

            }
            catch (Exception ex)
            {
                result.Msg = ex.Message;
            }

            return result;
        }
        /// <summary>
        /// 检查数据
        /// </summary>
        /// <param name="data"></param>
        /// <returns></returns>
        public ResultModel CheckData(user_address data)
        {
            ResultModel result = new ResultModel();
            if (string.IsNullOrEmpty(data.openid))
            {
                result.Msg = "openid is NULL";
            }
            else
            {
                result.MsgState = 1;//检测成功
            }
            return result;

        }
    }
}
