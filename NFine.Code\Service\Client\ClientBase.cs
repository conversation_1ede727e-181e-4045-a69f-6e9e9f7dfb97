﻿using NFine.Code.Service.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Code.Service.Client
{
    public abstract class ClientBase
    {
        protected virtual List<StoreUrlConfig> GetStoreConfig()
        {
            string filePath = AppDomain.CurrentDomain.BaseDirectory + "Configs\\storeUrlConfig.json";
            var configStr = System.IO.File.ReadAllText(filePath, Encoding.UTF8);
            return configStr.ToList<StoreUrlConfig>();
        }
    }
}
