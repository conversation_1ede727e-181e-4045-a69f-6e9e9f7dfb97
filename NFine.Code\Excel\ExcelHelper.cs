﻿/*******************************************************************************
 * Copyright © 2016 NFine.Framework 版权所有
 * Author: NFine
 * Description: NFine快速开发平台
 * Website：http://www.nfine.cn
*********************************************************************************/
using NFine.Code.Excel.Model;
using NPOI.HSSF.UserModel;
using NPOI.SS.UserModel;
using NPOI.XSSF.UserModel;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.IO;
using System.Linq;

namespace NFine.Code
{
    public class ExcelHelper
    {
        /// <summary>
        /// 创建指定的excel文件
        /// </summary>
        /// <param name="headers">表头信息</param>
        /// <param name="fileName">文件名称</param>
        /// <param name="filePath">文件地址</param>
        /// <param name="fileFormat">文件格式（xls,xlsx）</param>
        /// <returns></returns>
        public static bool CreateFile(List<string> headers, string fileName, string filePath, string fileFormat)
        {
            bool isCreate = false;
            if (fileFormat != "xls" && fileFormat != "xlsx") throw new Exception("文件格式不正确，无法创建！");

            //拼接文件完整地址
            var file = filePath + "\\" + fileName + "." + fileFormat;
            if (File.Exists(file))
                return true;

            //创建文件
            File.Create(file).Dispose();
            //使用NPIO实例化文件
            IWorkbook book = null;
            if (fileFormat == "xls")
                book = new HSSFWorkbook();
            else
                book = new XSSFWorkbook();

            //创建工作簿
            var sheet = book.CreateSheet();
            //生成第一行
            var row = sheet.CreateRow(0);
            for (int i = 0; i < headers.Count; i++)
            {
                var cell = row.CreateCell(i);
                cell.SetCellValue(headers[i]);
            }

            using (FileStream fs = new FileStream(file, FileMode.Open, FileAccess.Write))
            {
                book.Write(fs);
                isCreate = true;
            }

            return isCreate;
        }

        /// <summary>
        /// 创建指定的excel文件
        /// </summary>
        /// <param name="headers">表头信息</param>
        /// <param name="fileName">文件名称</param>
        /// <param name="filePath">文件地址</param>
        /// <param name="fileFormat">文件格式（xls,xlsx）</param>
        /// <returns></returns>
        public static bool CreateFile<T>(List<string> headers, List<T> data, string fileName, string filePath, string fileFormat)
        {
            bool isCreate = false;
            if (fileFormat != "xls" && fileFormat != "xlsx") throw new Exception("文件格式不正确，无法创建！");

            var type = typeof(T);
            var property = type.GetProperties();

            //拼接文件完整地址
            var file = filePath + "\\" + fileName + "." + fileFormat;
            //使用NPIO实例化文件
            IWorkbook book = null;
            if (fileFormat == "xls")
                book = new HSSFWorkbook();
            else
                book = new XSSFWorkbook();

            //创建工作簿
            var sheet = book.CreateSheet();
            //生成第一行
            var row = sheet.CreateRow(0);
            for (int i = 0; i < headers.Count; i++)
            {
                var cell = row.CreateCell(i);
                cell.SetCellValue(headers[i]);
            }

            for (int i = 1; i < data.Count + 1; i++)
            {
                int j = 0;
                var dataRrow = sheet.CreateRow(i);
                foreach (var item in property)
                {
                    var cell = dataRrow.CreateCell(j);
                    j++;
                    var value = item.GetValue(data[i - 1]);
                    cell.SetCellValue(value.ToString());
                }
            }

            using (FileStream fs = new FileStream(file, FileMode.Create, FileAccess.Write))
            {
                book.Write(fs);
                isCreate = true;
            }

            return isCreate;
        }

        /// <summary>
        /// 将excel转正对应模型
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="filePath">文件完整地址</param>
        /// <param name="DelFirstRow">是否删除第一行</param>
        /// <returns></returns>
        public static List<T> ExcelToModel<T>(string filePath, bool delFirstRow)
        {
            var type = typeof(T);
            var data = new List<T>();

            if (!File.Exists(filePath))
                throw new Exception("文件不存在！");

            var fileFormat = filePath.Split('.')[1];
            IWorkbook book = null;
            //实例化excel文件
            using (FileStream fs = new FileStream(filePath, FileMode.Open, FileAccess.Read))
            {
                if (fileFormat == "xls")
                    book = new HSSFWorkbook(fs);
                else
                    book = new XSSFWorkbook(fs);
            }
            //获取第一个工作簿
            var sheet = book.GetSheetAt(0);
            int i = 0;
            //是否删除第一行
            if (delFirstRow)
            {
                var row = sheet.GetRow(0);
                sheet.RemoveRow(row);
                i = 1;
            }

            //获取所有属性
            var properties = type.GetProperties();
            for (; i <= sheet.LastRowNum; i++)
            {
                var row = sheet.GetRow(i);
                T outTModel = (T)Activator.CreateInstance(type);

                for (int j = 0; j < row.LastCellNum; j++)
                {
                    var cell = row.GetCell(j);
                    var cellValue = cell == null ? string.Empty : cell.ToString();
                    properties[j].SetValue(outTModel, cellValue, null);
                }

                data.Add(outTModel);
            }

            return data;
        }

        /// <summary>
        /// 财务资金导出专用方法
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <param name="headers"></param>
        /// <param name="storeName"></param>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public static string StoreExprot<T>(List<T> data, List<string> headers, string storeName, DateTime startTime)
        {
            string dirPath = AppDomain.CurrentDomain.BaseDirectory + "ExcelDir";
            if (!Directory.Exists(dirPath))
                Directory.CreateDirectory(dirPath);

            var filePath = dirPath + "\\" + startTime.ToString("yyyy-MM") + storeName + "收入明细.xlsx";

            var type = typeof(T);
            var property = type.GetProperties();

            IWorkbook book = new XSSFWorkbook();
            var sheet = book.CreateSheet("落单金额");
            //合并单元格并居中
            sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(0, 0, 0, 6));

            ICellStyle cellStyle = book.CreateCellStyle();
            cellStyle.Alignment = HorizontalAlignment.Center;
            IFont font = book.CreateFont();
            font.FontName = "Arial";
            font.FontHeightInPoints = 12;
            font.Boldweight = (short)FontBoldWeight.Bold;
            cellStyle.SetFont(font);

            var head = sheet.CreateRow(0);
            var headCell = head.CreateCell(0);
            headCell.SetCellValue(storeName + "收入明细表");
            headCell.CellStyle = cellStyle;

            var rowOne = sheet.CreateRow(1);
            var oneCell = rowOne.CreateCell(0);
            oneCell.CellStyle = cellStyle;
            oneCell.SetCellValue("编制单位");
            var twoCell = rowOne.CreateCell(1);
            twoCell.CellStyle = cellStyle;
            twoCell.SetCellValue(storeName);
            var threeCell = rowOne.CreateCell(2);
            threeCell.CellStyle = cellStyle;
            threeCell.SetCellValue("收入所属期");
            var fourCell = rowOne.CreateCell(3);
            fourCell.CellStyle = cellStyle;
            fourCell.SetCellValue(startTime.ToString("yyyy-MM"));
            var columnRow = sheet.CreateRow(2);
            for (int i = 0; i < headers.Count; i++)
            {
                var cell = columnRow.CreateCell(i);
                cell.SetCellValue(headers[i]);
                cell.CellStyle = cellStyle;
            }

            for (int i = 3; i < data.Count + 3; i++)
            {
                int j = 0;
                var row = sheet.CreateRow(i);
                foreach (var item in property)
                {
                    var cell = row.CreateCell(j);
                    j++;
                    var value = item.GetValue(data[i - 3]);
                    cell.SetCellValue(value.ToString());
                }
            }

            using (FileStream stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                book.Write(stream);
            }

            return filePath;
        }

        /// <summary>
        /// 财务资金导出专用方法
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="data"></param>
        /// <param name="headers"></param>
        /// <param name="storeName"></param>
        /// <param name="startTime"></param>
        /// <returns></returns>
        public static string StoreHeadExprot(List<StoreHeadCountModel> data, string storeName, DateTime startTime)
        {
            string dirPath = AppDomain.CurrentDomain.BaseDirectory + "ExcelDir";
            if (!Directory.Exists(dirPath))
                Directory.CreateDirectory(dirPath);
            string filePath = dirPath + "\\" + startTime.ToString("yyyy-MM") + storeName + "自助餐人数统计.xlsx";

            IWorkbook book = new XSSFWorkbook();
            var sheet = book.CreateSheet(storeName);
            //合并单元格并居中
            sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(0, 0, 0, 4));

            ICellStyle cellStyle = book.CreateCellStyle();
            cellStyle.Alignment = HorizontalAlignment.Center;
            IFont font = book.CreateFont();
            font.FontName = "Arial";
            font.FontHeightInPoints = 12;
            font.Boldweight = (short)FontBoldWeight.Bold;
            cellStyle.SetFont(font);

            #region 头部处理
            var head = sheet.CreateRow(0);
            var headCell = head.CreateCell(0);
            headCell.SetCellValue(storeName + startTime.ToString("yyyy-MM") + "自助餐消费人数汇总");
            headCell.CellStyle = cellStyle;

            var columnRow = sheet.CreateRow(1);
            var columnCell1 = columnRow.CreateCell(0);
            columnCell1.SetCellValue("序号");
            columnCell1.CellStyle = cellStyle;
            var columnCell2 = columnRow.CreateCell(1);
            columnCell2.SetCellValue("经营类别");
            columnCell2.CellStyle = cellStyle;
            var columnCell3 = columnRow.CreateCell(2);
            columnCell3.SetCellValue("类别1");
            columnCell3.CellStyle = cellStyle;
            var columnCell4 = columnRow.CreateCell(3);
            columnCell4.SetCellValue("类别2");
            columnCell4.CellStyle = cellStyle;
            var columnCell5 = columnRow.CreateCell(4);
            columnCell5.SetCellValue("来客人数");
            columnCell5.CellStyle = cellStyle;

            #endregion

            int index = 1;
            for (int i = 2; i < data.Count + 2; i++)
            {
                sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(index + 1, index + data[i - 2].Details.Count, 1, 1));
                for (int j = 0; j < data[i - 2].Details.Count; j++)
                {
                    var row = sheet.CreateRow(index + 1);
                    var cell1 = row.CreateCell(0);
                    var cell2 = row.CreateCell(1);
                    var cell3 = row.CreateCell(2);
                    var cell4 = row.CreateCell(3);
                    var cell5 = row.CreateCell(4);
                    cell1.SetCellValue(data[i - 2].Details[j].Index);
                    cell2.SetCellValue(data[i - 2].Business);
                    cell3.SetCellValue(data[i - 2].Details[j].Category1);
                    cell4.SetCellValue(data[i - 2].Details[j].Category2);
                    cell5.SetCellValue(data[i - 2].Details[j].PeoCount);
                    index++;
                }
            }

            using (FileStream stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                book.Write(stream);
            }

            return filePath;
        }

        /// <summary>
        /// 导出为excel公共方法
        /// </summary>
        /// <typeparam name="T">模型</typeparam>
        /// <param name="data">数据源</param>
        /// <param name="excelName">文件名</param>
        /// <param name="format">xls或者xlsx</param>
        /// <returns></returns>
        public static string ExprotToExcel<T>(List<T> data, string excelName, string format = "xls")
        {
            string dirPath = AppDomain.CurrentDomain.BaseDirectory + "\\ExcelDir";
            if (!Directory.Exists(dirPath))
                Directory.CreateDirectory(dirPath);
            string filePath = dirPath + "\\" + excelName + "." + format;

            var type = typeof(T);
            var property = type.GetProperties();

            IWorkbook book = null;
            if (format == "xls")
                book = new HSSFWorkbook();
            else
                book = new XSSFWorkbook();

            var sheet = book.CreateSheet();
            //合并单元格并居中
            sheet.AddMergedRegion(new NPOI.SS.Util.CellRangeAddress(0, 0, 0, property.Count()));
            ICellStyle cellStyle = book.CreateCellStyle();
            cellStyle.Alignment = HorizontalAlignment.Center;
            IFont font = book.CreateFont();
            font.FontName = "Arial";
            font.FontHeightInPoints = 12;
            font.Boldweight = (short)FontBoldWeight.Bold;
            cellStyle.SetFont(font);
            //设置表头
            var head = sheet.CreateRow(0);
            var headCell = head.CreateCell(0);
            headCell.SetCellValue(excelName);
            head.RowStyle = cellStyle;
            //设置列名
            var column = sheet.CreateRow(1);
            column.RowStyle = cellStyle;
            for (int i = 0; i < property.Count(); i++)
            {
                var cell = column.CreateCell(i);
                var descriptionAttribute = (DescriptionAttribute)Attribute.GetCustomAttribute(property[i], typeof(DescriptionAttribute));
                cell.SetCellValue(descriptionAttribute == null ? property[i].Name : descriptionAttribute.Description);
            }
            //设置内容
            for (int i = 2; i < data.Count + 2; i++)
            {
                var row = sheet.CreateRow(i);
                int j = 0;
                foreach (var item in property)
                {
                    var cell = row.CreateCell(j);
                    j++;
                    var value = item.GetValue(data[i - 2]);
                    cell.SetCellValue(value.ToString());
                }
            }
            //保存文件
            using (FileStream stream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                book.Write(stream);
            }

            return filePath;
        }
    }
}
