﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F2035EE2-B73D-4FB8-A433-CAB465DE6A2A}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>NFine.Application</RootNamespace>
    <AssemblyName>NFine.Application</AssemblyName>
    <TargetFrameworkVersion>v4.5</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <SccProjectName>SAK</SccProjectName>
    <SccLocalPath>SAK</SccLocalPath>
    <SccAuxPath>SAK</SccAuxPath>
    <SccProvider>SAK</SccProvider>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Newtonsoft.Json, Version=4.5.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\NFine.Code\Packages\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="01 Infrastructure\DbLogType.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SystemManage\FileDownLoadApp.cs" />
    <Compile Include="SystemManage\ItemsApp.cs" />
    <Compile Include="SystemManage\ItemsDetailApp.cs" />
    <Compile Include="SystemManage\ModuleApp.cs" />
    <Compile Include="SystemManage\ModuleButtonApp.cs" />
    <Compile Include="SystemManage\AreaApp.cs" />
    <Compile Include="SystemManage\OrganizeApp.cs" />
    <Compile Include="SystemManage\RoleApp.cs" />
    <Compile Include="SystemManage\SiteApp.cs" />
    <Compile Include="SystemManage\TestApp.cs" />
    <Compile Include="SystemManage\UserApp.cs" />
    <Compile Include="SystemManage\UserLogOnApp.cs" />
    <Compile Include="SystemManage\DutyApp.cs" />
    <Compile Include="SystemManage\RoleAuthorizeApp.cs" />
    <Compile Include="SystemSecurity\DbBackupApp.cs" />
    <Compile Include="SystemSecurity\FilterIPApp.cs" />
    <Compile Include="SystemSecurity\LogApp.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\MarketingShops\MarketingShops.csproj">
      <Project>{7BEFE33B-32C5-4B8F-AA5A-42B3F64ECDE9}</Project>
      <Name>MarketingShops</Name>
    </ProjectReference>
    <ProjectReference Include="..\NFine.Code\NFine.Code.csproj">
      <Project>{db19ce03-c307-43fe-a209-08aa4ae10e21}</Project>
      <Name>NFine.Code</Name>
    </ProjectReference>
    <ProjectReference Include="..\NFine.Data\NFine.Data.csproj">
      <Project>{f71003e8-a836-48f4-9df6-df9095cebd18}</Project>
      <Name>NFine.Data</Name>
    </ProjectReference>
    <ProjectReference Include="..\NFine.Domain\NFine.Domain.csproj">
      <Project>{7dc886cd-b8a0-44e8-aadd-57fcb0cfecb8}</Project>
      <Name>NFine.Domain</Name>
    </ProjectReference>
    <ProjectReference Include="..\NFine.Repository\NFine.Repository.csproj">
      <Project>{875233bf-ea15-49e0-9a9c-77884a897ff6}</Project>
      <Name>NFine.Repository</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>