using NFine.Code;
using NFine.Code.Service;
using NFine.Web.Areas.Financial.Models;
using SERVICE.PROXY.PosService;
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceModel;
using System.Web.Mvc;

namespace NFine.Web.Areas.Financial.Controllers
{
    public class FoodLabelController : ControllerBase
    {
        [HttpGet]
        [HandlerAjaxOnly]
        public ActionResult GetTreeGridJson(ComponentApplicationServiceInterface.Web.Pagination pagination, Models.FoodLabelContext context)
        {
            try
            {
                if (context.StoreId <= 0)
                    throw new Exception("请选择店铺！");

                SERVICE.PROXY.PosService.FoodLabelContext labelContext = new SERVICE.PROXY.PosService.FoodLabelContext()
                {
                    pagination = pagination,
                    SearchKey = context.FdCName,
                    FtNo = context.FtNo,
                    HasLabel = context.HasLabel
                };

                return Data(RPC.Pos(context.StoreId).PosService.GetFoodLabelList(labelContext));
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
        }

        public ActionResult GetFtDropDown(int storeId)
        {
            if (storeId <= 0)
                return Error("请选择门店！");

            var result = RPC.Pos(storeId).PosService.GetFtDropDown(new GetFtDropDownContext());
            if (result != null && result.state == ComponentApplicationServiceInterface.Context.Response.ResponseType.success)
                return Data(result.data);
            else
                return Error(result == null ? "返回为空！" : result.message);
        }

        [HttpGet]
        public ActionResult CreateFile(int storeId)
        {
            if (storeId <= 0)
                return Error("请选择门店！");

            var headers = new List<string>()
            {
                "食品名称",
                "食品编号",
                "房型",
                "类型",
                "人数",
                "平台",
                "部门",
                "酒水类型",
                "小食类型",
                "单位",
                "会员",
                "卡券类",
                "消费类型",
                "人头类",
                "时段",
                "银行",
                "券类型",
                "会员卡",
                "会员等级",
                "赔损",
                "价格类型",
                "半价房",
                "服务费",
                "抵扣金",
                "小时数",
                "积分",
                "套餐成本",
                "商品类别",
                "酒吧分成",
                "餐饮分成",
                "内场分成",
                "自助餐分成",
                "类别1",
                "类别2",
                "时段",
                "销售星期",
                "商品类型",
                "房型",
                "下单类型",
                "晚间下单人数",
                "晚间团购金额",
                "免房费开始时间",
                "免房费结束时间"
            };

            var contex = new WaitCreateContext();
            var result = RPC.Pos(storeId).PosService.GetWaitCreate(new WaitCreateContext());
            if (result == null || result.state != ComponentApplicationServiceInterface.Context.Response.ResponseType.success)
                return Error(result == null ? "返回为空！" : result.message);

            var data = result.data.Select(x => new WaitCreateLabelModel()
            {
                FdCName = x.FdCName,
                FdNo = x.FdNo,
            }).ToList();

            ExcelHelper.CreateFile(headers, data, "待创建标签食品数据", AppDomain.CurrentDomain.BaseDirectory, "xlsx");
            var filePath = AppDomain.CurrentDomain.BaseDirectory + "\\待创建标签食品数据.xlsx";
            var stream = System.IO.File.OpenRead(filePath);

            Response.ContentType = "application/octet-stream";
            Response.Headers.Add("Content-Disposition", "attachment; filename=批量处理模板.xlsx");

            return File(stream, "application/octet-stream");
        }

        [HttpPost]
        [HandlerAjaxOnly]
        public ActionResult Batching()
        {
            var request = HttpContext.Request.Files;
            if (request.Count <= 0)
                return Error("请选择上传文件！");

            var storeId = HttpContext.Request.Form["storeId"].ToString();
            if (string.IsNullOrEmpty(storeId))
                return Error("请选择待处理店铺！");

            var file = request[0];
            var fileName = file.FileName;
            var filePath = AppDomain.CurrentDomain.BaseDirectory + fileName;
            file.SaveAs(filePath);

            var excelData = new List<BatchExcelToFoodLabelContext>();
            try
            {
                excelData = ExcelHelper.ExcelToModel<BatchExcelToFoodLabelContext>(filePath, true);
            }
            catch (Exception ex)
            {
                return Error(ex.Message);
            }
            finally
            {
                System.IO.File.Delete(filePath);
            }

            Nullable<int> defaultValue = null;
            Nullable<bool> boolValue = null;
            var context = excelData.Select(x => new BatchImportContext()
            {
                FdNo = x.FdNo,
                FoodCode = string.Empty,
                RtNo = x.RoomType,
                Type = x.Type,
                PeoNumber = string.IsNullOrEmpty(x.PeoNumber) ? defaultValue : int.Parse(x.PeoNumber),
                Platform = x.Platform,
                Depart = x.Depart,
                BeverageType = string.IsNullOrEmpty(x.BeverageType) ? defaultValue : int.Parse(x.BeverageType),
                SnackType = string.IsNullOrEmpty(x.SnackType) ? defaultValue : int.Parse(x.SnackType),
                Unit = string.IsNullOrEmpty(x.Unit) ? defaultValue : int.Parse(x.Unit),
                MemberMode = string.IsNullOrEmpty(x.MemberMode) ? boolValue : bool.Parse(x.MemberMode),
                CardType = string.IsNullOrEmpty(x.CardType) ? defaultValue : int.Parse(x.CardType),
                CashType = x.CashType,
                HeadType = x.HeadType,
                Period = x.Period,
                Bank = x.Bank,
                VoucherType = string.IsNullOrEmpty(x.VoucherType) ? defaultValue : int.Parse(x.VoucherType),
                MemberCard = string.IsNullOrEmpty(x.MemberCard) ? defaultValue : int.Parse(x.MemberCard),
                MemberLevel = string.IsNullOrEmpty(x.MemberLevel) ? defaultValue : int.Parse(x.MemberLevel),
                Losses = string.IsNullOrEmpty(x.Losses) ? defaultValue : int.Parse(x.Losses),
                PriceType = string.IsNullOrEmpty(x.PriceType) ? defaultValue : int.Parse(x.PriceType),
                BJF = string.IsNullOrEmpty(x.BJF) ? defaultValue : int.Parse(x.BJF),
                ServiceCharge = string.IsNullOrEmpty(x.ServiceCharge) ? defaultValue : int.Parse(x.ServiceCharge),
                Deduction = string.IsNullOrEmpty(x.Deduction) ? defaultValue : int.Parse(x.Deduction),
                Quantity = defaultValue,
                Hours = string.IsNullOrEmpty(x.Hours) ? defaultValue : int.Parse(x.Hours),
                Integral = string.IsNullOrEmpty(x.Integral) ? defaultValue : int.Parse(x.Integral),
                PackageCost = string.IsNullOrEmpty(x.PackageCost) ? defaultValue : int.Parse(x.PackageCost),
                PackageType = defaultValue,
                GoodsType = string.IsNullOrEmpty(x.GoodsType) ? defaultValue : int.Parse(x.GoodsType),
                DivideBar = string.IsNullOrEmpty(x.DivideBar) ? 0 : decimal.Parse(x.DivideBar),
                DivideRestaurant = string.IsNullOrEmpty(x.DivideRestaurant) ? 0 : decimal.Parse(x.DivideRestaurant),
                DivideInfield = string.IsNullOrEmpty(x.DivideInfield) ? 0 : decimal.Parse(x.DivideInfield),
                DivideBuffet = string.IsNullOrEmpty(x.DivideBuffet) ? 0 : decimal.Parse(x.DivideBuffet),
                Category1 = x.Category1,
                Category2 = x.Category2,
                TimeNo = x.TimeNo,
                SaleDate = x.SaleDate,
                FtNo = string.IsNullOrEmpty(x.FtNo) ? defaultValue : int.Parse(x.FtNo),
            }).ToArray();

            return Data(RPC.Pos(int.Parse(storeId)).PosService.BatchImport(context));
        }

        [HttpPost]
        [HandlerAjaxOnly]
        public ActionResult GetLabelInfo(string foodCode, int storeId)
        {
            if (storeId <= 0)
                return Error("请选择门店！");

            var context = new GetFoodLabelInfoContext()
            {
                FoodCode = foodCode
            };

            return Data(RPC.Pos(storeId).PosService.GetLabelInfo(context));
        }

        [HttpPost]
        [HandlerAjaxOnly]
        public ActionResult Delete(string foodCode, int storeId)
        {
            if (storeId <= 0)
                return Error("请选择门店！");

            var context = new DeleteFoodLabelContext()
            {
                FoodCode = foodCode
            };
            return Data(RPC.Pos(storeId).PosService.Delete(context));
        }

        public ActionResult LableForm()
        {
            return View();
        }

        [HttpPost]
        [HandlerAjaxOnly]
        public ActionResult Update(BatchImportContext inputModel, int storeId)
        {
            if (storeId <= 0)
                return Error("请选择门店！");

            var context = new List<BatchImportContext>() { inputModel }.ToArray();
            return Data(RPC.Pos(storeId).PosService.BatchImport(context));
        }

        public ActionResult CreateForm()
        {
            return View();
        }

        [HttpGet]
        [HandlerAjaxOnly]
        public ActionResult GetCates(int storeId)
        {
            var result = RPC.Pos(storeId).PosService.GetCates(new GetFtDropDownContext());

            if (result != null && result.state == ComponentApplicationServiceInterface.Context.Response.ResponseType.success)
                return Data(result.data);
            else
                return Error(result == null ? "返回为空！" : result.message);
        }

        [HttpGet]
        [HandlerAjaxOnly]
        public ActionResult GetTypes(int storeId)
        {
            var result = RPC.Pos(storeId).PosService.GetTypes(new GetFtDropDownContext());

            if (result != null && result.state == ComponentApplicationServiceInterface.Context.Response.ResponseType.success)
                return Data(result.data);
            else
                return Error(result == null ? "返回为空！" : result.message);
        }

        public ActionResult CreateLabel(CreateLabelContext context, int StoreId)
        {
            return Data(RPC.Pos(StoreId).PosService.CreateLabel(context));
        }
    }
}
