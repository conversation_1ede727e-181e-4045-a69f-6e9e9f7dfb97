<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NPOI.OpenXml4Net</name>
    </assembly>
    <members>
        <member name="T:NPOI.OpenXml4Net.OPC.CertificateEmbeddingOption">
             Specifies the location where the X.509 certificate that is used in signing is stored.
            
             <AUTHOR>
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.CertificateEmbeddingOption.IN_CERTIFICATE_PART">
            The certificate is embedded in its own PackagePart. 
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.CertificateEmbeddingOption.IN_SIGNATURE_PART">
            The certificate is embedded in the SignaturePart that is created for the signature being added. 
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.CertificateEmbeddingOption.NOT_EMBEDDED">
            The certificate in not embedded in the package. 
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Configuration">
             Storage class for configuration storage parameters.
             TODO xml syntax checking is no longer done with DOM4j parser -> remove the schema or do it ?
            
             <AUTHOR> <PERSON><PERSON>
             @version 1.0
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.ContentTypes">
             Open Packaging Convention content types (see Annex F : Standard Namespaces
             and Content Types).
            
             <AUTHOR> define some constants, Julien Chable
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.CORE_PROPERTIES_PART">
            Core Properties part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.DIGITAL_SIGNATURE_CERTIFICATE_PART">
            Digital Signature Certificate part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.DIGITAL_SIGNATURE_ORIGIN_PART">
            Digital Signature Origin part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.DIGITAL_SIGNATURE_XML_SIGNATURE_PART">
            Digital Signature XML Signature part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.RELATIONSHIPS_PART">
            Relationships part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.CUSTOM_XML_PART">
            Custom XML part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.PLAIN_OLD_XML">
            Plain old xml. Note - OOXML uses application/xml, and not text/xml!
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.IMAGE_TIFF">
             TIFF image format.
            
             @see <a href="http://partners.adobe.com/public/developer/tiff/index.html#spec">
             http://partners.adobe.com/public/developer/tiff/index.html#spec</a>
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.IMAGE_PICT">
             Pict image format.
            
             @see <a href="http://developer.apple.com/documentation/mac/QuickDraw/QuickDraw-2.html">
             http://developer.apple.com/documentation/mac/QuickDraw/QuickDraw-2.html</a>
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ContentTypes.XML">
            XML file.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.EncryptionOption">
             Specifies the encryption option for parts in a Package.
            
             <AUTHOR> Chable
             @version 0.1
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.EncryptionOption.NONE">
            No encryption. 
        </member>
        <!-- 对于成员“T:NPOI.OpenXml4Net.OPC.Internal.ContentType”忽略有格式错误的 XML 注释 -->
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentType.type">
            Type in Type/Subtype.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentType.subType">
            Subtype
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentType.parameters">
            Parameters
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentType.patternMediaType">
            Media type compiled pattern for parameters.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentType.#ctor(System.String)">
             Constructor. Check the input with the RFC 2616 grammar.
            
             @param contentType
                        The content type to store.
             @throws InvalidFormatException
                         If the specified content type is not valid with RFC 2616.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentType.GetParameters(System.String)">
             Gets the value associated to the specified key.
            
             @param key
                        The key of the key/value pair.
             @return The value associated to the specified key.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.Internal.ContentType.SubType">
             Get the subtype.
            
             @return The subtype of this content type.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.Internal.ContentType.Type">
             Get the type.
            
             @return The type of this content type.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager">
            Manage package content types ([Content_Types].xml part).
            
            <AUTHOR> Chable
            @version 1.0
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.CONTENT_TYPES_PART_NAME">
            Content type part name.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.TYPES_NAMESPACE_URI">
            Content type namespace
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.container">
            Reference to the package using this content type manager.
        </member>
        <!-- 对于成员“F:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.defaultContentType”忽略有格式错误的 XML 注释 -->
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.overrideContentType">
            Override content type tree.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.#ctor(System.IO.Stream,NPOI.OpenXml4Net.OPC.OPCPackage)">
            Constructor. Parses the content of the specified input stream.
            
            @param in
                       If different of <i>null</i> then the content types part is
                       retrieve and parse.
            @throws InvalidFormatException
                        If the content types part content is not valid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.AddContentType(NPOI.OpenXml4Net.OPC.PackagePartName,System.String)">
            Build association extention-> content type (will be stored in
            [Content_Types].xml) for example ContentType="image/png" Extension="png"
            <p>
            [M2.8]: When adding a new part to a package, the package implementer
            shall ensure that a content type for that part is specified in the
            Content Types stream; the package implementer shall perform the steps
            described in &#167;9.1.2.3:
            </p><p>
            1. Get the extension from the part name by taking the substring to the
            right of the rightmost occurrence of the dot character (.) from the
            rightmost segment.
            </p><p>
            2. If a part name has no extension, a corresponding Override element
            shall be added to the Content Types stream.
            </p><p>
            3. Compare the resulting extension with the values specified for the
            Extension attributes of the Default elements in the Content Types stream.
            The comparison shall be case-insensitive ASCII.
            </p><p>
            4. If there is a Default element with a matching Extension attribute,
            then the content type of the new part shall be compared with the value of
            the ContentType attribute. The comparison might be case-sensitive and
            include every character regardless of the role it plays in the
            content-type grammar of RFC 2616, or it might follow the grammar of RFC
            2616.
            </p><p>
            a. If the content types match, no further action is required.
            </p><p>
            b. If the content types do not match, a new Override element shall be
            added to the Content Types stream. .
            </p><p>
            5. If there is no Default element with a matching Extension attribute, a
            new Default element or Override element shall be added to the Content
            Types stream.
            </p>
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.AddOverrideContentType(NPOI.OpenXml4Net.OPC.PackagePartName,System.String)">
            Add an override content type for a specific part.
            
            @param partName
                       Name of the part.
            @param contentType
                       Content type of the part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.AddDefaultContentType(System.String,System.String)">
            Add a content type associated with the specified extension.
            
            @param extension
                       The part name extension to bind to a content type.
            @param contentType
                       The content type associated with the specified extension.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.RemoveContentType(NPOI.OpenXml4Net.OPC.PackagePartName)">
            <p>
            Delete a content type based on the specified part name. If the specified
            part name is register with an override content type, then this content
            type is remove, else the content type is remove in the default content
            type list if it exists and if no part is associated with it yet.
            </p><p>
            Check rule M2.4: The package implementer shall require that the Content
            Types stream contain one of the following for every part in the package:
            One matching Default element One matching Override element Both a
            matching Default element and a matching Override element, in which case
            the Override element takes precedence.
            </p>
            @param partName
                       The part URI associated with the override content type to
                       delete.
            @exception InvalidOperationException
                           Throws if
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.IsContentTypeRegister(System.String)">
            Check if the specified content type is already register.
            
            @param contentType
                       The content type to check.
            @return <code>true</code> if the specified content type is already
                    register, then <code>false</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.GetContentType(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Get the content type for the specified part, if any.
            <p>
            Rule [M2.9]: To get the content type of a part, the package implementer
            shall perform the steps described in &#167;9.1.2.4:
            </p><p>
            1. Compare the part name with the values specified for the PartName
            attribute of the Override elements. The comparison shall be
            case-insensitive ASCII.
            </p><p>
            2. If there is an Override element with a matching PartName attribute,
            return the value of its ContentType attribute. No further action is
            required.
            </p><p>
            3. If there is no Override element with a matching PartName attribute,
            then a. Get the extension from the part name by taking the substring to
            the right of the rightmost occurrence of the dot character (.) from the
            rightmost segment. b. Check the Default elements of the Content Types
            stream, comparing the extension with the value of the Extension
            attribute. The comparison shall be case-insensitive ASCII.
            </p><p>
            4. If there is a Default element with a matching Extension attribute,
            return the value of its ContentType attribute. No further action is
            required.
            </p><p>
            5. If neither Override nor Default elements with matching attributes are
            found for the specified part name, the implementation shall not map this
            part name to a part.
            </p>
            @param partName
                       The URI part to check.
            @return The content type associated with the URI (in case of an override
                    content type) or the extension (in case of default content type),
                    else <code>null</code>.
            
            @exception OpenXml4NetRuntimeException
                           Throws if the content type manager is not able to find the
                           content from an existing part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.ClearAll">
            Clear all content types.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.ClearOverrideContentTypes">
            Clear all override content types.
            
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.ParseContentTypesFile(System.IO.Stream)">
            Parse the content types part.
            
            @throws InvalidFormatException
                        Throws if the content type doesn't exist or the XML format is
                        invalid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.Save(System.IO.Stream)">
            Save the contents type part.
            
            @param outStream
                       The output stream use to save the XML content of the content
                       types part.
            @return <b>true</b> if the operation success, else <b>false</b>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.AppendSpecificTypes(System.Xml.XmlDocument,System.Xml.XmlElement,System.Collections.Generic.KeyValuePair{NPOI.OpenXml4Net.OPC.PackagePartName,System.String})">
            Use to Append specific type XML elements, use by the save() method.
            
            @param root
                       XML parent element use to Append this override type element.
            @param entry
                       The values to Append.
            @see #save(java.io.OutputStream)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.AppendDefaultType(System.Xml.XmlDocument,System.Xml.XmlElement,System.Collections.Generic.KeyValuePair{System.String,System.String})">
            Use to Append default types XML elements, use by the save() metid.
            
            @param root
                       XML parent element use to Append this default type element.
            @param entry
                       The values to Append.
            @see #save(java.io.OutputStream)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ContentTypeManager.SaveImpl(System.Xml.XmlDocument,System.IO.Stream)">
            Specific implementation of the save method. Call by the save() method,
            call before exiting.
            
            @param out
                       The output stream use to write the content type XML.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.FileHelper">
             Provide useful method to manage file.
            
             <AUTHOR> Chable
             @version 0.1
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.FileHelper.GetDirectory(System.String)">
             Get the directory part of the specified file path.
            
             @param f
                        File to process.
             @return The directory path from the specified
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.FileHelper.CopyFile(System.String,System.String)">
             Copy a file.
            
             @param in
                        The source file.
             @param out
                        The target location.
             @throws IOException
                         If an I/O error occur.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.FileHelper.GetFilename(System.String)">
            Get file name from the specified File object.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.Marshallers.DefaultMarshaller">
             Default marshaller that specified that the part is responsible to marshall its content.
            
             <AUTHOR> Chable
             @version 1.0
             @see PartMarshaller
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.PartMarshaller">
             Object implemented this interface are considered as part marshaller. A part
             marshaller is responsible to marshall a part in order to be save in a
             package.
            
             <AUTHOR> Chable
             @version 0.1
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PartMarshaller.Marshall(NPOI.OpenXml4Net.OPC.PackagePart,System.IO.Stream)">
             Save the content of the package in the stream
            
             @param part
                        Part to marshall.
             @param out
                        The output stream into which the part will be marshall.
             @return false if any marshall error occurs, else <b>true</b>
             @throws OpenXml4NetException
                         Throws only if any other exceptions are thrown by inner
                         methods.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.DefaultMarshaller.Marshall(NPOI.OpenXml4Net.OPC.PackagePart,System.IO.Stream)">
             Save part in the output stream by using the save() method of the part.
            
             @throws OpenXml4NetException
                         If any error occur.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller">
             Package properties marshaller.
            
             <AUTHOR> Julien Chable
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.Marshall(NPOI.OpenXml4Net.OPC.PackagePart,System.IO.Stream)">
            Marshall package core properties to an XML document. Always return
            <code>true</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddCategory">
            Add category property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddContentStatus">
            Add content status property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddContentType">
            Add content type property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddCreated">
            Add created property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddCreator">
            Add creator property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddDescription">
            Add description property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddIdentifier">
            Add identifier property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddKeywords">
            Add keywords property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddLanguage">
            Add language property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddLastModifiedBy">
            Add 'last modified by' property if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddLastPrinted">
             Add 'last printed' property if needed.
            
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddModified">
            Add modified property element if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddRevision">
            Add revision property if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddSubject">
            Add subject property if needed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.PackagePropertiesMarshaller.AddTitle">
            Add title property if needed.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.Marshallers.ZipPackagePropertiesMarshaller">
             Package core properties marshaller specialized for zipped package.
            
             <AUTHOR> Chable
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.Marshallers.ZipPartMarshaller">
             Zip part marshaller. This marshaller is use to save any part in a zip stream.
            
             <AUTHOR> Chable
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.ZipPartMarshaller.Marshall(NPOI.OpenXml4Net.OPC.PackagePart,System.IO.Stream)">
             Save the specified part.
            
             @throws OpenXml4NetException
                         Throws if an internal exception is thrown.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Marshallers.ZipPartMarshaller.MarshallRelationshipPart(NPOI.OpenXml4Net.OPC.PackageRelationshipCollection,NPOI.OpenXml4Net.OPC.PackagePartName,ICSharpCode.SharpZipLib.Zip.ZipOutputStream)">
             Save relationships into the part.
            
             @param rels
                        The relationships collection to marshall.
             @param relPartName
                        Part name of the relationship part to marshall.
             @param zos
                        Zip output stream in which to save the XML content of the
                        relationships serialization.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackagePart">
            Provides a base class for parts stored in a Package.
            
            <AUTHOR> Chable
            @version 0.9
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.AddRelationship(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.TargetMode,System.String)">
            Add a relationship to a part (except relationships part).
            
            @param targetPartName
                       Name of the target part. This one must be relative to the
                       source root directory of the part.
            @param targetMode
                       Mode [Internal|External].
            @param relationshipType
                       Type of relationship.
            @return The newly created and added relationship
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.AddRelationship(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.TargetMode,System.String,System.String)">
                     * Add a relationship to a part (except relationships part).
                     
                     * Check rule M1.25: The Relationships part shall not have relationships to
                     * any other part. Package implementers shall enforce this requirement upon
                     * the attempt to create such a relationship and shall treat any such
                     * relationship as invalid.
                     * 
                     * @param targetPartName
                     *            Name of the target part. This one must be relative to the
                     *            source root directory of the part.
                     * @param targetMode
                     *            Mode [Internal|External].
                     * @param relationshipType
                     *            Type of relationship.
                     * @param id
                     *            Relationship unique id.
                     * @return The newly created and added relationship
                     * 
                     * @throws InvalidFormatException
                     *             If the URI point to a relationship part URI.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.AddExternalRelationship(System.String,System.String)">
            Adds an external relationship to a part
             (except relationships part).
            
            The targets of external relationships are not
             subject to the same validity checks that internal
             ones are, as the contents is potentially
             any file, URL or similar.
             
            @param target External target of the relationship
            @param relationshipType Type of relationship.
            @return The newly created and added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#addExternalRelationship(java.lang.String, java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.AddExternalRelationship(System.String,System.String,System.String)">
            Adds an external relationship to a part
             (except relationships part).
            
            The targets of external relationships are not
             subject to the same validity checks that internal
             ones are, as the contents is potentially
             any file, URL or similar.
             
            @param target External target of the relationship
            @param relationshipType Type of relationship.
            @param id Relationship unique id.
            @return The newly created and added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#addExternalRelationship(java.lang.String, java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.ClearRelationships">
            Delete all the relationships attached to this.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.RemoveRelationship(System.String)">
            Delete the relationship specified by its id.
            
            @param id
                       The ID identified the part to delete.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.GetRelationship(System.String)">
            Retrieves a package relationship from its id.
            
            @param id
                       ID of the package relationship to retrieve.
            @return The package relationship
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.GetRelationshipsByType(System.String)">
            Retrieve all relationships attached to this part which have the specified
            type.
            
            @param relationshipType
                       Relationship type filter.
            @return All relationships from this part that have the specified type.
            @throws InvalidFormatException
                        If an error occurs while parsing the part.
            @throws InvalidOperationException
                        If the package is open in write only mode.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.RelationshipSource.IsRelationshipExists(NPOI.OpenXml4Net.OPC.PackageRelationship)">
            Checks if the specified relationship is part of this package part.
            
            @param rel
                       The relationship to check.
            @return <b>true</b> if the specified relationship exists in this part,
                    else returns <b>false</b>
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.RelationshipSource.Relationships">
            Retrieve all the relationships attached to this.
            
            @return This part's relationships.
            @throws OpenXml4NetException
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.RelationshipSource.HasRelationships">
            Knows if the part have any relationships.
            
            @return <b>true</b> if the part have at least one relationship else
                    <b>false</b>.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePart.container">
            This part's container.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePart.partName">
            The part name. (required by the specification [M1.1])
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePart.contentType">
            The type of content of this part. (required by the specification [M1.2])
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePart.isRelationshipPart">
            Flag to know if this part is a relationship.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePart.isDeleted">
            Flag to know if this part has been logically deleted.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePart.relationships">
            This part's relationships.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.Internal.ContentType)">
            Constructor.
            
            @param pack
                       Parent package.
            @param partName
                       The part name, relative to the parent Package root.
            @param contentType
                       The content type.
            @throws InvalidFormatException
                        If the specified URI is not valid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.Internal.ContentType,System.Boolean)">
            Constructor.
            
            @param pack
                       Parent package.
            @param partName
                       The part name, relative to the parent Package root.
            @param contentType
                       The content type.
            @param loadRelationships
                       Specify if the relationships will be loaded
            @throws InvalidFormatException
                        If the specified URI is not valid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName,System.String)">
            Constructor.
            
            @param pack
                       Parent package.
            @param partName
                       The part name, relative to the parent Package root.
            @param contentType
                       The Multipurpose Internet Mail Extensions (MIME) content type
                       of the part's data stream.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.AddExternalRelationship(System.String,System.String)">
            Adds an external relationship to a part (except relationships part).
            
            The targets of external relationships are not subject to the same
            validity checks that internal ones are, as the contents is potentially
            any file, URL or similar.
            
            @param target
                       External target of the relationship
            @param relationshipType
                       Type of relationship.
            @return The newly created and added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#addExternalRelationship(java.lang.String,
                 java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.AddExternalRelationship(System.String,System.String,System.String)">
            Adds an external relationship to a part (except relationships part).
            
            The targets of external relationships are not subject to the same
            validity checks that internal ones are, as the contents is potentially
            any file, URL or similar.
            
            @param target
                       External target of the relationship
            @param relationshipType
                       Type of relationship.
            @param id
                       Relationship unique id.
            @return The newly created and added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#addExternalRelationship(java.lang.String,
                 java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.AddRelationship(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.TargetMode,System.String)">
            Add a relationship to a part (except relationships part).
            
            @param targetPartName
                       Name of the target part. This one must be relative to the
                       source root directory of the part.
            @param targetMode
                       Mode [Internal|External].
            @param relationshipType
                       Type of relationship.
            @return The newly created and added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#AddRelationship(org.apache.poi.OpenXml4Net.opc.PackagePartName,
                 org.apache.poi.OpenXml4Net.opc.TargetMode, java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.AddRelationship(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.TargetMode,System.String,System.String)">
            Add a relationship to a part (except relationships part).
            <p>
            Check rule M1.25: The Relationships part shall not have relationships to
            any other part. Package implementers shall enforce this requirement upon
            the attempt to create such a relationship and shall treat any such
            relationship as invalid.
            </p>
            @param targetPartName
                       Name of the target part. This one must be relative to the
                       source root directory of the part.
            @param targetMode
                       Mode [Internal|External].
            @param relationshipType
                       Type of relationship.
            @param id
                       Relationship unique id.
            @return The newly created and added relationship
            
            @throws InvalidFormatException
                        If the URI point to a relationship part URI.
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#AddRelationship(org.apache.poi.OpenXml4Net.opc.PackagePartName,
                 org.apache.poi.OpenXml4Net.opc.TargetMode, java.lang.String, java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.AddRelationship(System.Uri,NPOI.OpenXml4Net.OPC.TargetMode,System.String)">
            Add a relationship to a part (except relationships part).
            
            @param targetURI
                       URI the target part. Must be relative to the source root
                       directory of the part.
            @param targetMode
                       Mode [Internal|External].
            @param relationshipType
                       Type of relationship.
            @return The newly created and added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#AddRelationship(org.apache.poi.OpenXml4Net.opc.PackagePartName,
                 org.apache.poi.OpenXml4Net.opc.TargetMode, java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.AddRelationship(System.Uri,NPOI.OpenXml4Net.OPC.TargetMode,System.String,System.String)">
            Add a relationship to a part (except relationships part).
            <p>
            Check rule M1.25: The Relationships part shall not have relationships to
            any other part. Package implementers shall enforce this requirement upon
            the attempt to create such a relationship and shall treat any such
            relationship as invalid.
            </p>
            @param targetURI
                       URI of the target part. Must be relative to the source root
                       directory of the part.
            @param targetMode
                       Mode [Internal|External].
            @param relationshipType
                       Type of relationship.
            @param id
                       Relationship unique id.
            @return The newly created and added relationship
            
            @throws InvalidFormatException
                        If the URI point to a relationship part URI.
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#AddRelationship(org.apache.poi.OpenXml4Net.opc.PackagePartName,
                 org.apache.poi.OpenXml4Net.opc.TargetMode, java.lang.String, java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.ClearRelationships">
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#clearRelationships()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.RemoveRelationship(System.String)">
            Delete the relationship specified by its id.
            
            @param id
                       The ID identified the part to delete.
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#removeRelationship(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetRelationship(System.String)">
            Retrieves a package relationship from its id.
            
            @param id
                       ID of the package relationship to retrieve.
            @return The package relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#getRelationship(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetRelationshipsByType(System.String)">
            Retrieve all relationships attached to this part which have the specified
            type.
            
            @param relationshipType
                       Relationship type filter.
            @return All relationships from this part that have the specified type.
            @throws InvalidFormatException
                        If an error occurs while parsing the part.
            @throws InvalidOperationException
                        If the package is open in write only mode.
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#getRelationshipsByType(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetRelationshipsCore(System.String)">
            Implementation of the getRelationships method().
            
            @param filter
                       Relationship type filter. If <i>null</i> then the filter is
                       disabled and return all the relationships.
            @return All relationships from this part that have the specified type.
            @throws InvalidFormatException
                        Throws if an error occurs during parsing the relationships
                        part.
            @throws InvalidOperationException
                        Throws if the package is open en write only mode.
            @see #getRelationshipsByType(String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.IsRelationshipExists(NPOI.OpenXml4Net.OPC.PackageRelationship)">
            Checks if the specified relationship is part of this package part.
            
            @param rel
                       The relationship to check.
            @return <b>true</b> if the specified relationship exists in this part,
                    else returns <b>false</b>
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#isRelationshipExists(org.apache.poi.OpenXml4Net.opc.PackageRelationship)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetRelatedPart(NPOI.OpenXml4Net.OPC.PackageRelationship)">
             Get the PackagePart that is the target of a relationship.
            
             @param rel A relationship from this part to another one 
             @return The target part of the relationship
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetInputStream">
            Get the input stream of this part to read its content.
            
            @return The input stream of the content of this part, else
                    <code>null</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetOutputStream">
             Get the output stream of this part. If the part is originally embedded in
             Zip package, it'll be transform intot a <i>MemoryPackagePart</i> in
             order to write inside (the standard Java API doesn't allow to write in
             the file)
            
             @see org.apache.poi.openxml4j.opc.internal.MemoryPackagePart
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.ThrowExceptionIfRelationship">
            Throws an exception if this package part is a relationship part.
            
            @throws InvalidOperationException
                        If this part is a relationship part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.LoadRelationships">
            Ensure the package relationships collection instance is built.
            
            @throws InvalidFormatException
                        Throws if
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetInputStreamImpl">
            Abtract method that get the input stream of this part.
            
            @exception IOException
                           Throws if an IO Exception occur in the implementation
                           method.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.GetOutputStreamImpl">
            Abstract method that get the output stream of this part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.Save(System.IO.Stream)">
            Save the content of this part and the associated relationships part (if
            this part own at least one relationship) into the specified output
            stream.
            
            @param zos
                       Output stream to save this part.
            @throws OpenXml4NetException
                        If any exception occur.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.Load(System.IO.Stream)">
            Load the content of this part.
            
            @param ios
                       The input stream of the content to load.
            @return <b>true</b> if the content has been successfully loaded, else
                    <b>false</b>.
            @throws InvalidFormatException
                        Throws if the content format is invalid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.Close">
            Close this part : flush this part, close the input stream and output
            stream. After this method call, the part must be available for packaging.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePart.Flush">
            Flush the content of this part. If the input stream and/or output stream
            as in a waiting state to read or write, the must to empty their
            respective buffer.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePart.Relationships">
            Retrieve all the relationships attached to this part.
            
            @return This part's relationships.
            @throws OpenXml4NetException
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#getRelationships()
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePart.HasRelationships">
            Knows if the part have any relationships.
            
            @return <b>true</b> if the part have at least one relationship else
                    <b>false</b>.
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#hasRelationships()
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePart.PartName">
            @return the uri
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePart.ContentType">
            @return the contentType
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePart.IsRelationshipPart">
            @return true if this part is a relationship
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePart.IsDeleted">
            @return true if this part has been logically deleted
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.MemoryPackagePart.data">
            Storage for the part data.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.MemoryPackagePart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName,System.String)">
            Constructor.
            
            @param pack
                       The owner package.
            @param partName
                       The part name.
            @param contentType
                       The content type.
            @throws InvalidFormatException
                        If the specified URI is not OPC compliant.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.MemoryPackagePart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName,System.String,System.Boolean)">
            Constructor.
            
            @param pack
                       The owner package.
            @param partName
                       The part name.
            @param contentType
                       The content type.
            @param loadRelationships
                       Specify if the relationships will be loaded.
            @throws InvalidFormatException
                        If the specified URI is not OPC compliant.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.MemoryPackagePartOutputStream.Close">
            Close this stream and flush the content.
            @see #flush()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.MemoryPackagePartOutputStream.Flush">
            Flush this output stream. This method is called by the close() method.
            Warning : don't call this method for output consistency.
            @see #close()
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart">
            Represents the core properties part of a package.
            
            <AUTHOR> Chable
            @version 1.0
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackageProperties">
            Represents the core properties of an OPC package.
            
            <AUTHOR> Chable
            @version 1.0
            @see org.apache.poi.OpenXml4Net.opc.OPCPackage
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetCategoryProperty">
            Set the category of the content of this package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetCategoryProperty(System.String)">
            Set the category of the content of this package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetContentStatusProperty">
            Set the status of the content.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetContentStatusProperty(System.String)">
            Get the status of the content.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetContentTypeProperty">
            Get the type of content represented, generally defined by a specific use
            and intended audience.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetContentTypeProperty(System.String)">
            Set the type of content represented, generally defined by a specific use
            and intended audience.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetCreatedProperty">
            Get the date of creation of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetCreatedProperty(System.String)">
            Set the date of creation of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetCreatedProperty(System.Nullable{System.DateTime})">
            Set the date of creation of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetCreatorProperty">
            Get the entity primarily responsible for making the content of the
            resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetCreatorProperty(System.String)">
            Set the entity primarily responsible for making the content of the
            resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetDescriptionProperty">
            Get the explanation of the content of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetDescriptionProperty(System.String)">
            Set the explanation of the content of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetIdentifierProperty">
            Get an unambiguous reference to the resource within a given context.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetIdentifierProperty(System.String)">
            Set an unambiguous reference to the resource within a given context.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetKeywordsProperty">
            Get a delimited Set of keywords to support searching and indexing. This
            is typically a list of terms that are not available elsewhere in the
            properties
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetKeywordsProperty(System.String)">
            Set a delimited Set of keywords to support searching and indexing. This
            is typically a list of terms that are not available elsewhere in the
            properties
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetLanguageProperty">
            Get the language of the intellectual content of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetLanguageProperty(System.String)">
            Set the language of the intellectual content of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetLastModifiedByProperty">
            Get the user who performed the last modification.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetLastModifiedByProperty(System.String)">
            Set the user who performed the last modification.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetLastPrintedProperty">
            Get the date and time of the last printing.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetLastPrintedProperty(System.String)">
            Set the date and time of the last printing.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetLastPrintedProperty(System.Nullable{System.DateTime})">
            Set the date and time of the last printing.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetModifiedProperty">
            Get the date on which the resource was changed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetModifiedProperty(System.String)">
            Set the date on which the resource was changed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetModifiedProperty(System.Nullable{System.DateTime})">
            Set the date on which the resource was changed.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetRevisionProperty">
            Get the revision number.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetRevisionProperty(System.String)">
            Set the revision number.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetSubjectProperty">
            Get the topic of the content of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetSubjectProperty(System.String)">
            Set the topic of the content of the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetTitleProperty">
            Get the name given to the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetTitleProperty(System.String)">
            Set the name given to the resource.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.GetVersionProperty">
            Get the version number.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageProperties.SetVersionProperty(System.String)">
            Set the version number.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName)">
            Constructor.
            
            @param pack
                       Container package.
            @param partName
                       Name of this part.
            @throws InvalidFormatException
                        Throws if the content is invalid.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.category">
            A categorization of the content of this package.
            
            [Example: Example values for this property might include: Resume, Letter,
            Financial Forecast, Proposal, Technical Presentation, and so on. This
            value might be used by an application's user interface to facilitate
            navigation of a large Set of documents. end example]
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.contentStatus">
            The status of the content.
            
            [Example: Values might include "Draft", "Reviewed", and "Final". end
            example]
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.contentType">
            The type of content represented, generally defined by a specific use and
            intended audience.
            
            [Example: Values might include "Whitepaper", "Security Bulletin", and
            "Exam". end example] [Note: This property is distinct from MIME content
            types as defined in RFC 2616. end note]
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.created">
            Date of creation of the resource.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.creator">
            An entity primarily responsible for making the content of the resource.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.description">
            An explanation of the content of the resource.
            
            [Example: Values might include an abstract, table of contents, reference
            to a graphical representation of content, and a free-text account of the
            content. end example]
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.identifier">
            An unambiguous reference to the resource within a given context.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.keywords">
            A delimited Set of keywords to support searching and indexing. This is
            typically a list of terms that are not available elsewhere in the
            properties.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.language">
            The language of the intellectual content of the resource.
            
            [Note: IETF RFC 3066 provides guidance on encoding to represent
            languages. end note]
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.lastModifiedBy">
            The user who performed the last modification. The identification is
            environment-specific.
            
            [Example: A name, email address, or employee ID. end example] It is
            recommended that this value be as concise as possible.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.lastPrinted">
            The date and time of the last printing.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.modified">
            Date on which the resource was changed.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.revision">
            The revision number.
            
            [Example: This value might indicate the number of saves or revisions,
            provided the application updates it after each revision. end example]
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.subject">
            The topic of the content of the resource.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.title">
            The name given to the resource.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.version">
            The version number. This value is Set by the user or by the application.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetCategoryProperty">
            Get the category property.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getCategoryProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetContentStatusProperty">
            Get content status.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getContentStatusProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetContentTypeProperty">
            Get content type.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getContentTypeProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetCreatedProperty">
            Get created date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getCreatedProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetCreatedPropertyString">
            Get created date formated into a String.
            
            @return A string representation of the created date.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetCreatorProperty">
            Get creator.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getCreatorProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetDescriptionProperty">
            Get description.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getDescriptionProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetIdentifierProperty">
            Get identifier.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getIdentifierProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetKeywordsProperty">
            Get keywords.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getKeywordsProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetLanguageProperty">
            Get the language.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getLanguageProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetLastModifiedByProperty">
            Get the author of last modifications.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getLastModifiedByProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetLastPrintedProperty">
            Get last printed date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getLastPrintedProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetLastPrintedPropertyString">
            Get last printed date formated into a String.
            
            @return A string representation of the last printed date.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetModifiedProperty">
            Get modified date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getModifiedProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetModifiedPropertyString">
            Get modified date formated into a String.
            
            @return A string representation of the modified date.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetRevisionProperty">
            Get revision.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getRevisionProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetSubjectProperty">
            Get subject.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getSubjectProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetTitleProperty">
            Get title.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getTitleProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetVersionProperty">
            Get version.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#getVersionProperty()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetCategoryProperty(System.String)">
            Set the category.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setCategoryProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetContentStatusProperty(System.String)">
            Set the content status.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setContentStatusProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetContentTypeProperty(System.String)">
            Set the content type.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setContentTypeProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetCreatedProperty(System.String)">
            Set the created date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setCreatedProperty(org.apache.poi.OpenXml4Net.util.Nullable)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetCreatedProperty(System.Nullable{System.DateTime})">
            Set the created date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setCreatedProperty(org.apache.poi.OpenXml4Net.util.Nullable)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetCreatorProperty(System.String)">
            Set the creator.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setCreatorProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetDescriptionProperty(System.String)">
            Set the description.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setDescriptionProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetIdentifierProperty(System.String)">
            Set identifier.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setIdentifierProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetKeywordsProperty(System.String)">
            Set keywords.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setKeywordsProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetLanguageProperty(System.String)">
            Set language.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setLanguageProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetLastModifiedByProperty(System.String)">
            Set last modifications author.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setLastModifiedByProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetLastPrintedProperty(System.String)">
            Set last printed date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setLastPrintedProperty(org.apache.poi.OpenXml4Net.util.Nullable)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetLastPrintedProperty(System.Nullable{System.DateTime})">
            Set last printed date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setLastPrintedProperty(org.apache.poi.OpenXml4Net.util.Nullable)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetModifiedProperty(System.String)">
            Set last modification date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setModifiedProperty(org.apache.poi.OpenXml4Net.util.Nullable)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetModifiedProperty(System.Nullable{System.DateTime})">
            Set last modification date.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setModifiedProperty(org.apache.poi.OpenXml4Net.util.Nullable)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetRevisionProperty(System.String)">
            Set revision.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setRevisionProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetSubjectProperty(System.String)">
            Set subject.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setSubjectProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetTitleProperty(System.String)">
            Set title.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setTitleProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetVersionProperty(System.String)">
            Set version.
            
            @see org.apache.poi.OpenXml4Net.opc.PackageProperties#setVersionProperty(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetStringValue(System.String)">
            Convert a strig value into a String
        </member>
        <!-- 对于成员“M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.SetDateValue(System.String)”忽略有格式错误的 XML 注释 -->
        <!-- 对于成员“M:NPOI.OpenXml4Net.OPC.Internal.PackagePropertiesPart.GetDateValue(System.Nullable{System.DateTime})”忽略有格式错误的 XML 注释 -->
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.PartUnmarshaller">
             Object implemented this interface are considered as part unmarshaller. A part
             unmarshaller is responsible to unmarshall a part in order to load it from a
             package.
            
             <AUTHOR> Chable
             @version 0.1
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.PartUnmarshaller.Unmarshall(NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.UnmarshallContext,System.IO.Stream)">
             Save the content of the package in the stream
            
             @param in
                        The input stream from which the part will be unmarshall.
             @return The part freshly unmarshall from the input stream.
             @throws OpenXml4NetException
                         Throws only if any other exceptions are thrown by inner
                         methods.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.PackagePropertiesUnmarshaller">
             Package properties unmarshaller.
            
             <AUTHOR> Chable
             @version 1.0
        </member>
        <!-- 对于成员“M:NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.PackagePropertiesUnmarshaller.CheckElementForOPCCompliance(System.Xml.XmlElement)”忽略有格式错误的 XML 注释 -->
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.UnmarshallContext">
            Context needed for the unmarshall process of a part. This class is immutable.
            
            <AUTHOR> Chable
            @version 1.0
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.UnmarshallContext.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName)">
            Constructor.
            
            @param targetPackage
                       Container.
            @param partName
                       Name of the part to unmarshall.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.UnmarshallContext.Package">
            @return the container
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.UnmarshallContext.PartName">
            @return the partName
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.Internal.Unmarshallers.UnmarshallContext.ZipEntry">
            @return the zipEntry
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.Internal.ZipContentTypeManager">
            Zip implementation of the ContentTypeManager.
            
            <AUTHOR> Chable
            @version 1.0
            @see ContentTypeManager
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipContentTypeManager.#ctor(System.IO.Stream,NPOI.OpenXml4Net.OPC.OPCPackage)">
            Delegate constructor to the super constructor.
            
            @param in
                       The input stream to parse to fill internal content type
                       collections.
            @throws InvalidFormatException
                        If the content types part content is not valid.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.FORWARD_SLASH">
            Forward slash use to convert part name between OPC and zip item naming
            conventions.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.READ_WRITE_FILE_BUFFER_SIZE">
            Buffer to read data from file. Use big buffer to improve performaces. the
            InputStream class is reading only 8192 bytes per read call (default value
            set by sun)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.#ctor">
            Prevent this class to be instancied.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.GetCorePropertiesZipEntry(NPOI.OpenXml4Net.OPC.ZipPackage)">
             Retrieve the zip entry of the core properties part.
            
             @throws OpenXml4NetException
                         Throws if internal error occurs.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.GetContentTypeZipEntry(NPOI.OpenXml4Net.OPC.ZipPackage)">
            Retrieve the Zip entry of the content types part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.GetOPCNameFromZipItemName(System.String)">
             Convert a zip name into an OPC name by adding a leading forward slash to
             the specified item name.
            
             @param zipItemName
                        Zip item name to convert.
             @return An OPC compliant name.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.GetZipItemNameFromOPCName(System.String)">
             Convert an OPC item name into a zip item name by removing any leading
             forward slash if it exist.
            
             @param opcItemName
                        The OPC item name to convert.
             @return A zip item name without any leading slashes.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.GetZipURIFromOPCName(System.String)">
             Convert an OPC item name into a zip URI by removing any leading forward
             slash if it exist.
            
             @param opcItemName
                        The OPC item name to convert.
             @return A zip URI without any leading slashes.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.OpenZipFile(System.IO.FileInfo)">
             Opens the specified file as a zip, or returns null if no such file exists
            
             @param file
                        The file to open.
             @return The zip archive freshly open.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.Internal.ZipHelper.OpenZipFile(System.String)">
             Retrieve and open a zip file with the specified path.
            
             @param path
                        The file path.
             @return The zip archive freshly open.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.OPCPackage">
            Represents a container that can store multiple data objects.
            
            <AUTHOR> Chable, CDubet
            @version 0.1
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.logger">
            Logger.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.defaultPackageAccess">
            Default package access.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.packageAccess">
            Package access.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.partList">
            Package parts collection.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.relationships">
            Package relationships.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.partMarshallers">
            Part marshallers by content type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.defaultPartMarshaller">
            Default part marshaller.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.partUnmarshallers">
            Part unmarshallers by content type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.packageProperties">
            Core package properties.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.contentTypeManager">
            Manage parts content types of this package.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.isDirty">
            Flag if a modification is done to the document.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.originalPackagePath">
            File path of this package.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.OPCPackage.output">
            Output stream for writing this package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.#ctor(NPOI.OpenXml4Net.OPC.PackageAccess)">
            Constructor.
            
            @param access
                       Package access.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Init">
            Initialize the package instance.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Open(System.String)">
            Open a package with read/write permission.
            
            @param path
                       The document path.
            @return A Package object, else <b>null</b>.
            @throws InvalidFormatException
                        If the specified file doesn't exist, and a parsing error
                        occur.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Open(System.IO.FileInfo)">
             Open a package with read/write permission.
            
             @param file
                        The file to open.
             @return A Package object, else <b>null</b>.
             @throws InvalidFormatException
                         If the specified file doesn't exist, and a parsing error
                         occur.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Open(System.String,NPOI.OpenXml4Net.OPC.PackageAccess)">
            Open a package.
            
            @param path
                       The document path.
            @param access
                       PackageBase access.
            @return A PackageBase object, else <b>null</b>.
            @throws InvalidFormatException
                        If the specified file doesn't exist, and a parsing error
                        occur.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Open(System.IO.FileInfo,NPOI.OpenXml4Net.OPC.PackageAccess)">
             Open a package.
            
             @param file
                        The file to open.
             @param access
                        PackageBase access.
             @return A PackageBase object, else <b>null</b>.
             @throws InvalidFormatException
                         If the specified file doesn't exist, and a parsing error
                         occur.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Open(System.IO.Stream)">
            Open a package.
            
            Note - uses quite a bit more memory than {@link #open(String)}, which
            doesn't need to hold the whole zip file in memory, and can take advantage
            of native methods
            
            @param in
                       The InputStream to read the package from
            @return A PackageBase object
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.OpenOrCreate(System.String)">
            Opens a package if it exists, else it Creates one.
            
            @param file
                       The file to open or to Create.
            @return A newly Created package if the specified file does not exist,
                    else the package extract from the file.
            @throws InvalidFormatException
                        Throws if the specified file exist and is not valid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Create(System.String)">
            Creates a new package.
            
            @param file
                       Path of the document.
            @return A newly Created PackageBase ready to use.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.ConfigurePackage(NPOI.OpenXml4Net.OPC.OPCPackage)">
            Configure the package.
            
            @param pkg
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Flush">
            Flush the package : save all.
            
            @see #close()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Close">
            Close the package and save its content.
            
            @throws IOException
                        If an IO exception occur during the saving process.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Revert">
            Close the package WITHOUT saving its content. Reinitialize this package
            and cancel all changes done to it.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddThumbnail(System.String)">
            Add a thumbnail to the package. This method is provided to make easier
            the addition of a thumbnail in a package. You can do the same work by
            using the traditionnal relationship and part mechanism.
            
            @param path
                       The full path to the image file.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.ThrowExceptionIfReadOnly">
            Throws an exception if the package access mode is in read only mode
            (PackageAccess.Read).
            
            @throws InvalidOperationException
                        Throws if a writing operation is done on a read only package.
            @see org.apache.poi.OpenXml4Net.opc.PackageAccess
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.ThrowExceptionIfWriteOnly">
            Throws an exception if the package access mode is in write only mode
            (PackageAccess.Write). This method is call when other methods need write
            right.
            
            @throws InvalidOperationException
                        Throws if a read operation is done on a write only package.
            @see org.apache.poi.OpenXml4Net.opc.PackageAccess
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPackageProperties">
            Retrieves or Creates if none exists, core package property part.
            
            @return The PackageProperties part of this package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPart(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Retrieve a part identified by its name.
            
            @param PartName
                       Part name of the part to retrieve.
            @return The part with the specified name, else <code>null</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPartsByContentType(System.String)">
            Retrieve parts by content type.
            
            @param contentType
                       The content type criteria.
            @return All part associated to the specified content type.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPartsByRelationshipType(System.String)">
            Retrieve parts by relationship type.
            
            @param relationshipType
                       Relationship type.
            @return All parts which are the target of a relationship with the
                    specified type, if the method can't retrieve relationships from
                    the package, then return <code>null</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPart(NPOI.OpenXml4Net.OPC.PackageRelationship)">
            Get the target part from the specified relationship.
            
            @param partRel
                       The part relationship uses to retrieve the part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetParts">
            Load the parts of the archive if it has not been done yet. The
            relationships of each part are not loaded.
            Note - Rule M4.1 states that there may only ever be one Core
             Properties Part, but Office produced files will sometimes
             have multiple! As Office ignores all but the first, we relax
             Compliance with Rule M4.1, and ignore all others silently too. 
            @return All this package's parts.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.CreatePart(NPOI.OpenXml4Net.OPC.PackagePartName,System.String)">
            Create and Add a part, with the specified name and content type, to the
            package.
            
            @param PartName
                       Part name.
            @param contentType
                       Part content type.
            @return The newly Created part.
            @throws InvalidFormatException
                        If rule M1.12 is not verified : Packages shall not contain
                        equivalent part names and package implementers shall neither
                        Create nor recognize packages with equivalent part names.
            @see #CreatePartImpl(PackagePartName, String, bool) 
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.CreatePart(NPOI.OpenXml4Net.OPC.PackagePartName,System.String,System.Boolean)">
            Create and Add a part, with the specified name and content type, to the
            package. For general purpose, prefer the overload version of this method
            without the 'loadRelationships' parameter.
            
            @param PartName
                       Part name.
            @param contentType
                       Part content type.
            @param loadRelationships
                       Specify if the existing relationship part, if any, logically
                       associated to the newly Created part will be loaded.
            @return The newly Created part.
            @throws InvalidFormatException
                        If rule M1.12 is not verified : Packages shall not contain
                        equivalent part names and package implementers shall neither
                        Create nor recognize packages with equivalent part names.
            @see {@link#CreatePartImpl(URI, String)}
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.CreatePart(NPOI.OpenXml4Net.OPC.PackagePartName,System.String,System.IO.MemoryStream)">
            Add a part to the package.
            
            @param PartName
                       Part name of the part to Create.
            @param contentType
                       type associated with the file
            @param content
                       the contents to Add. In order to have faster operation in
                       document merge, the data are stored in memory not on a hard
                       disk
            
            @return The new part.
            @see #CreatePart(PackagePartName, String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddPackagePart(NPOI.OpenXml4Net.OPC.PackagePart)">
            Add the specified part to the package. If a part already exists in the
            package with the same name as the one specified, then we replace the old
            part by the specified part.
            
            @param part
                       The part to Add (or replace).
            @return The part Added to the package, the same as the one specified.
            @throws InvalidFormatException
                        If rule M1.12 is not verified : Packages shall not contain
                        equivalent part names and package implementers shall neither
                        Create nor recognize packages with equivalent part names.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RemovePart(NPOI.OpenXml4Net.OPC.PackagePart)">
            Remove the specified part in this package. If this part is relationship
            part, then delete all relationships in the source part.
            
            @param part
                       The part to Remove. If <code>null</code>, skip the action.
            @see #RemovePart(PackagePartName)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RemovePart(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Remove a part in this package. If this part is relationship part, then
            delete all relationships in the source part.
            
            @param PartName
                       The part name of the part to Remove.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RemovePartRecursive(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Remove a part from this package as well as its relationship part, if one
            exists, and all parts listed in the relationship part. Be aware that this
            do not delete relationships which target the specified part.
            
            @param PartName
                       The name of the part to delete.
            @throws InvalidFormatException
                        Throws if the associated relationship part of the specified
                        part is not valid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.DeletePart(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Delete the part with the specified name and its associated relationships
            part if one exists. Prefer the use of this method to delete a part in the
            package, compare to the Remove() methods that don't Remove associated
            relationships part.
            
            @param PartName
                       Name of the part to delete
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.DeletePartRecursive(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Delete the part with the specified name and all part listed in its
            associated relationships part if one exists. This process is recursively
            apply to all parts in the relationships part of the specified part.
            Prefer the use of this method to delete a part in the package, compare to
            the Remove() methods that don't Remove associated relationships part.
            
            @param PartName
                       Name of the part to delete
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.ContainPart(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Check if a part already exists in this package from its name.
            
            @param PartName
                       Part name to check.
            @return <i>true</i> if the part is logically Added to this package, else
                    <i>false</i>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddRelationship(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.TargetMode,System.String,System.String)">
            Add a relationship to the package (except relationships part).
            
            Check rule M4.1 : The format designer shall specify and the format
            producer shall Create at most one core properties relationship for a
            package. A format consumer shall consider more than one core properties
            relationship for a package to be an error. If present, the relationship
            shall target the Core Properties part.
            
            Check rule M1.25: The Relationships part shall not have relationships to
            any other part. Package implementers shall enforce this requirement upon
            the attempt to Create such a relationship and shall treat any such
            relationship as invalid.
            
            @param targetPartName
                       Target part name.
            @param targetMode
                       Target mode, either Internal or External.
            @param relationshipType
                       Relationship type.
            @param relID
                       ID of the relationship.
            @see PackageRelationshipTypes
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddRelationship(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.TargetMode,System.String)">
            Add a package relationship.
            
            @param targetPartName
                       Target part name.
            @param targetMode
                       Target mode, either Internal or External.
            @param relationshipType
                       Relationship type.
            @see PackageRelationshipTypes
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddExternalRelationship(System.String,System.String)">
            Adds an external relationship to a part (except relationships part).
            
            The targets of external relationships are not subject to the same
            validity checks that internal ones are, as the contents is potentially
            any file, URL or similar.
            
            @param target
                       External target of the relationship
            @param relationshipType
                       Type of relationship.
            @return The newly Created and Added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#AddExternalRelationship(java.lang.String,
                 java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddExternalRelationship(System.String,System.String,System.String)">
            Adds an external relationship to a part (except relationships part).
            
            The targets of external relationships are not subject to the same
            validity checks that internal ones are, as the contents is potentially
            any file, URL or similar.
            
            @param target
                       External target of the relationship
            @param relationshipType
                       Type of relationship.
            @param id
                       Relationship unique id.
            @return The newly Created and Added relationship
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#AddExternalRelationship(java.lang.String,
                 java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RemoveRelationship(System.String)">
            Delete a relationship from this package.
            
            @param id
                       Id of the relationship to delete.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetRelationshipsByType(System.String)">
            Retrieves all relationships with the specified type.
            
            @param relationshipType
                       The filter specifying the relationship type.
            @return All relationships with the specified relationship type.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetRelationshipsHelper(System.String)">
            Retrieves all relationships with specified id (normally just ine because
            a relationship id is supposed to be unique).
            
            @param id
                       Id of the wanted relationship.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.ClearRelationships">
            Clear package relationships.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.EnsureRelationships">
            Ensure that the relationships collection is not null.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetRelationship(System.String)">
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#GetRelationship(java.lang.String)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.IsRelationshipExists(NPOI.OpenXml4Net.OPC.PackageRelationship)">
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#isRelationshipExists(org.apache.poi.OpenXml4Net.opc.PackageRelationship)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddMarshaller(System.String,NPOI.OpenXml4Net.OPC.Internal.PartMarshaller)">
            Add a marshaller.
            
            @param contentType
                       The content type to bind to the specified marshaller.
            @param marshaller
                       The marshaller to register with the specified content type.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.AddUnmarshaller(System.String,NPOI.OpenXml4Net.OPC.Internal.PartUnmarshaller)">
            Add an unmarshaller.
            
            @param contentType
                       The content type to bind to the specified unmarshaller.
            @param unmarshaller
                       The unmarshaller to register with the specified content type.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RemoveMarshaller(System.String)">
            Remove a marshaller by its content type.
            
            @param contentType
                       The content type associated with the marshaller to Remove.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RemoveUnmarshaller(System.String)">
            Remove an unmarshaller by its content type.
            
            @param contentType
                       The content type associated with the unmarshaller to Remove.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPackageAccess">
            Get the package access mode.
            
            @return the packageAccess The current package access.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.ValidatePackage(NPOI.OpenXml4Net.OPC.OPCPackage)">
            Validates the package compliance with the OPC specifications.
            
            @return <b>true</b> if the package is valid else <b>false</b>
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Save(System.String)">
            Save the document in the specified file.
            
            @param targetFile
                       Destination file.
            @throws IOException
                        Throws if an IO exception occur.
            @see #save(OutputStream)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.Save(System.IO.Stream)">
            Save the document in the specified output stream.
            
            @param outputStream
                       The stream to save the package.
            @see #saveImpl(OutputStream)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.CreatePartImpl(NPOI.OpenXml4Net.OPC.PackagePartName,System.String,System.Boolean)">
            Core method to Create a package part. This method must be implemented by
            the subclass.
            
            @param PartName
                       URI of the part to Create.
            @param contentType
                       Content type of the part to Create.
            @return The newly Created package part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RemovePartImpl(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Core method to delete a package part. This method must be implemented by
            the subclass.
            
            @param PartName
                       The URI of the part to delete.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.FlushImpl">
            Flush the package but not save.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.CloseImpl">
            Close the package and cause a save of the package.
            
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.RevertImpl">
            Close the package without saving the document. Discard all changes made
            to this package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.SaveImpl(System.IO.Stream)">
            Save the package into the specified output stream.
            
            @param outputStream
                       The output stream use to save this package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPartImpl(NPOI.OpenXml4Net.OPC.PackagePartName)">
            Get the package part mapped to the specified URI.
            
            @param PartName
                       The URI of the part to retrieve.
            @return The package part located by the specified URI, else <b>null</b>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.GetPartsImpl">
            Get all parts link to the package.
            
            @return A list of the part owned by the package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.OPCPackage.ReplaceContentType(System.String,System.String)">
             Replace a content type in this package.
            
             <p>
                 A typical scneario to call this method is to rename a template file to the main format, e.g.
                 ".dotx" to ".docx"
                 ".dotm" to ".docm"
                 ".xltx" to ".xlsx"
                 ".xltm" to ".xlsm"
                 ".potx" to ".pptx"
                 ".potm" to ".pptm"
             </p>
             For example, a code converting  a .xlsm macro workbook to .xlsx would look as follows:
             <p>
                <pre><code>
            
                 OPCPackage pkg = OPCPackage.open(new FileInputStream("macro-workbook.xlsm"));
                 pkg.replaceContentType(
                     "application/vnd.ms-excel.sheet.macroEnabled.main+xml",
                     "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml");
            
                 FileOutputStream out = new FileOutputStream("workbook.xlsx");
                 pkg.save(out);
                 out.close();
            
                </code></pre>
             </p>
            
             @param oldContentType  the content type to be replaced
             @param newContentType  the replacement
             @return whether replacement was succesfull
             @since POI-3.8
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.OPCPackage.Relationships">
            Retrieves all package relationships.
            
            @return All package relationships of this package.
            @throws OpenXml4NetException
            @see #GetRelationshipsHelper(String)
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.OPCPackage.HasRelationships">
            @see org.apache.poi.OpenXml4Net.opc.RelationshipSource#hasRelationships()
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackageAccess">
             Specifies package access.
            
             <AUTHOR> Chable
             @version 1.0
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageAccess.READ">
            Read only. Write not authorized. 
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageAccess.WRITE">
            Write only. Read not authorized. 
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageAccess.READ_WRITE">
            Read and Write mode. 
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackageNamespaces">
             Open Packaging Convention namespaces URI.
            
             <AUTHOR> Chable
             @version 1.0
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageNamespaces.CONTENT_TYPES">
            Content Types.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageNamespaces.CORE_PROPERTIES">
            Core Properties.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageNamespaces.DIGITAL_SIGNATURE">
            Digital Signatures.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageNamespaces.RELATIONSHIPS">
            Relationships.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageNamespaces.MARKUP_COMPATIBILITY">
            Markup Compatibility.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackagePartCollection">
             A package part collection.
            
             <AUTHOR> Chable
             @version 0.1
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePartCollection.registerPartNameStr">
            Arraylist use to store this collection part names as string for rule
            M1.11 optimized checking.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartCollection.Put(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.PackagePart)">
             Check rule [M1.11]: a package implementer shall neither create nor
             recognize a part with a part name derived from another part name by
             Appending segments to it.
            
             @exception InvalidOperationException
                            Throws if you try to add a part with a name derived from
                            another part name.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackagePartName">
             An immutable Open Packaging Convention compliant part name.
            
             <AUTHOR> Chable
            
             @see <a href="http://www.ietf.org/rfc/rfc3986.txt">http://www.ietf.org/rfc/rfc3986.txt</a>
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePartName.partNameURI">
            Part name stored as an URI.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePartName.RFC3986_PCHAR_SUB_DELIMS">
            Reserved characters for sub delimitations.
        </member>
        <!-- 对于成员“F:NPOI.OpenXml4Net.OPC.PackagePartName.RFC3986_PCHAR_UNRESERVED_SUP”忽略有格式错误的 XML 注释 -->
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePartName.RFC3986_PCHAR_AUTHORIZED_SUP">
            Authorized reserved characters for pChar.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagePartName.isRelationship">
            Flag to know if this part name is from a relationship part name.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.#ctor(System.Uri,System.Boolean)">
             Constructor. Makes a ValidPartName object from a java.net.URI
            
             @param uri
                        The URI to validate and to transform into ValidPartName.
             @param checkConformance
                        Flag to specify if the contructor have to validate the OPC
                        conformance. Must be always <code>true</code> except for
                        special URI like '/' which is needed for internal use by
                        OpenXml4Net but is not valid.
             @throws InvalidFormatException
                         Throw if the specified part name is not conform to Open
                         Packaging Convention specifications.
             @see java.net.URI
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.#ctor(System.String,System.Boolean)">
             Constructor. Makes a ValidPartName object from a String part name.
            
             @param partName
                        Part name to valid and to create.
             @param checkConformance
                        Flag to specify if the contructor have to validate the OPC
                        conformance. Must be always <code>true</code> except for
                        special URI like '/' which is needed for internal use by
                        OpenXml4Net but is not valid.
             @throws InvalidFormatException
                         Throw if the specified part name is not conform to Open
                         Packaging Convention specifications.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.IsRelationshipPartURI(System.Uri)">
             Check if the specified part name is a relationship part name.
            
             @param partUri
                        The URI to check.
             @return <code>true</code> if this part name respect the relationship
                     part naming convention else <code>false</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.IsRelationshipPartURI">
             Know if this part name is a relationship part name.
            
             @return <code>true</code> if this part name respect the relationship
                     part naming convention else <code>false</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.ThrowExceptionIfInvalidPartUri(System.Uri)">
             Throws an exception (of any kind) if the specified part name does not
             follow the Open Packaging Convention specifications naming rules.
            
             @param partUri
                        The part name to check.
             @throws Exception
                         Throws if the part name is invalid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.ThrowExceptionIfEmptyURI(System.Uri)">
             Throws an exception if the specified URI is empty. [M1.1]
            
             @param partURI
                        Part URI to check.
             @throws InvalidFormatException
                         If the specified URI is empty.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.ThrowExceptionIfPartNameHaveInvalidSegments(System.Uri)">
             Throws an exception if the part name has empty segments. [M1.3]
            
             Throws an exception if a segment any characters other than pchar
             characters. [M1.6]
            
             Throws an exception if a segment contain percent-encoded forward slash
             ('/'), or backward slash ('\') characters. [M1.7]
            
             Throws an exception if a segment contain percent-encoded unreserved
             characters. [M1.8]
            
             Throws an exception if the specified part name's segments end with a dot
             ('.') character. [M1.9]
            
             Throws an exception if a segment doesn't include at least one non-dot
             character. [M1.10]
            
             @param partUri
                        The part name to check.
             @throws InvalidFormatException
                         if the specified URI contain an empty segments or if one the
                         segments contained in the part name, ends with a dot ('.')
                         character.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.CheckPCharCompliance(System.String)">
             Throws an exception if a segment any characters other than pchar
             characters. [M1.6]
            
             Throws an exception if a segment contain percent-encoded forward slash
             ('/'), or backward slash ('\') characters. [M1.7]
            
             Throws an exception if a segment contain percent-encoded unreserved
             characters. [M1.8]
            
             @param segment
                        The segment to check
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.ThrowExceptionIfPartNameNotStartsWithForwardSlashChar(System.Uri)">
             Throws an exception if the specified part name doesn't start with a
             forward slash character '/'. [M1.4]
            
             @param partUri
                        The part name to check.
             @throws InvalidFormatException
                         If the specified part name doesn't start with a forward slash
                         character '/'.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.ThrowExceptionIfPartNameEndsWithForwardSlashChar(System.Uri)">
             Throws an exception if the specified part name ends with a forwar slash
             character '/'. [M1.5]
            
             @param partUri
                        The part name to check.
             @throws InvalidFormatException
                         If the specified part name ends with a forwar slash character
                         '/'.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.ThrowExceptionIfAbsoluteUri(System.Uri)">
             Throws an exception if the specified URI is absolute.
            
             @param partUri
                        The URI to check.
             @throws InvalidFormatException
                         Throws if the specified URI is absolute.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.Compare(NPOI.OpenXml4Net.OPC.PackagePartName,NPOI.OpenXml4Net.OPC.PackagePartName)">
             Compare two part name following the rule M1.12 :
            
             Part name equivalence is determined by comparing part names as
             case-insensitive ASCII strings. Packages shall not contain equivalent
             part names and package implementers shall neither create nor recognize
             packages with equivalent part names. [M1.12]
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagePartName.Equals(System.Object)">
            Part name equivalence is determined by comparing part names as
            case-insensitive ASCII strings. Packages shall not contain equivalent
            part names and package implementers shall neither create nor recognize
            packages with equivalent part names. [M1.12]
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePartName.Extension">
             Retrieves the extension of the part name if any. If there is no extension
             returns an empty String. Example : '/document/content.xml' => 'xml'
            
             @return The extension of the part name.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePartName.Name">
             Get this part name.
            
             @return The name of this part name.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagePartName.URI">
             Part name property getter.
            
             @return This part name URI.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackageRelationship">
            A part relationship.
            
            <AUTHOR> Chable
            @version 1.0
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationship.id">
            L'ID de la relation.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationship.container">
            Reference to the package.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationship.relationshipType">
            Type de relation.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationship.source">
            Partie source de cette relation.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationship.targetMode">
            Le mode de ciblage [Internal|External]
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationship.targetUri">
            URI de la partie cible.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationship.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePart,System.Uri,NPOI.OpenXml4Net.OPC.TargetMode,System.String,System.String)">
            Constructor.
            
            @param pkg
            @param sourcePart
            @param targetUri
            @param targetMode
            @param relationshipType
            @param id
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationship.Package">
            @return the container
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationship.Id">
            @return the id
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationship.RelationshipType">
            @return the relationshipType
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationship.Source">
            @return the source
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationship.SourceUri">
            
            @return URL of the source part of this relationship
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationship.TargetMode">
            public URI getSourceUri(){ }
            
            @return the targetMode
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationship.TargetUri">
            @return the targetUri
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection">
            Represents a collection of PackageRelationship elements that are owned by a
            given PackagePart or the Package.
            
            <AUTHOR> Chable, CDubettier
            @version 0.1
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.relationshipsByID">
            Package relationships ordered by ID.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.relationshipsByType">
            Package relationships ordered by type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.relationshipPart">
            This relationshipPart.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.sourcePart">
            Source part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.partName">
            This part name.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.container">
            Reference to the package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.#ctor">
            Constructor.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.#ctor(NPOI.OpenXml4Net.OPC.PackageRelationshipCollection,System.String)">
            Copy constructor.
            
            This collection will contain only elements from the specified collection
            for which the type is compatible with the specified relationship type
            filter.
            
            @param coll
                       Collection to import.
            @param filter
                       Relationship type filter.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage)">
            Constructor.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.#ctor(NPOI.OpenXml4Net.OPC.PackagePart)">
            Constructor.
            
            @throws InvalidFormatException
                        Throws if the format of the content part is invalid.
            
            @throws InvalidOperationException
                        Throws if the specified part is a relationship part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePart)">
            Constructor. Parse the existing package relationship part if one exists.
            
            @param container
                       The parent package.
            @param part
                       The part that own this relationships collection. If <b>null</b>
                       then this part is considered as the package root.
            @throws InvalidFormatException
                        If an error occurs during the parsing of the relatinships
                        part fo the specified part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.GetRelationshipPartName(NPOI.OpenXml4Net.OPC.PackagePart)">
            Get the relationship part name of the specified part.
            
            @param part
                       The part .
            @return The relationship part name of the specified part. Be careful,
                    only the correct name is returned, this method does not check if
                    the part really exist in a package !
            @throws InvalidOperationException
                        Throws if the specified part is a relationship part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.AddRelationship(NPOI.OpenXml4Net.OPC.PackageRelationship)">
            Add the specified relationship to the collection.
            
            @param relPart
                       The relationship to add.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.AddRelationship(System.Uri,NPOI.OpenXml4Net.OPC.TargetMode,System.String,System.String)">
            Add a relationship to the collection.
            
            @param targetUri
                       Target URI.
            @param targetMode
                       The target mode : INTERNAL or EXTERNAL
            @param relationshipType
                       Relationship type.
            @param id
                       Relationship ID.
            @return The newly created relationship.
            @see PackageAccess
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.RemoveRelationship(System.String)">
            Remove a relationship by its ID.
            
            @param id
                       The relationship ID to Remove.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.RemoveRelationship(NPOI.OpenXml4Net.OPC.PackageRelationship)">
            Remove a relationship by its reference.
            
            @param rel
                       The relationship to delete.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.GetRelationship(System.Int32)">
            Retrieves a relationship by its index in the collection.
            
            @param index
                       Must be a value between [0-relationships_count-1]
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.GetRelationshipByID(System.String)">
            Retrieves a package relationship based on its id.
            
            @param id
                       ID of the package relationship to retrieve.
            @return The package relationship identified by the specified id.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.ParseRelationshipsPart(NPOI.OpenXml4Net.OPC.PackagePart)">
            Parse the relationship part and add all relationship in this collection.
            
            @param relPart
                       The package part to parse.
            @throws InvalidFormatException
                        Throws if the relationship part is invalid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.GetRelationships(System.String)">
            Retrieves all relations with the specified type.
            
            @param typeFilter
                       Relationship type filter. If <b>null</b> then all
                       relationships are returned.
            @return All relationships of the type specified by the filter.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.GetEnumerator">
            Get this collection's iterator.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.Iterator(System.String)">
            Get an iterator of a collection with all relationship with the specified
            type.
            
            @param typeFilter
                       Type filter.
            @return An iterator to a collection containing all relationships with the
                    specified type contain in this collection.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.Clear">
            Clear all relationships.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackageRelationshipCollection.Size">
            Get the numbe rof relationships in the collection.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes">
             Relationship types.
            
             <AUTHOR> Chable
             @version 0.2
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.CORE_PROPERTIES">
            Core properties relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.DIGITAL_SIGNATURE">
            Digital signature relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.DIGITAL_SIGNATURE_CERTIFICATE">
            Digital signature certificate relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.DIGITAL_SIGNATURE_ORIGIN">
            Digital signature origin relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.THUMBNAIL">
            Thumbnail relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.EXTENDED_PROPERTIES">
            Extended properties relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.CUSTOM_PROPERTIES">
            Custom properties relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.CORE_DOCUMENT">
            Core properties relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.CUSTOM_XML">
            Custom XML relationship type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.IMAGE_PART">
            Image type.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackageRelationshipTypes.STYLE_PART">
            Style type.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.PackagingUriHelper">
             Helper for part and pack Uri.
            
             <AUTHOR> Chable, CDubet, Kim Ung
             @version 0.1
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.packageRootUri">
            Package root Uri.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.RELATIONSHIP_PART_EXTENSION_NAME">
            Extension name of a relationship part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.RELATIONSHIP_PART_SEGMENT_NAME">
            Segment name of a relationship part.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.PACKAGE_PROPERTIES_SEGMENT_NAME">
            Segment name of the package properties folder.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.PACKAGE_CORE_PROPERTIES_NAME">
            Core package properties art name.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.FORWARD_SLASH_CHAR">
            Forward slash Uri separator.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.FORWARD_SLASH_STRING">
            Forward slash Uri separator.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.PACKAGE_RELATIONSHIPS_ROOT_URI">
            Package relationships part Uri
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.PACKAGE_RELATIONSHIPS_ROOT_PART_NAME">
            Package relationships part name.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.CORE_PROPERTIES_URI">
            Core properties part Uri.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.CORE_PROPERTIES_PART_NAME">
            Core properties partname.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.PACKAGE_ROOT_URI">
            Root package Uri.
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.PackagingUriHelper.PACKAGE_ROOT_PART_NAME">
            Root package part name.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.IsRelationshipPartURI(System.Uri)">
             Know if the specified Uri is a relationship part name.
            
             @param partUri
                        Uri to check.
             @return <i>true</i> if the Uri <i>false</i>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.GetFilename(System.Uri)">
            Get file name from the specified Uri.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.GetFilenameWithoutExtension(System.Uri)">
            Get the file name without the trailing extension.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.GetPath(System.Uri)">
            Get the directory path from the specified Uri.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.Combine(System.Uri,System.Uri)">
             Combine two URIs.
            
             @param prefix the prefix Uri
             @param suffix the suffix Uri
            
             @return the Combined Uri
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.Combine(System.String,System.String)">
            Combine a string Uri with a prefix and a suffix.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.RelativizeUri(System.Uri,System.Uri,System.Boolean)">
             Fully relativize the source part Uri against the target part Uri.
            
             @param sourceURI
                        The source part Uri.
             @param targetURI
                        The target part Uri.
             @return A fully relativize part name Uri ('word/media/image1.gif',
                     '/word/document.xml' => 'media/image1.gif') else
                     <code>null</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.RelativizeUri(System.Uri,System.Uri)">
             Fully relativize the source part URI against the target part URI.
            
             @param sourceURI
                        The source part URI.
             @param targetURI
                        The target part URI.
             @return A fully relativize part name URI ('word/media/image1.gif',
                     '/word/document.xml' => 'media/image1.gif') else
                     <code>null</code>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.ResolvePartUri(System.Uri,System.Uri)">
             Resolve a source uri against a target.
            
             @param sourcePartUri
                        The source Uri.
             @param targetUri
                        The target Uri.
             @return The resolved Uri.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.GetURIFromPath(System.String)">
            Get Uri from a string path.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.GetSourcePartUriFromRelationshipPartUri(System.Uri)">
             Get the source part Uri from a specified relationships part.
            
             @param relationshipPartUri
                        The relationship part use to retrieve the source part.
             @return The source part Uri from the specified relationships part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.CreatePartName(System.Uri)">
             Create an OPC compliant part name by throwing an exception if the Uri is
             not valid.
            
             @param partUri
                        The part name Uri to validate.
             @return A valid part name object, else <code>null</code>.
             @throws InvalidFormatException
                         Throws if the specified Uri is not OPC compliant.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.CreatePartName(System.String)">
             Create an OPC compliant part name.
            
             @param partName
                        The part name to validate.
             @return The correspondant part name if valid, else <code>null</code>.
             @throws InvalidFormatException
                         Throws if the specified part name is not OPC compliant.
             @see #CreatePartName(Uri)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.CreatePartName(System.String,NPOI.OpenXml4Net.OPC.PackagePart)">
             Create an OPC compliant part name by resolving it using a base part.
            
             @param partName
                        The part name to validate.
             @param relativePart
                        The relative base part.
             @return The correspondant part name if valid, else <code>null</code>.
             @throws InvalidFormatException
                         Throws if the specified part name is not OPC compliant.
             @see #CreatePartName(Uri)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.CreatePartName(System.Uri,NPOI.OpenXml4Net.OPC.PackagePart)">
             Create an OPC compliant part name by resolving it using a base part.
            
             @param partName
                        The part name Uri to validate.
             @param relativePart
                        The relative base part.
             @return The correspondant part name if valid, else <code>null</code>.
             @throws InvalidFormatException
                         Throws if the specified part name is not OPC compliant.
             @see #CreatePartName(Uri)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.IsValidPartName(System.Uri)">
             Validate a part Uri by returning a bool.
             ([M1.1],[M1.3],[M1.4],[M1.5],[M1.6])
            
             (OPC Specifications 8.1.1 Part names) :
            
             Part Name Syntax
            
             The part name grammar is defined as follows:
            
             <i>part_name = 1*( "/" segment )
            
             segment = 1*( pchar )</i>
            
            
             (pchar is defined in RFC 3986)
            
             @param partUri
                        The Uri to validate.
             @return <b>true</b> if the Uri is valid to the OPC Specifications, else
                     <b>false</b>
            
             @see #CreatePartName(Uri)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.DecodeURI(System.Uri)">
             Decode a Uri by converting all percent encoded character into a String
             character.
            
             @param uri
                        The Uri to decode.
             @return The specified Uri in a String with converted percent encoded
                     characters.
        </member>
        <!-- 对于成员“M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.ToUri(System.String)”忽略有格式错误的 XML 注释 -->
        <!-- 对于成员“M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.Encode(System.String)”忽略有格式错误的 XML 注释 -->
        <member name="M:NPOI.OpenXml4Net.OPC.PackagingUriHelper.GetRelationshipPartName(NPOI.OpenXml4Net.OPC.PackagePartName)">
             Build a part name where the relationship should be stored ((ex
             /word/document.xml -> /word/_rels/document.xml.rels)
            
             @param partName
                        Source part Uri
             @return the full path (as Uri) of the relation file
             @throws InvalidOperationException
                         Throws if the specified Uri is a relationshp part.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.PackagingUriHelper.PackageRootUri">
             Gets the Uri for the package root.
            
             @return Uri of the package root.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.StreamHelper.SaveXmlInStream(System.Xml.XmlDocument,System.IO.Stream)">
             Turning the DOM4j object in the specified output stream.
            
             @param xmlContent
                        The XML document.
             @param outStream
                        The Stream in which the XML document will be written.
             @return <b>true</b> if the xml is successfully written in the stream,
                     else <b>false</b>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.StreamHelper.CopyStream(System.IO.Stream,System.IO.Stream)">
             Copy the input stream into the output stream.
            
             @param inStream
                        The source stream.
             @param outStream
                        The destination stream.
             @return <b>true</b> if the operation succeed, else return <b>false</b>.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.TargetMode">
             Specifies whether the target of a PackageRelationship is inside or outside a
             Package.
            
             <AUTHOR> Chable
             @version 1.0
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.TargetMode.Internal">
            The relationship references a resource that is external to the package. 
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.TargetMode.External">
            The relationship references a part that is inside the package. 
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.ZipPackage">
             Physical zip package.
            
             <AUTHOR> Chable
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ZipPackage.zipArchive">
            Zip archive, as either a file on disk,
             or a stream
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.#ctor">
            Constructor. Creates a new ZipPackage.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.#ctor(System.IO.Stream,NPOI.OpenXml4Net.OPC.PackageAccess)">
             Constructor. <b>Operation not supported.</b>
            
             @param in
                        Zip input stream to load.
             @param access
             @throws ArgumentException
                         If the specified input stream not an instance of
                         ZipInputStream.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.#ctor(System.String,NPOI.OpenXml4Net.OPC.PackageAccess)">
             Constructor. Opens a Zip based Open XML document.
            
             @param path
                        The path of the file to open or create.
             @param access
                        The package access mode.
             @throws InvalidFormatException
                         If the content type part parsing encounters an error.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.#ctor(System.IO.FileInfo,NPOI.OpenXml4Net.OPC.PackageAccess)">
             Constructor. Opens a Zip based Open XML document.
            
             @param file
                        The file to open or create.
             @param access
                        The package access mode.
             @throws InvalidFormatException
                         If the content type part parsing encounters an error.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.GetPartsImpl">
             Retrieves the parts from this package. We assume that the package has not
             been yet inspect to retrieve all the parts, this method will open the
             archive and look for all parts contain inside it. If the package part
             list is not empty, it will be emptied.
            
             @return All parts contain in this package.
             @throws InvalidFormatException
                         Throws if the package is not valid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.BuildPartName(ICSharpCode.SharpZipLib.Zip.ZipEntry)">
            Builds a PackagePartName for the given ZipEntry,
             or null if it's the content types / invalid part
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.CreatePartImpl(NPOI.OpenXml4Net.OPC.PackagePartName,System.String,System.Boolean)">
             Create a new MemoryPackagePart from the specified URI and content type
            
            
             aram partName The part URI.
            
             @param contentType
                        The part content type.
             @return The newly created zip package part, else <b>null</b>.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.RemovePartImpl(NPOI.OpenXml4Net.OPC.PackagePartName)">
             Delete a part from the package
            
             @throws ArgumentException
                         Throws if the part URI is nulll or invalid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.FlushImpl">
            Flush the package. Do nothing.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.CloseImpl">
             Close and save the package.
            
             @see #close()
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.GenerateTempFileName(System.String)">
             Create a unique identifier to be use as a temp file name.
            
             @return A unique identifier use to be use as a temp file name.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.RevertImpl">
            Close the package without saving the document. Discard all the changes
            made to this package.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.GetPartImpl(NPOI.OpenXml4Net.OPC.PackagePartName)">
             Implement the getPart() method to retrieve a part from its URI in the
             current package
            
            
             @see #getPart(PackageRelationship)
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackage.SaveImpl(System.IO.Stream)">
             Save this package into the specified stream
            
            
             @param outputStream
                        The stream use to save this package.
            
             @see #save(OutputStream)
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.ZipPackage.ZipArchive">
             Get the zip archive
            
             @return The zip archive.
        </member>
        <member name="T:NPOI.OpenXml4Net.OPC.ZipPackagePart">
            Zip implementation of a PackagePart.
            
            <AUTHOR> Chable
            @version 1.0
            @see PackagePart
        </member>
        <member name="F:NPOI.OpenXml4Net.OPC.ZipPackagePart.zipEntry">
            The zip entry corresponding to this part.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackagePart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,NPOI.OpenXml4Net.OPC.PackagePartName,System.String)">
            Constructor.
            
            @param container
                       The container package.
            @param partName
                       Part name.
            @param contentType
                       Content type.
            @throws InvalidFormatException
                        Throws if the content of this part invalid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackagePart.#ctor(NPOI.OpenXml4Net.OPC.OPCPackage,ICSharpCode.SharpZipLib.Zip.ZipEntry,NPOI.OpenXml4Net.OPC.PackagePartName,System.String)">
            Constructor.
            
            @param container
                       The container package.
            @param zipEntry
                       The zip entry corresponding to this part.
            @param partName
                       The part name.
            @param contentType
                       Content type.
            @throws InvalidFormatException
                        Throws if the content of this part is invalid.
        </member>
        <member name="M:NPOI.OpenXml4Net.OPC.ZipPackagePart.GetInputStreamImpl">
            Implementation of the getInputStream() which return the inputStream of
            this part zip entry.
            
            @return Input stream of this part zip entry.
        </member>
        <member name="P:NPOI.OpenXml4Net.OPC.ZipPackagePart.ZipArchive">
            Get the zip entry of this part.
            
            @return The zip entry in the zip structure coresponding to this part.
        </member>
        <member name="T:NPOI.OpenXml4Net.Util.ZipEntrySource">
            An Interface to make getting the different bits
             of a Zip File easy.
            Allows you to get at the ZipEntries, without
             needing to worry about ZipFile vs ZipInputStream
             being annoyingly very different.
        </member>
        <member name="M:NPOI.OpenXml4Net.Util.ZipEntrySource.GetInputStream(ICSharpCode.SharpZipLib.Zip.ZipEntry)">
            Returns an InputStream of the decompressed 
             data that makes up the entry
        </member>
        <member name="M:NPOI.OpenXml4Net.Util.ZipEntrySource.Close">
            Indicates we are done with reading, and 
             resources may be freed
        </member>
        <member name="P:NPOI.OpenXml4Net.Util.ZipEntrySource.Entries">
            Returns an Enumeration of all the Entries
        </member>
        <member name="T:NPOI.OpenXml4Net.Util.ZipFileZipEntrySource">
            A ZipEntrySource wrapper around a ZipFile.
            Should be as low in terms of memory as a
             normal ZipFile implementation is.
        </member>
        <member name="T:NPOI.OpenXml4Net.Util.ZipInputStreamZipEntrySource">
            Provides a way to get at all the ZipEntries
             from a ZipInputStream, as many times as required.
            Allows a ZipInputStream to be treated much like
             a ZipFile, for a price in terms of memory.
            Be sure to call {@link #close()} as soon as you're
             done, to free up that memory!
        </member>
        <member name="M:NPOI.OpenXml4Net.Util.ZipInputStreamZipEntrySource.#ctor(ICSharpCode.SharpZipLib.Zip.ZipInputStream)">
            Reads all the entries from the ZipInputStream 
             into memory, and closes the source stream.
            We'll then eat lots of memory, but be able to
             work with the entries at-will.
        </member>
        <member name="T:NPOI.OpenXml4Net.Util.ZipInputStreamZipEntrySource.EntryEnumerator">
            Why oh why oh why are Iterator and Enumeration
             still not compatible?
        </member>
        <member name="T:NPOI.OpenXml4Net.Util.ZipInputStreamZipEntrySource.FakeZipEntry">
            So we can close the real zip entry and still
             effectively work with it.
            Holds the (decompressed!) data in memory, so
             close this as soon as you can! 
        </member>
    </members>
</doc>
