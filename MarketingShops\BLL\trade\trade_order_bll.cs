﻿using MarketingShops.BLL.Miniapp.trade;
using MarketingShops.DAL.trade;
using MarketingShops.Model;
using MarketingShops.Model.trade;
using MarketingShops.Model.trade.baseTable;
using MarketingShops.Model.trade.Search;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.trade
{
    /// <summary>
    /// 订单专用bll
    /// </summary>
  public class trade_order_bll
    {
      trade_order_dal _dal = new trade_order_dal();

      /// <summary>
      /// 订单查询
      /// </summary>
      /// <param name="sys">查询条件</param>
      /// <param name="Page">分页信息</param>
      /// <returns></returns>
      public PageResultData GetGridInfo(trade_search_params sys, Pagination Page)  
      {
          return _dal.GetGridInfo(sys, Page);
      }
      
      /// <summary>
      /// 获取订单详情
      /// </summary>
      /// <param name="orderid">订单编号</param>
      /// <returns></returns>
      public object GetTradeInfo(string orderid)
      {
          return _dal.GetTradeInfo(orderid);
      }
      
      /// <summary>
      /// 下单（添加订单记录）
      /// </summary>
      /// <param name="model">订单信息</param>
      /// <param name="pay">支付信息</param>
      /// <returns></returns>
      public int AddTradeInfo(TradeInfo model, buy_bill_payment pay)
      {
          if (model != null)
          {
              if (model.address != null)
                  model.address.contacts_no = Guid.NewGuid().ToString();
              if (model.goodsList != null && model.goodsList.Count > 0)
              {
                  for (int i = 0; i < model.goodsList.Count; i++)
                  {
                      model.goodsList[i].detailedno = Guid.NewGuid();
                  }
              }
              if (string.IsNullOrEmpty(model.bill_core.body)) model.bill_core.body = "";
              if (string.IsNullOrEmpty(model.bill_core.scene)) model.bill_core.scene = "";
              if (pay != null && !string.IsNullOrEmpty(pay.transactionId)) model.bill_core.billState = 2;
          }
          return _dal.AddTradeInfo(model, pay);
      }

      /// <summary>
      /// 订单完成
      /// </summary>
      /// <param name="orderid">订单号</param>
      /// <returns></returns>
      public int FinishOrder(string orderid)
      {
          int Result = _dal.FinishOrder(orderid);
          TradeInfo data = trade_mini_bll.GetMiniTrandInfo(orderid);
          if (Result > 0)
          {
              trade_mini_bll mini_bll = new trade_mini_bll();
              trade_mini_bll.MimiApp(orderid, data);
          }
          return Result;
      }
     
     
    }
}
