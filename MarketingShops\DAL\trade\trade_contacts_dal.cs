﻿using MarketingShops.Model.trade.baseTable;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.trade
{
    /// <summary>
    /// 订单-联系人专用dal
    /// </summary>
   public class trade_contacts_dal
    {
        /// <summary>
        /// 修改联系人信息
        /// </summary>
        /// <param name="contacts">联系人数据</param>
        /// <returns></returns>
        public int UpdateContactsInfo(buy_bill_contacts contacts)
        {
            string sql = @"UPDATE [MarketingShops].[dbo].[buy_bill_contacts]
   SET [name] = @name
      ,[phone] = @phone
      ,[region] = @region
      ,[detailed] = @detailed
 WHERE  [contacts_no] = @contacts_no";
            return DbHelp_MarketingShops.ado.OperationData(sql, contacts);
        }

        /// <summary>
        /// 获取收件人信息
        /// </summary>
        /// <param name="orderid">订单号</param>
        /// <returns></returns>
        public buy_bill_contacts GetContactsInfo(string orderid)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle<buy_bill_contacts>(@"SELECT [contacts_no]
      ,[name]
      ,[phone]
      ,[region]
      ,[detailed]
  FROM [MarketingShops].[dbo].[buy_bill_contacts] where orderid=@orderid", new { orderid = orderid });
        }
    }
}
