﻿using MarketingShops.Model;
using MarketingShops.Model.RefundTardeDetailed.Export;
using MarketingShops.Model.RefundTardeDetailed.Search;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.RefundTardeDetailed
{
    /// <summary>
    /// 账单退款明细
    /// </summary>
    public class refund_bill_detailed_dal
    {
        #region 查询（分页）
        /// <summary>
        /// 系统查询
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">页码信息</param>
        /// <returns></returns>
        public PageResultData GetGridInfo(string seltj, Pagination Page)
        {
            string sql = @"SELECT {0} FROM dbo.refund_bill_detailed where" + seltj;
            return DbHelp_MarketingShops.paging<refund_bill_detailed>(sql, @"
        [orderid]  
      ,[refundState]
      ,[number]
      ,[fee]
      ,[integral]
      ,[recharge]
      ,[opName]
      ,[opTime]", Page.sidx, Page.page, Page.rows);
        }
        #endregion

        #region 导出退款明细
        /// <summary>
        /// 获取导出数据结构
        /// </summary>
        /// <param name="sys">导出条件</param>
        /// <returns></returns>
        public List<export_refund_bill_detailed> ExportTradeList(string seltj)
        {
            string sql = @"SELECT   [orderid], [refundState], [number], [fee], [integral], [recharge], [opName], [opTime]  FROM dbo.refund_bill_detailed where" + seltj + " order by opTime desc";
            List<export_refund_bill_detailed> list = DbHelp_MarketingShops.ado.GetDataList<export_refund_bill_detailed>(sql);
            return list;
        }
        #endregion
    }
}
