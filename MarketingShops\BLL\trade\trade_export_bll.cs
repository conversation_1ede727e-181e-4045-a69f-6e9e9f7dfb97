﻿using MarketingShops.DAL.trade;
using MarketingShops.Model.trade;
using MarketingShops.Model.trade.baseTable;
using MarketingShops.Model.trade.Search;
using NFine.Code.Export;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.trade
{
    /// <summary>
    /// 订单-导出专用bll
    /// </summary>
   public class trade_export_bll
    {
       trade_export_dal _dal = new trade_export_dal();
        /// <summary>
        /// 导出报表
        /// </summary>
        /// <param name="sys">导出条件</param>
        /// <returns></returns>
       public void ExportTradeList(trade_search_params sys)   
        {
            string seltj = trade_search_dal.GetSearchStr(sys);
            string filename = "订单统计报表" + DateTime.Now.ToString("yyyy-MM-dd");
            string fileformat = "csv";
            List<ExportTradeInfo> data = _dal.ExportTradeList(seltj);
            ExportData ex = new ExportData();
            ex.ExportToExcel<ExportTradeInfo>(data, filename, fileformat);
        }
    }
}
