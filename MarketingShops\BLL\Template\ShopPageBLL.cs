﻿using MarketingShops.DAL.Template;
using MarketingShops.Model;
using MarketingShops.Model.Template.PageShopTemplate;
using MarketingShops.Model.Template.ShopPageTemplate;
using MarketingShops.Model.Template.ShopPageTemplate.search;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace MarketingShops.BLL.Template
{
    public class ShopPageBLL
    {
        private static ShopPageDAL _dal = new ShopPageDAL();
        #region  数据查询
        
        /// <summary>
        /// 查询活动信息
        /// </summary>
        /// <param name="pagination">页面信息</param>
        /// <param name="template">活动信息（查询的条件）</param>
        /// <returns></returns>
        public PageResultData GetGridJson(Pagination pagination, sell_search_params template)
        {
            //编号排序
            if (pagination.sidx == "KEY")
                pagination.sidx = "[KEY]";
            return _dal.GetGridJson(template, pagination);
        }
        /// <summary>
        /// 获取活动详情
        /// </summary>
        /// <param name="key">活动模板key</param>
        /// <returns></returns>
        public sell_edit_model GetActiveInfo(string key)
        {
            try
            {
                return _dal.GetActiveInfo(key.Trim());
            }
            catch (Exception ex)
            {
                throw new Exception("查询失败：" + ex.Message);
            }
        }
       

        public object GetEnumData()
        {
            List<State_Enum> StateDataList =  _dal.GetState();
            var data = _dal.GetTemplate();
            var PageTemplateDataList = data.Where(i => i.TemplateType == "page").ToList();
            var GoodTemplateDataList = data.Where(i => i.TemplateType == "goods").ToList();
            var TemplateData = new { PageTemplateDataList, GoodTemplateDataList };
            var GoodList = _dal.GetGood();
            return new
            {
                StateDataList,
                TemplateData,
                GoodList
            };
        }
        /// <summary>
        /// 获取组合购信息，包括商品详情
        /// </summary>
        /// <param name="KEY">组合购key</param>
        /// <returns></returns>
        public GetSellInfo GetSellCombinationInfo(string KEY)
        {
            var TemplateBase = _dal.GetSellCombinationInfo(KEY.Trim());
            if (TemplateBase == null) throw new Exception("查询不到相关组合购数据");
            GetSellInfo data = new GetSellInfo()
            {
                KEY=TemplateBase.KEY,
                Banner = TemplateBase.Banner,
                BTime=TemplateBase.BTime,
                ETime=TemplateBase.ETime,
                TemplateNo = TemplateBase.TemplateNo,
                Title = TemplateBase.Title,
                Describe = TemplateBase.Describe,
                State = TemplateBase.State,
                IsShowMember = TemplateBase.IsShowMember,
                type = "api"
            };
            var GoodItems = _dal.GetGID(KEY.Trim());
            if (GoodItems.Count > 0) {
                data.Shop = new ShopList();
                data.Shop.goodsList = GoodItems;
            }
            data.Luck = _dal.GetLuckList(TemplateBase.KEY.Trim());
            if (data.Luck == null)
                data.Luck = new Sell_Combination_luck();
            return data;
        }
        #endregion
        #region  数据操作
        /// <summary>
        /// 添加/修改组合购
        /// </summary>
        /// <param name="TemplateData">组合购信息</param>
        /// <returns></returns>
        public int SubmitForm(sell_edit_model TemplateData,bool start_up) 
        {
            try
            {
                int Result = 0;
                //重新计算商品总数
                TemplateData.GoodProject.ItemCou = TemplateData.GoodItems.Count;
                if (string.IsNullOrEmpty(TemplateData.KEY))
                {
                    TemplateData.KEY = "C" + GenerateStringID(2);
                    TemplateData.GoodProject.SCGPID = "CG" + GenerateStringID(2);
                    Result = _dal.AddActive(TemplateData,start_up);
                }
                else
                { 
                    Result = _dal.EditActive(TemplateData,start_up);
                }
                if (Result > 0)
                {
                    //同步小程序api接口
                    try
                    {
                        var param = GetSellCombinationInfo(TemplateData.KEY.Trim());
                        string str = param.ToJson();
                        NFine.Code.Miniapp.cloud.CloudDBManage clond = new NFine.Code.Miniapp.cloud.CloudDBManage();
                        if (string.IsNullOrEmpty(TemplateData.KEY))
                            clond.add(str);
                        else
                            clond.update(str, "{\"KEY\": \"" + TemplateData.KEY.Trim() + "\"}");
                    }
                    catch(Exception ex)
                    {
                        throw new Exception(ex.Message);
                    }
                }
                return Result;
            }
            catch(Exception ex)
            {
                throw new Exception("操作过程发成异常："+ex.Message);
            }
        }
        /// <summary>
        /// 删除组合购
        /// </summary>
        /// <param name="key">组合购key</param>
        /// <returns></returns>
        public int DeleteActive(string key) 
        {
            try
            {
                return _dal.DeleteActive(key);
            }
            catch (Exception ex)
            {
                throw new Exception("删除失败：" + ex.Message);
            }
        }
      
        #endregion
        /// <summary>
        /// 生成唯一编号
        /// </summary>
        /// <param name="type">生成类型</param>
        /// <returns></returns>
        private string GenerateStringID(int type = 1)
        {
            string str = "";
            if (type == 1)
            {
                //根据guid生成唯一编号
                str = Guid.NewGuid().ToString("N");
                str = str.Substring(0, str.Length - 24).ToUpper();
            }
            else if (type == 2)
            {
                ///根据时间戳生成唯一编号
                long time = (DateTime.Now.ToUniversalTime().Ticks - 621355968000000000) / 10000000;
                str = time.ToString().Substring(4, time.ToString().Length - 4);
            }
            return str;
        }
    }
}
