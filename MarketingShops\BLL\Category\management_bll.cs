﻿using MarketingShops.DAL.Category;
using MarketingShops.Model;
using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.BLL.Category
{
   public class management_bll
    {
       private static management_dal _dal = new management_dal();
        /// <summary>
        /// 获取信息
        /// </summary>
        /// <param name="pagination">分页信息</param>
        /// <param name="tcp_name">项目名称</param>
        /// <returns></returns>
       public PageResultData GetGridInfo(Pagination pagination, string tcp_name, string tcc_name)
        {
           
            return _dal.GetGridInfo(tcp_name,tcc_name, pagination);
        }
   
       /// <summary>
       /// 获取详情信息
       /// </summary>
       /// <param name="tcpi_id">关联表id</param>
       /// <returns></returns>
       public List<management> GetDetailsInfo(int tcc_id)
       {
           return _dal.GetDetailsInfo(tcc_id);
       }

      
      
       /// <summary>
       /// 新增配置
       /// </summary>
       /// <param name="obj"></param>
       /// <returns></returns>
       public int AddConfig(List<management> obj)
       {
           return _dal.AddConfig(obj);
       }
       /// <summary>
       /// 修改配置
       /// </summary>
       /// <param name="obj"></param>
       /// <returns></returns>
       public int UpdateConfig(List<management> obj)
       {
           return _dal.UpdateConfig(obj);
       }
      
       /// <summary>
       /// 删除配置信息
       /// </summary>
       /// <param name="tcc_id"></param>
       /// <returns></returns>
       public int DeleteConfig(int tcc_id)
       {
           return _dal.DeleteConfig(tcc_id);
       }
       /// <summary>
       /// 获取项目信息
       /// </summary>
       /// <returns></returns>
       public List<project> GetProjectInfo() 
       {
           return _dal.GetProjectInfo();
       }
       /// <summary>
       /// 获取栏目信息
       /// </summary>
       /// <returns></returns>
       public List<column> GetColumnInfo() 
       {
           return _dal.GetColumnInfo();
       }
       /// <summary>
       /// 获取项信息
       /// </summary>
       /// <returns></returns>
       public List<item> GetItemInfo()
       {
           return _dal.GetItemInfo();
       }

    }
}
