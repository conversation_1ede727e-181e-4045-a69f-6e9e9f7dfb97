﻿using MarketingShops.Model;
using MarketingShops.Model.Category;
using MarketingShops.Model.Classification;
using MarketingShops.Model.Goods;
using MarketingShops.Model.Goods.baseTable;
using MarketingShops.Model.Goods.enumTable;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL
{
   public class goods_dal
    { 
       #region  数据查询
        /// <summary>
        /// 查询商品信息
        /// </summary>
        /// <param name="seltj">查询条件</param>
        /// <param name="Page">页码信息</param>
        /// <returns></returns>
       public PageResultData GetGoodsInfo(goods_search_params sys, Pagination Page)
       {
           string variable = "1=1";
           if (!string.IsNullOrEmpty(sys.name))//商品名称
               variable += "and [name]='" + sys.name.Trim() + " '";
           if (!string.IsNullOrEmpty(sys.BTime) && !string.IsNullOrEmpty(sys.ETime))
           {
               sys.ETime += ":59";
               variable += " and updatetime between '" + sys.BTime.Trim() + "' and  '" + sys.ETime.Trim() + "' ";
           }
           if (sys.price1 >= 0 && sys.price2 >= 0)//价格区间
               variable += "and [sprice] between " + sys.price1 + " and " + sys.price2 + " ";
           if (sys.stock1 >= 0 && sys.stock2 >= 0)//库存区间
               variable += "and [stocknums] between " + sys.stock1 + " and " + sys.stock2 + " ";
           if (sys.sale1 >= 0 && sys.sale2 >= 0)//销售区间
               variable += "and [salesnums] between " + sys.sale1 + " and " + sys.sale2 + " ";

           string sql = "SELECT {0} FROM  dbo.goods_table_search_view where " + variable;
           return DbHelp_MarketingShops.paging<goods_dat_entity>(sql, "*", Page);

       }
       /// <summary>
       /// 商品信息
       /// </summary>
       /// <param name="seltj">查询条件</param>
       /// <returns></returns>
       public good_table GetJsonInfo(string name, int price1, int price2, string time)
       {
           string variable = "1=1";
           if (!string.IsNullOrEmpty(name))
               variable += "and name='" + name.Trim() + "' ";
           if (price1 > 0 && price2 > 0)
               variable += "and showprice between " + price1 + " and " + price2 + " ";
           if (!string.IsNullOrEmpty(time))
               variable += "and convert(varchar(10),updatetime,120)='" + time.Trim() + "' ";

           good_table table = new good_table();
           string sql = "select * from goods_table_search_view where " + variable + " order by sort desc,updatetime desc";
            table.main = DbHelp_MarketingShops.ado.GetDataList<goods_search_data>(sql);
            if (table.main.Count == 0)
            {
                sql = "select * from  goods_dat_entity where " + variable;
                table.child = DbHelp_MarketingShops.ado.GetDataList<goods_search_data>(sql);
                sql = "select * from goods_table_search_view where pid in (select pid from  goods_dat_entity where " + variable + " ) order by sort desc,updatetime desc";
                table.main = DbHelp_MarketingShops.ado.GetDataList<goods_search_data>(sql);
            }
            else
            {
                sql = "select * from  goods_dat_entity where pid in (select pid from goods_dat_main where " + variable + " and g_count>1 ) order by sort desc";
                table.child = DbHelp_MarketingShops.ado.GetDataList<goods_search_data>(sql);
            }
          
           return table;
       }
    #endregion

       /// <summary>
       /// 获取商品信息
       /// </summary>
       /// <param name="id">商品id</param>
       /// <returns></returns>
       public GetGoodModel GetGoodInfo(string id)
       {
           GetGoodModel good = new GetGoodModel();
           //商品信息
           string sql = @"select [g_id],[pid] ,[productno] ,[name] ,[title] ,[stocknums] ,[photo],[salesnums] ,[sprice],[yprice],[state],[code] from goods_dat_entity where g_id='" + id + "'";
           good.goods = DbHelp_MarketingShops.ado.GetDataSingle<goods_dat_entity>(sql);
           sql = @"select [sku_json],[desc],[m_id],[f_tcpid] ,[f_id],[scid],[gd_id] from goods_dat_child where g_id='" + id + "'";
           good.child = DbHelp_MarketingShops.ado.GetDataSingle<goods_dat_child>(sql);
           //详情信息
           sql = "select [details] ,[phototot] from goods_dat_details where gd_id='" + good.child.gd_id + "'";
           good.details = DbHelp_MarketingShops.ado.GetDataSingle<goods_dat_details>(sql);
           if (good.details.phototot > 0)//存在素材
           {
               sql = "select [dm_id],[path] ,[type] from goods_dat_details_material where g_id='" + id + "' order by type";
               good.details_material = DbHelp_MarketingShops.ado.GetDataList<goods_dat_details_material>(sql);
           }
           //sku信息
           good.sku = Json.ToObject<List<goods_dat_sku_entity>>(good.child.sku_json);
           if (good.sku != null && good.sku.Count > 0)
           {
               //支付方式
               List<goods_dat_sku_paymentMethod> payData = null;
               string InStr = "(";
               for (int i = 0; i < good.sku.Count; i++)
               {
                   if (!InStr.Contains(good.sku[i].sku_id))//去掉重复的輸入值
                   {
                       InStr += "'" + good.sku[i].sku_id + "',";
                   }
               }
               InStr = InStr.Substring(0, InStr.LastIndexOf(",")) + ")";
               sql = "SELECT [GDSPID],[sku_id],[Cash],[Integral] ,[Recharge] ,[Txt] ,[State] FROM goods_dat_sku_paymentMethod WHERE sku_id in " + InStr + "";

               payData = DbHelp_MarketingShops.ado.GetDataList<goods_dat_sku_paymentMethod>(sql);
               if (payData.Count > 0 && payData != null)
               {
                   for (int i = 0; i < good.sku.Count; i++)
                   {
                       good.sku[i].paymentMethod = payData.Where(k => k.sku_id == good.sku[i].sku_id).ToList();
                   }
               }
           }
           //spu信息
           sql = "select [spu_id] ,[spu_count] ,[spu_list_json] from goods_dat_spu_entity where g_id='" + id + "'";
           good.spu = DbHelp_MarketingShops.ado.GetDataSingle<goods_dat_spu_entity>(sql);

           if (good.spu!=null)
                good.spulist = Json.ToObject<List<goods_dat_spu_entity_list>>(good.spu.spu_list_json);
           //有效管理信息
           sql = @"select [gvm_id],[on_sale_time] ,[in_stock_time],[start_sale_time],[end_sale_time] ,[start_validity_time] ,[end_validity_time] ,[vilidity_model_no] from goods_manage_valid where g_id='" + id + "'";
           good.managevalid = DbHelp_MarketingShops.ado.GetDataSingle<goods_manage_valid>(sql);
           //物流信息
           sql = "select freightno from goods_logistics where g_id='" + id + "'";
           good.logistics = DbHelp_MarketingShops.ado.GetDataSingle<goods_logistics>(sql);
          
           
           return good;
       }

//       public List<goods_dat_sku_paymentMethod> GetPayMenthon(string SKUID)
//       {
//           return DbHelp_MarketingShops.ado.GetDataList<goods_dat_sku_paymentMethod>(@"SELECT [GDSPID]
//                  ,[Cash]
//                  ,[Integral]
//                  ,[Recharge]
//                  ,[Txt]
//                  ,[State]
//              FROM [MarketingShops].[dbo].[goods_dat_sku_paymentMethod] where [sku_id]=@sku_id", new { sku_id = SKUID });
//       }
       /// <summary>
       /// 获取对应分类的栏目信息
       /// </summary>
       /// <param name="f_id">分类编号</param>
       /// <returns>对应栏目数据集合</returns>
       public templatedata_model GetGoodsNext(string f_id) 
       {
           //找到对应项目
           goods_classification project = DbHelp_MarketingShops.ado.GetDataSingle<goods_classification>("select * from dbo.goods_classification where f_id='" + f_id + "'");
           if (project != null)
           {
               //配置信息
               List<good_item> ItemInfo = null;
               List<item_dictionary> DictionaryInfo = null;
               List<management> ConfigData = DbHelp_MarketingShops.ado.GetDataList<management>("select * from dbo.template_category_view where tcp_id='" + project.F_TcpId + "'").GroupBy(i => i.tcc_id).Select(r => r.First()).ToList();
               if (ConfigData != null)
               {
                   //获取项信息（颜色、尺寸、大小……）
                   ItemInfo = DbHelp_MarketingShops.ado.GetDataList<good_item>("select a.*,b.tcc_id from dbo.template_category_item a join dbo.template_category_view b on a.tci_id=b.tci_id where tcp_id='" + project.F_TcpId + "'");
                   //项内容
                   if(ItemInfo.Count>0)
                        DictionaryInfo = DbHelp_MarketingShops.ado.GetDataList<item_dictionary>("select * from dbo.template_category_dictionary where tci_id in(" + string.Join(",", ItemInfo.Select(i => i.tci_id).ToArray()) + ")");
               }
               return new templatedata_model { 
                   ConfigData=ConfigData,
                   ItemInfo= ItemInfo,
                   DictionaryInfo= DictionaryInfo 
               };
           }
           return null;
       }
       /// <summary>
       /// 获取Enum的值
       /// </summary>
       /// <typeparam name="T"></typeparam>
       /// <param name="tablename">表名</param>
       /// <returns></returns>
       public List<T> GetEnumData<T>(string tablename) 
       {
           return DbHelp_MarketingShops.ado.GetDataList<T>("select * from " + tablename);
       }

       /// <summary>
       /// 获取栏目数据
       /// </summary>
       /// <returns></returns>
       public List<good_enum_column> GetColoumData()
       {
           return DbHelp_MarketingShops.ado.GetDataList<good_enum_column>(" select F_Id,F_ParentId,F_FullName from dbo.goods_classification ");
          
       }
      /// <summary>
       /// 修改商品
      /// </summary>
      /// <param name="goodbase">商品信息</param>
      /// <param name="keyValue">商品编号</param>
      /// <returns></returns>
       public int EditGoodInfo(EditGoodModel model, string keyValue)
       {
           string sql = GetUpdatesql(model, keyValue);
           var data = new
           {
               Json = model.ToJson(),
               //商品主表
               g_id = keyValue,
               productno = model.goods.productno,
               name = model.goods.name,
               title = model.goods.title,
               pid = model.goods.pid,
               stocknums = model.goods.salesnums,
               photo = model.goods.photo,
               salesnums = model.goods.salesnums,
               yprice = model.goods.yprice,
               sprice = model.goods.sprice,
               state = model.goods.state,
               sort = 0,
               code = model.goods.code,
               //商品字表
               sku_json = model.child.sku_json,
               desc = model.child.desc,
               m_id = model.child.m_id,
               f_tcpid = model.child.f_tcpid,
               f_id = model.child.f_id,
               scid = model.child.scid,
               gd_id = model.child.gd_id,
               //详情
               details = model.details.details,
               phototot = model.details.phototot,
               //spu
               spu_id = model.spu != null ? model.spu.spu_id : "",
               spu_count = model.spulist != null ? model.spulist.Count : 0,
               spu_list_json = model.spulist != null ? model.spulist.ToJson() : "",
               //有效管理
               on_sale_time = model.managevalid.on_sale_time,
               in_stock_time = model.managevalid.in_stock_time,
               start_sale_time = model.managevalid.start_sale_time,
               end_sale_time = model.managevalid.end_sale_time,
               start_validity_time = model.managevalid.start_validity_time,
               end_validity_time = model.managevalid.end_validity_time,
               vilidity_model_no = model.managevalid.vilidity_model_no,
               gvm_id=model.managevalid.gvm_id,
               //物流
               freightno = model.logistics != null ? model.logistics.freightno : 0
           };
           return DbHelp_MarketingShops.ado.OperationData(sql,data);
       }

      /// <summary>
       /// 添加商品信息
      /// </summary>
      /// <param name="goodbase">商品信息</param>
      /// <returns></returns>
       public int AddGoodInfo(EditGoodModel model)
       {
           string sql = GetInsertsql(model);
           var data = new {
               Json = model.ToJson(),
               //商品主表
                g_id=model.goods.g_id,
                productno=model.goods.productno,
                name=model.goods.name,
                title=model.goods.title,
                pid=model.goods.pid,
                stocknums=model.goods.salesnums,
                photo=model.goods.photo,
                salesnums=model.goods.salesnums,
                yprice=model.goods.yprice,
                sprice=model.goods.sprice,
                state = model.goods.state,
                sort = 0,
                code=model.goods.code,
                //商品字表
                sku_json = model.child.sku_json,
                desc=model.child.desc,
                m_id = model.child.m_id,
                f_tcpid = model.child.f_tcpid,
                f_id = model.child.f_id,
                scid = model.child.scid,
                gd_id =model.child.gd_id,
                //详情
                details = model.details.details,
                phototot=model.details.phototot,
                //spu
                spu_id = model.spu!=null?model.spu.spu_id:"",
                spu_count = model.spulist != null ?model.spulist.Count:0,
                spu_list_json=model.spulist != null ?model.spulist.ToJson():"",
                //有效管理
                on_sale_time=model.managevalid.on_sale_time,
                in_stock_time = model.managevalid.in_stock_time,
                start_sale_time = model.managevalid.start_sale_time,
                end_sale_time = model.managevalid.end_sale_time,
                start_validity_time = model.managevalid.start_validity_time,
                end_validity_time = model.managevalid.end_validity_time,
                vilidity_model_no = model.managevalid.vilidity_model_no,
                //物流
               freightno = model.logistics != null ? model.logistics.freightno : 0
            };
           return DbHelp_MarketingShops.ado.OperationData(sql,data);
       }

       /// <summary>
       /// 删除规格
       /// </summary>
       /// <param name="sku_id">规格编号</param>
       /// <param name="g_id">商品编号</param>
       /// <param name="jsonlist">sku_json</param>
       /// <returns></returns>
       public int DeleteSKUInfo(string sku_id, string g_id, string jsonlist) 
       {
           goods_dat_sku_entity data = DbHelp_MarketingShops.ado.GetDataSingle<goods_dat_sku_entity>("select * from goods_dat_sku_entity where sku_id=@sku_id");
           if (data != null)
           {
               StringBuilder sb = new StringBuilder();
               var userinfo = OperatorProvider.Provider.GetCurrent();
               sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('delete','goods_dat_sku_entity', getdate(),@UserCode,@UserName,@Json,@sku_id,@name)");

               #region 开始进行数据库操作
               sb.Append(" SET XACT_ABORT OFF  BEGIN TRAN ");
               sb.Append(" update goods_dat_sku_entity set isdelete=1 where sku_id= @sku_id");
               sb.Append(" update goods_dat_child set sku_json=@jsonlist where g_id=@g_id");
               sb.Append(" COMMIT TRAN");
               #endregion
               return DbHelp_MarketingShops.ado.OperationData(sb.ToString(), new {
                   jsonlist = jsonlist,
                   g_id = g_id,
                   sku_id = sku_id,
                   Json=data.ToJson(),
                   name=data.name,
                   UserCode=userinfo.UserCode,
                   UserName = userinfo.UserName
               });
           }
           else
               return 0;
       }

       #region  SQL语句
       /// <summary>
       /// 新增语句
       /// </summary>
       /// <param name="goodbase">商品信息</param>
       /// <returns>SQL语句</returns>
       string GetInsertsql(EditGoodModel goodbase)
       {
           try
           {
               StringBuilder sb = new StringBuilder();
               var userinfo = OperatorProvider.Provider.GetCurrent();
               if (userinfo == null) throw new Exception("系统登录已超时,请重新登录"); 
               sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('insert','goods_dat_entity', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "',@Json,@g_id,@name)");
               #region 开始进行数据库操作
               sb.Append(@" if not exists(select * from [MarketingShops].[dbo].[goods_dat_entity] where g_id=@g_id) begin");
               sb.Append(" SET XACT_ABORT OFF  BEGIN TRAN ");
               //商品信息
               sb.Append(@"INSERT INTO [MarketingShops].[dbo].[goods_dat_entity]
           ([g_id]
           ,[pid]
           ,[productno]
           ,[name]
           ,[title]
           ,[stocknums]
           ,[photo]
           ,[salesnums]
           ,[sprice]
           ,[yprice]
           ,[state]
           ,[sort]
           ,[code])
     VALUES
           (@g_id
           ,@pid
           ,@productno
           ,@name
           ,@title
           ,@stocknums
           ,@photo
           ,@salesnums
           ,@sprice
           ,@yprice
           ,@state
           ,@sort
           ,@code)");
               sb.Append(@"INSERT INTO [MarketingShops].[dbo].[goods_dat_child]
           ([g_id]
           ,[sku_json]
           ,[desc]
           ,[m_id]
           ,[f_tcpid]
           ,[f_id]
           ,[scid]
           ,[gd_id])
     VALUES
           (@g_id
           ,@sku_json
           ,@desc
           ,@m_id
           ,@f_tcpid
           ,@f_id
           ,@scid
           ,@gd_id)");
               // 详情
               sb.Append(@"INSERT INTO [MarketingShops].[dbo].[goods_dat_details]
           ([gd_id]
           ,[details]
           ,[phototot])
     VALUES
           (@gd_id
           ,@details
           ,@phototot)");
               // 详情素材
               if (goodbase.details_material != null)
               {
                   for (int i = 0; i < goodbase.details_material.Count; i++)
                   {
                       sb.Append(@"INSERT INTO goods_dat_details_material ([g_id] ,[gd_id],[path],[type],[sort]) VALUES(@g_id,@gd_id,'" + goodbase.details_material[i].path + "','" + goodbase.details_material[i].type + "'," + i + ")");
                   }
               }
               //sku信息
               for (int i = 0; i < goodbase.sku.Count; i++)
               {
                   sb.Append(@"INSERT INTO goods_dat_sku_entity ([sku_id],[g_id],[name],[stock],[photo],[yprice],[sprice],[sku_list_json],[sort],[code],[weight],[paynum])
VALUES('" + goodbase.sku[i].sku_id + "','" + goodbase.goods.g_id + "' ,'" + goodbase.sku[i].name + "'," + goodbase.sku[i].stock + " ,'" + goodbase.sku[i].photo + "'," + goodbase.sku[i].yprice + "," + goodbase.sku[i].sprice + ",'" + goodbase.sku[i].sku_list_json + "'," + i + ",'" + goodbase.sku[i].code + "'," + goodbase.sku[i].weight + "," + goodbase.sku[i].paynum + ")");
                   //支付方式
                   if (goodbase.sku[i].paymentMethod != null)
                   {
                       for (int j = 0; j < goodbase.sku[i].paymentMethod.Count; j++)
                       {
                           sb.Append(@" INSERT INTO [MarketingShops].[dbo].[goods_dat_sku_paymentMethod]
                       ([sku_id]
                       ,[Cash]
                       ,[Integral]
                       ,[Recharge]
                       ,[Txt]
                       ,[State])
                 VALUES
           ('" + goodbase.sku[i].sku_id + "' ," + goodbase.sku[i].paymentMethod[j].cash + " ," + goodbase.sku[i].paymentMethod[j].integral + "," + goodbase.sku[i].paymentMethod[j].recharge + " ,'" + goodbase.sku[i].paymentMethod[j].txt + "' ," + goodbase.sku[i].paymentMethod[j].state + ")");

                       }
                   }
               }
               
               //spu 属性详情
               if (goodbase.spulist != null)
               {
                   for (int i = 0; i < goodbase.spulist.Count; i++)
                   {
                       sb.Append(@"INSERT INTO goods_dat_spu_entity_list ([spu_id],[tcp_id],[name],[value])VALUES('" + goodbase.spu.spu_id + "'," + goodbase.spulist[i].tcp_id + ",'" + goodbase.spulist[i].name + "','" + goodbase.spulist[i].value + "')");
                   }
                   //spu信息
                   sb.Append(@" INSERT INTO [MarketingShops].[dbo].[goods_dat_spu_entity]
           ([spu_id]
           ,[g_id]
           ,[spu_count]
           ,[spu_list_json])
     VALUES
           (@spu_id
           ,@g_id
           ,@spu_count
           ,@spu_list_json)");
               }
               // 有效管理
               sb.Append(@"INSERT INTO [MarketingShops].[dbo].[goods_manage_valid]
           ([g_id]
           ,[on_sale_time]
           ,[in_stock_time]
           ,[start_sale_time]
           ,[end_sale_time]
           ,[start_validity_time]
           ,[end_validity_time]
           ,[vilidity_model_no])
     VALUES
           (@g_id
           ,@on_sale_time
           ,@in_stock_time
           ,@start_sale_time
           ,@end_sale_time
           ,@start_validity_time
           ,@end_validity_time
           ,@vilidity_model_no)");
               //物流
               if (goodbase.child.scid == 1 && goodbase.logistics!=null)//当该商品为真实商品时，才需要添加物流信息
                   sb.Append(@" INSERT INTO [MarketingShops].[dbo].[goods_logistics] ([g_id] ,[freightno]) VALUES(@g_id,@freightno)");
                sb.Append(@" COMMIT TRAN");
                sb.Append(" end");
               #endregion
               return sb.ToString();
           }
           catch (Exception ex) 
           {
               return ex.Message;
           }
          
       }
       /// <summary>
       /// 更新语句
       /// </summary>
       /// <param name="goodbase">商品信息</param>
       /// <param name="keyValue">商品编号</param>
       /// <returns>SQL语句</returns>
       string GetUpdatesql(EditGoodModel goodbase, string keyValue) 
       {
           StringBuilder sb = new StringBuilder();
           try {
               var userinfo = OperatorProvider.Provider.GetCurrent();
               sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
               ([type] ,[table_name] ,[edittime]
               ,[userid]
               ,[username]
               ,[json],[primary],[title])
         VALUES
               ('update','goods_dat_entity', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "',@Json,@g_id,@name)");
               #region 开始更新数据
               sb.Append(" SET XACT_ABORT OFF  BEGIN TRAN ");
               sb.Append(@" update goods_dat_entity set name=@name,title=@title,yprice=@yprice,sprice=@sprice,stocknums=@stocknums,updatetime=getdate() where g_id=@g_id");
               sb.Append(@" update goods_dat_child set [desc]=@desc,[sku_json]=@sku_json ,[scid]=@scid, [m_id]=@m_id,[updatetime]=getdate() where [g_id]=@g_id");
               sb.Append(@" update goods_dat_details set details=@details,phototot=@phototot where gd_id=@gd_id");
               if (goodbase.details_material != null) 
               {
                   for (int i = 0; i < goodbase.details_material.Count; i++)
                   {
                       if (goodbase.details_material[i].type == "main")
                           sb.Append(@" update goods_dat_entity set photo='" + goodbase.details_material[i].path + "' where g_id=@g_id");
                       sb.Append(@" if exists(select * from goods_dat_details_material where dm_id=" + goodbase.details_material[i].dm_id + ")");
                       sb.Append(@" update goods_dat_details_material set path='" + goodbase.details_material[i].path + "',type='" + goodbase.details_material[i].type + "',sort=" + i + " where dm_id=" + goodbase.details_material[i].dm_id + "");
                       sb.Append(@" else ");
                       sb.Append(@" INSERT INTO goods_dat_details_material ([g_id] ,[gd_id],[path],[type],[sort]) VALUES(@g_id,@gd_id,'" + goodbase.details_material[i].path + "','" + goodbase.details_material[i].type + "'," + i + ")");
                   }
               }
              
                for (int i = 0; i < goodbase.sku.Count; i++)
                {
                    //检测当前规格是否存在（根据编号）--检测新增还是修改
                    sb.Append(@" if exists(select * from goods_dat_sku_entity where sku_id='" + goodbase.sku[i].sku_id + "')");
                    sb.Append(@" update goods_dat_sku_entity set photo='" + goodbase.sku[i].photo + "',stock=" + goodbase.sku[i].stock + ",yprice=" + goodbase.sku[i].yprice + ",sprice=" + goodbase.sku[i].sprice + ",code='" + goodbase.sku[i].code + "',sort=" + i + ",weight=" + goodbase.sku[i].weight + ",paynum=" + goodbase.sku[i].paynum + ",isdelete=0 where sku_id='" + goodbase.sku[i].sku_id + "'");
                    //检测当前规格是否存在（根据名称）--检测是否存在重复的规格名称
                    sb.Append(@" else if exists(select * from goods_dat_sku_entity where g_id='" + goodbase.goods.g_id + "' and name='" + goodbase.sku[i].name + "')");
                    sb.Append(@" update goods_dat_sku_entity set photo='" + goodbase.sku[i].photo + "',stock=" + goodbase.sku[i].stock + ",yprice=" + goodbase.sku[i].yprice + ",sprice=" + goodbase.sku[i].sprice + ",code='" + goodbase.sku[i].code + "',sort=" + i + ",weight=" + goodbase.sku[i].weight + ",paynum=" + goodbase.sku[i].paynum + ",isdelete=0 where g_id=@g_id and name='" + goodbase.sku[i].name + "' ");
                    sb.Append(@" else");
                    sb.Append(@" INSERT INTO goods_dat_sku_entity ([sku_id],[g_id],[name],[stock],[photo],[yprice],[sprice],[sku_list_json],[sort],[code],[weight],[paynum])
VALUES('" + goodbase.sku[i].sku_id + "',@g_id ,'" + goodbase.sku[i].name + "'," + goodbase.sku[i].stock + " ,'" + goodbase.sku[i].photo + "'," + goodbase.sku[i].yprice + "," + goodbase.sku[i].sprice + ",'" + goodbase.sku[i].sku_list_json + "'," + i + ",'" + goodbase.sku[i].code + "'," + goodbase.sku[i].weight + "," + goodbase.sku[i].paynum + ")");
                    if (goodbase.sku[i].paymentMethod != null)
                    {
                        for (int j = 0; j < goodbase.sku[i].paymentMethod.Count; j++)
                        {
                            if (goodbase.sku[i].paymentMethod[j].GDSPID > 0)
                                sb.Append(@"update goods_dat_sku_paymentMethod set State=" + goodbase.sku[i].paymentMethod[j].state + " where GDSPID=" + goodbase.sku[i].paymentMethod[j].GDSPID + "");
                            else
                                sb.Append(@" INSERT INTO [MarketingShops].[dbo].[goods_dat_sku_paymentMethod]
                       ([sku_id]
                       ,[Cash]
                       ,[Integral]
                       ,[Recharge]
                       ,[Txt]
                       ,[State])
                 VALUES
           ('" + goodbase.sku[i].sku_id + "' ," + goodbase.sku[i].paymentMethod[j].cash + " ," + goodbase.sku[i].paymentMethod[j].integral + "," + goodbase.sku[i].paymentMethod[j].recharge + " ,'" + goodbase.sku[i].paymentMethod[j].txt + "' ," + goodbase.sku[i].paymentMethod[j].state + ")");
                           
                        }
                    }
                }
                if (goodbase.spulist != null && goodbase.spulist.Count > 0)
                {
                    sb.Append(@" update goods_dat_spu_entity set spu_count=@spu_count,spu_list_json=@spu_list_json where spu_id=@spu_id");
                    for (int i = 0; i < goodbase.spulist.Count; i++)
                    {
                        sb.Append(@" update goods_dat_spu_entity_list set value='" + goodbase.spulist[i].value + "' where spu_id=@spu_id and name='" + goodbase.spulist[i].name + "'");
                    }
                }
                sb.Append(@" update goods_manage_valid set end_sale_time=@end_sale_time,start_sale_time=@start_sale_time,in_stock_time=@in_stock_time,on_sale_time=@on_sale_time,end_validity_time=@end_validity_time,start_validity_time=@start_validity_time,vilidity_model_no=@vilidity_model_no where gvm_id=@gvm_id");
               if (goodbase.child.scid == 2)//当该商品为虚拟商品时，删除物流信息
                   sb.Append(@" delete from goods_logistics where g_id=@g_id");
               else if (goodbase.logistics != null && goodbase.child.scid==1)
               {
                   sb.Append(@" if exists(select * from  [MarketingShops].[dbo].[goods_logistics] where g_id=@g_id)");
                   sb.Append(@" update [MarketingShops].[dbo].[goods_logistics] set [freightno]=@freightno where g_id=@g_id ");
                   sb.Append(@" else");
                   sb.Append(@" INSERT INTO [MarketingShops].[dbo].[goods_logistics] ([g_id] ,[freightno]) VALUES(@g_id,@freightno)");
               }
              
               sb.Append(@" COMMIT TRAN");
               #endregion
               return sb.ToString();

           }
           catch (Exception ex) { throw new Exception(ex.Message); }
       }

       #endregion

    }
}
