﻿using ComponentApplicationServiceInterface.Context.Response;
using NFine.Code.Service.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace NFine.Code.Service
{
    public class RPC
    {
        static PosClientService _Pos;
        static SaasClientService _SaasPos;
        public static PosClientService Pos(int storeId)
        {
            _Pos = new PosClientService(storeId);
            return _Pos;
        }

        public static SaasClientService SaasPos(int storeId)
        {
            _SaasPos = new SaasClientService(storeId);
            return _SaasPos;
        }
    }
}
