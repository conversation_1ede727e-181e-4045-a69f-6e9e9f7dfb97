﻿using MarketingShops.Model.Category;
using NFine.Code;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.Category
{
   public class dictionary_dal
    {
        /// <summary>
        /// 获取项内容详情
        /// </summary>
        /// <param name="id">项内容id</param>
        /// <returns></returns>
        public List<item_dictionary> GetItemDictionaryInfo(int id)
        {
            return DbHelp_MarketingShops.ado.GetDataList<item_dictionary>("select * from template_category_dictionary where tci_id=" + id + " order by sort desc");
        }

       /// <summary>
       /// 获取资源字典详情
       /// </summary>
       /// <param name="ikey"></param>
       /// <returns></returns>
        public item_dictionary GetDictionary(string ikey)
        {
            return DbHelp_MarketingShops.ado.GetDataSingle<item_dictionary>("select * from template_category_dictionary where ikey='" + ikey + "'");
        }

        /// <summary>
        /// 删除字典
        /// </summary>
        /// <param name="ikey">字典key</param>
        /// <returns></returns>
        public int DeletrDictionaty(string ikey)
        {
            var userinfo = OperatorProvider.Provider.GetCurrent();
            StringBuilder sb = new StringBuilder();
            item_dictionary data = GetDictionary(ikey);
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            sb.Append("delete from template_category_item where ikey='"+ikey+"' ");
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
           ([type] ,[table_name] ,[edittime]
           ,[userid]
           ,[username]
           ,[json],[primary],[title])
     VALUES
           ('delete','template_category_item', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + data.ToJson() + "'," + data.ikey + ",'" + data.val + "')");
            sb.Append(" Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }

        public int AddDictionary(List<item_dictionary> dic)
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < dic.Count; i++)
            {
                if (!string.IsNullOrEmpty(dic[i].ikey))
                {
                    sb.Append("if not exists(select * from template_category_dictionary where ikey='" + dic[i].ikey + "')");
                    sb.Append(" begin");
                    GetAddSQL(dic[i]);
                    sb.Append(" end");
                }
                else
                    GetAddSQL(dic[i]);
            }
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
        public string GetAddSQL(item_dictionary dic)
        {
            StringBuilder sb = new StringBuilder();
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            var userinfo = OperatorProvider.Provider.GetCurrent();
            sb.Append("insert into template_category_dictionary(ikey,val,tci_id,sort,enabledmark)values('D' + RIGHT(100000000 + CONVERT(bigint, ABS(CHECKSUM(NEWID()))), 5),'" + dic.val + "','" + dic.tci_id + "'," + dic.sort + ",'" + dic.enabledmark + "')");
            sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
            ([type] ,[table_name] ,[edittime]
            ,[userid]
            ,[username]
            ,[json],[primary],[title])
        VALUES
            ('insert','template_category_dictionary', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + dic.ToJson() + "'," + dic.ikey + ",'" + dic.val + "')");
            sb.Append(" Commit Tran");
            return sb.ToString();
        }

        public int UpdataDictionary(List<item_dictionary> dic,int tci_id)
        {
            var userinfo = OperatorProvider.Provider.GetCurrent();
            StringBuilder sb = new StringBuilder();
            string type = "insert";
            sb.Append("SET XACT_ABORT ON");
            sb.Append(" Begin Tran ");
            sb.Append(" declare @ikey nvarchar(50) ");
            for (int i = 0; i < dic.Count; i++)
            {
                
                dic[i].tci_id = tci_id;
                if (string.IsNullOrEmpty(dic[i].ikey))
                {
                    sb.Append(" set @ikey='D' + RIGHT(100000000 + CONVERT(bigint, ABS(CHECKSUM(NEWID()))), 5)");
                    type = "insert"; 
                }
                else {
                    sb.Append(" set @ikey='" + dic[i].ikey + "'");
                    type = "update"; 
                }
                sb.Append("if exists(select * from template_category_dictionary where ikey=@ikey)");
                sb.Append(" update template_category_dictionary set val='" + dic[i].val + "',sort=" + dic[i].sort + ",enabledmark='" + dic[i].enabledmark + "' where ikey=@ikey");
                sb.Append(" else begin");
                sb.Append(" insert into template_category_dictionary(ikey,val,tci_id,sort,enabledmark)values(@ikey,'" + dic[i].val + "'," + dic[i].tci_id + "," + dic[i].sort + ",'" + dic[i].enabledmark + "')");
                sb.Append(" update template_category_item set dicCou=dicCou+1 where tci_id=" + tci_id);//更新项内容数量
                sb.Append(" end ");
                sb.Append(@"INSERT INTO [MarketingShops].[dbo].[log_marketing_edit]
               ([type] ,[table_name] ,[edittime]
               ,[userid]
               ,[username]
               ,[json],[primary],[title])
         VALUES
               ('" + type + "','template_category_dictionary', getdate(),'" + userinfo.UserCode + "','" + userinfo.UserName + "','" + dic[i].ToJson() + "',@ikey,'" + dic[i].val + "')");
            }
            sb.Append(" Commit Tran");
            return DbHelp_MarketingShops.ado.OperationData(sb.ToString());
        }
    }
}
