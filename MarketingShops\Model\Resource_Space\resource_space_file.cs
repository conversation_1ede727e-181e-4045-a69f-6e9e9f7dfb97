﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Img_Manage
{
    public class resource_space_file
    {
       /// <summary>
       /// 自身key
       /// </summary>
       public Guid ikey { get; set; }
       /// <summary>
       /// 父级key
       /// </summary>
       public Guid? mkey { get; set; }
       /// <summary>
       /// 子项目id
       /// </summary>
       public int s_id { get; set; }
       /// <summary>
       /// 插入人
       /// </summary>
       public string inputid { get; set; }
       /// <summary>
       /// 文件名称
       /// </summary>
       public string filename { get; set; }
       /// <summary>
       /// 文件大小kb
       /// </summary>
       public int filesize { get; set; }
       /// <summary>
       /// 插入时间
       /// </summary>
       public DateTime inputtime { get; set; }
       /// <summary>
       /// 文件类型
       /// </summary>
       public int ft_id { get; set; }
        /// <summary>
        /// 文件类型名称
        /// </summary>
       public string ft_name { get; set; }
       /// <summary>
       /// 是否文件
       /// </summary>
       public bool isfile { get; set; }
       /// <summary>
       /// 子集
       /// </summary>
       public List<resource_space_file> subfileList { get; set; }
        /// <summary>
        /// 文件路径
        /// </summary>
       public string fileurl { get; set; }
        /// <summary>
        /// 是否外部链接
        /// </summary>
       public bool isouturl { get; set; }
       public string sizestr
       {
           get
           {
               return HumanReadableFilesize(filesize);
           }
       }


       /// <summary>
       /// kb
       /// </summary>
       /// <param name="size"></param>
       /// <returns></returns>
       private string HumanReadableFilesize(double size)
       {
           String[] units = new String[] { "KB", "MB", "GB", "TB", "PB" };
           double mod = 1024.0;
           int i = 0;
           while (size >= mod)
           {
               size /= mod;
               i++;
           }
           return Math.Round(size) + units[i];
       }


    }
}
