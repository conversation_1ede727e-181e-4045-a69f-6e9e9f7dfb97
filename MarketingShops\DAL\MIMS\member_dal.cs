﻿using MarketingShops.Model.trade;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.DAL.MIMS
{
    /// <summary>
    /// 会员数据管理
    /// </summary>
   public static class member_dal
    {
        /// <summary>
        /// 获取会员信息
        /// </summary>
        /// <param name="MemberKey">会员key</param>
        /// <returns>会员信息</returns>
       public static MemberInfo GetMemberInfoByKey(string MemberKey)
       {
           return DbHelp_MIMS.ado.GetDataSingle<MemberInfo>(@"SELECT [MemberRegDate]
      ,[MemberName]
      ,[MemberSex]
      ,[MemberBirthday]
      ,[MemberIDNumber]
      ,[MemberPhoneNumber]
      ,[MemberAddress]
      ,a.[MemberCardTypeNo]
      ,[PointTotal]
      ,[IntegralTotal]
      ,[RechargeTotal]
      ,[ReturnTotal]
      ,a.[Val3]
      ,a.[Val4]
      ,[RechargeTotalFrozen],[MemberCardStatus]
  FROM [dbo].[MemberInfo] a join [dbo].[MemberCardInfo] b on a.[MemberKey]=b.[MemberKey]  where a.[MemberKey]=@MemberKey order by [MemberCardRegDate] desc", new { MemberKey = MemberKey });

       }
    }
}
