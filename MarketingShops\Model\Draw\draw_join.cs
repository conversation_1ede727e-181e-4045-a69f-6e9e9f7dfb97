﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MarketingShops.Model.Draw
{

    public class draw_join:draw_join_state
    {

        /// <summary>
        /// 主键自增id
        /// </summary>
        public int j_id { get; set; }
        /// <summary>
        /// 参与名称
        /// </summary>
        public string j_name { get; set; }
        /// <summary>
        /// 参与电话
        /// </summary>
        public string j_phone { get; set; }
        /// <summary>
        /// 参与地址
        /// </summary>
        public string j_site { get; set; }
        /// <summary>
        /// 参与人openid
        /// </summary>
        public string openid { get; set; }
        /// <summary>
        /// 参与活动
        /// </summary>
        public int d_id { get; set; }
        public string d_name { get; set; }
        /// <summary>
        /// 抽取规格
        /// </summary>
        public int c_id { get; set; }
        public string c_name { get; set; }
        /// <summary>
        /// 参与时间
        /// </summary>
        public DateTime inputtime { get; set; }
        /// <summary>
        /// 参与凭证
        /// </summary>
        public string voucher { get; set; }


        

    }
}
