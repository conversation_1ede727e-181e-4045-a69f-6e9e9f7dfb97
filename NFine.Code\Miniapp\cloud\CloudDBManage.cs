﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace NFine.Code.Miniapp.cloud
{
    /// <summary>
    /// 云DB管理
    /// </summary>
    public class CloudDBManage
    {
        public string env = "cloudutils-jfdyd";
        /// <summary>
        /// 数据库
        /// </summary>
        public string db = "BasicsInfo";
        public string key = "th_miniapp_seasonscard_token";
        /// <summary>
        /// 操作类型
        /// </summary>
        enum handleType {
            /// <summary>
            /// 修改
            /// </summary>
            update,
           /// <summary>
            /// 添加
           /// </summary>
            add,
            /// <summary>
            /// 删除
            /// </summary>
            del
        }
        /// <summary>
        /// 添加
        /// </summary>
        /// <param name="param">参数</param>
        public void add(string param)
        {
            try {
                handle(param, handleType.add);
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
            
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="param">参数</param>
        /// <param name="where">条件</param>
        public void del(string param, string where)
        {
            try {
                handle(param, handleType.del, where);
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
           
        }
        /// <summary>
        /// 修改
        /// </summary>
        /// <param name="param">参数</param>
        /// <param name="where">条件</param>
        public void update(string param, string where)
        {
            try { 
                handle(param, handleType.update, where); 
            }
            catch (Exception ex) { throw new Exception(ex.Message); }
           
        }
        /// <summary>
        /// 开始同步小程序数据
        /// </summary>
        /// <param name="param">参数</param>
        /// <param name="type">同步类型</param>
        /// <param name="where">同步条件</param>
        void handle(string param,handleType type,string where ="")
        {
            string url = geturl(type, where);
            if (string.IsNullOrEmpty(url)) 
                throw new Exception("url地址获取失败");
            string result_str = HttpMethods.HttpPost(url, "data=" + HttpUtility.UrlEncode(param));
            AjaxResult ajax = result_str.ToObject<AjaxResult>();
            if (ajax.data != null)
            {
                if (!ajax.data.ToString().Contains("ok"))
                    throw new Exception("数据操作成功，小程序同步失败");
            }
        }
        /// <summary>
        /// 获取url参数
        /// </summary>
        /// <param name="type">url类型</param>
        /// <param name="where">条件</param>
        /// <returns></returns>
        string geturl(handleType type,string where="")
        {
            string url="";
            string baseParam = "?key=" + key + "&db=" + db + "&env=" + env +"&token=" + OperatorProvider.Provider.GetCurrent().token;
            if(type==handleType.add)
                url = "http://uniongroupme.tang-hui.com.cn:801/miniapp/clouddb/databaseadd" + baseParam;
            else if (type==handleType.update)
                url = "http://uniongroupme.tang-hui.com.cn:801/miniapp/clouddb/databaseupdate"+baseParam+"&where=" + where;
            return url;
        }
    }
}
